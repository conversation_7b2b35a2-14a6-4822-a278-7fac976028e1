{
	"compilerOptions": {
		"module": "Node16",
		"moduleResolution": "Node16",
		"target": "ES2022",
		"lib": [
			"ES2022"
		],
		"sourceMap": true,
		"strict": true,   /* enable all strict type-checking options */
		"esModuleInterop": true,
		"types": ["node", "vite", "vitest/globals"],
		"baseUrl": ".",
		"paths": {
			"@/*": ["./webview-ui/src/*"]
		}
		/* Additional Checks */
		// "noImplicitReturns": true, /* Report error when not all code paths in function return a value. */
		// "noFallthroughCasesInSwitch": true, /* Report errors for fallthrough cases in switch statement. */
		// "noUnusedParameters": true,  /* Report errors on unused parameters. */
	},
	"include": [
		"src/**/*",
		"vite.config.mts",
		"webview-ui/src/**/*"
	]
}

# Xyne Code - AI-First Coding Assistant

A VSCode extension that brings Cursor-like AI capabilities to your development workflow. Chat with an AI assistant, get code explanations, debug help, and generate tests - all within your familiar VSCode environment.

## ✨ Features

- **🤖 AI Chat Interface**: Clean, modern chat interface inspired by Cursor
- **💬 Intelligent Responses**: Context-aware AI responses for coding questions
- **🔍 Code Analysis**: Get explanations for complex code structures
- **🐛 Debug Assistance**: Help with troubleshooting and error resolution
- **🧪 Test Generation**: Generate comprehensive unit tests
- **📎 @Mentions**: Reference files, functions, and variables in your chat
- **⚡ Real-time Loading**: Beautiful loading states and smooth animations
- **🎨 Modern UI**: Dark theme with gradients and smooth transitions

## 🚀 Getting Started

1. Install the extension from the VSCode marketplace
2. Open the Xyne Chat panel from the activity bar
3. Start chatting with your AI coding assistant!

## 💡 Usage Examples

### Code Explanation
```
@filename.js Explain how this function works
```

### Debug Help
```
I'm getting a TypeError when calling this function. Can you help?
```

### Test Generation
```
Generate unit tests for my React component
```

## 🛠️ Development

### Prerequisites
- Node.js 18+
- VSCode 1.101.0+

### Setup
```bash
npm install
npm run watch
```

### Building
```bash
npm run build
```

## 🎨 Design Philosophy

Xyne Code is designed to feel like a natural extension of your coding workflow, with:

- **Cursor-inspired UI**: Familiar interface for Cursor users
- **Responsive Design**: Works beautifully at any sidebar width
- **Smooth Animations**: Polished interactions and transitions
- **Accessible**: Keyboard shortcuts and screen reader support

## 🔧 Architecture

- **Extension Host**: TypeScript-based VSCode extension
- **Webview UI**: React + TypeScript + Tailwind CSS
- **Build System**: Vite for fast development and optimized builds
- **Styling**: Custom design system with Cursor-like colors and components

## 📝 Changelog

### v0.0.1
- Initial release with core chat functionality
- Modern UI with Cursor-inspired design
- @mention support for code references
- Loading states and smooth animations
- Responsive layout and dark theme

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines for more details.

## 📄 License

MIT License - see LICENSE file for details.

---

**Made with ❤️ for developers who love great AI tools**

#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Simple runner script that uses tsx to run the TypeScript test runner
const runner = spawn('npx', ['tsx', 'src/write-tools/test-runner.ts', ...process.argv.slice(2)], {
  cwd: __dirname,
  stdio: 'inherit'
});

runner.on('close', (code) => {
  process.exit(code);
});

runner.on('error', (error) => {
  console.error('Failed to start evaluation runner:', error);
  process.exit(1);
});
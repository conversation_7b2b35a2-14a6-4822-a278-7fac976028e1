#!/usr/bin/env tsx

import * as path from 'path';
import { ResultsDatabase } from '../src/database/ResultsDatabase';
import { reactComponentTestCase, configFileTestCase } from '../src/test-cases/file-creation/react-component';
import { bugFixTestCase, refactorVariableNamesTestCase } from '../src/test-cases/code-modification/bug-fix';
import { importManagementTestCase, addFeatureMultiFileTestCase } from '../src/test-cases/multi-file/import-management';
import { invalidInputsTestCase, corruptedFileRecoveryTestCase } from '../src/test-cases/error-handling/invalid-inputs';
import { extractFunctionsTestCase, consolidateHooksTestCase } from '../src/test-cases/refactoring/extract-functions';

async function populateTestCases() {
  const database = new ResultsDatabase();
  await database.initialize();

  // List of all test cases that should be in the database
  const testCases = [
    reactComponentTestCase,
    configFileTestCase,
    bugFixTestCase,
    refactorVariableNamesTestCase,
    importManagementTestCase,
    addFeatureMultiFileTestCase,
    invalidInputsTestCase,
    corruptedFileRecoveryTestCase,
    extractFunctionsTestCase,
    consolidateHooksTestCase
  ];

  console.log('Populating test cases database...');
  
  for (const testCase of testCases) {
    try {
      await database.saveTestCase(testCase);
      console.log(`✅ Saved test case: ${testCase.name} (${testCase.category})`);
    } catch (error) {
      console.error(`❌ Failed to save test case ${testCase.name}:`, error);
    }
  }

  console.log(`\n🎉 Successfully populated ${testCases.length} test cases in database!`);
  console.log('📊 Dashboard should now show proper category performance metrics.');
}

populateTestCases().catch(console.error);
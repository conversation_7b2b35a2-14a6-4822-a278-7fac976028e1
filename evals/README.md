# Xyne Code Write Tools Evaluation Framework

A comprehensive evaluation system for testing AI models' ability to use Xyne Code's write tools effectively across various coding scenarios.

## Overview

This framework evaluates how well different AI models can:
- Create new files and components
- Modify existing code accurately
- Handle multi-file operations
- Recover from errors
- Refactor code effectively

## Prerequisites

- Node.js 16+ and npm
- Python 3.8+ and pip
- API keys for AI models you want to test

## Setup

### 1. <PERSON>lone and Install Dependencies

```bash
# Clone the repository
git clone <repository-url>
cd evals

# Install Node.js dependencies
npm install

# Install Python dependencies for dashboard
cd dashboard && pip install flask flask-cors && cd ..
```

### 2. Configure API Keys

Set your API keys as environment variables:

```bash
# For Anthropic Claude
export ANTHROPIC_API_KEY="your_anthropic_api_key_here"

# For OpenAI GPT
export OPENAI_API_KEY="your_openai_api_key_here"

# For Google Gemini
export GEMINI_API_KEY="your_gemini_api_key_here"
```

### 3. Configure Models

Update `config/models.json` with your model configurations:

```json
[
  {
    "id": "claude-3.5-sonnet",
    "name": "Claude 3.5 Sonnet",
    "provider": "anthropic",
    "model": "claude-3-5-sonnet-20241022",
    "maxTokens": 4096,
    "temperature": 0.1,
    "apiKey": "your_anthropic_api_key_here"
  },
  {
    "id": "gemini-pro",
    "name": "Gemini Pro",
    "provider": "gemini",
    "model": "gemini-1.5-pro",
    "maxTokens": 4096,
    "temperature": 0.1,
    "apiKey": "your_gemini_api_key_here"
  }
]
```

## Running Tests

### Basic Usage

```bash
# Run all test cases for all configured models
npm run test:all

# Run tests for a specific model
npm run test -- --model gemini-pro

# Run tests for specific categories
npm run test -- --category file-creation,bug-fixing

# Run with debug output
DEBUG=1 npm run test -- --model gemini-pro
```

### CLI Commands

The test runner (`src/write-tools/test-runner.ts`) provides these commands:

```bash
# Run evaluation with all models
npx ts-node src/write-tools/test-runner.ts

# Run with specific model
npx ts-node src/write-tools/test-runner.ts --model gemini-pro

# Run specific test cases
npx ts-node src/write-tools/test-runner.ts --test-ids bug-fix-async-await,react-component-basic

# Run with custom settings
npx ts-node src/write-tools/test-runner.ts --concurrency 2 --timeout 60000
```

## Dashboard

### Starting the Dashboard

```bash
# Start the Flask dashboard server
cd dashboard && python app.py

# Or use npm script (starts dashboard in background)
npm run dashboard
```

### Accessing the Dashboard

1. Open your browser to `http://localhost:5000`
2. View real-time evaluation metrics
3. Compare model performance
4. Analyze test results and error patterns

The dashboard will automatically refresh every 30 seconds to show the latest results.

## Test Structure

### Running Individual Test Categories

Each test category can be run independently:

```bash
# File Creation Tests
npx ts-node src/write-tools/test-runner.ts --category file-creation

# Bug Fixing Tests  
npx ts-node src/write-tools/test-runner.ts --category bug-fixing

# Multi-file Operations
npx ts-node src/write-tools/test-runner.ts --category multi-file

# Error Handling Tests
npx ts-node src/write-tools/test-runner.ts --category error-handling

# Refactoring Tests
npx ts-node src/write-tools/test-runner.ts --category refactoring
```

### Available Test Cases

Current test cases include:

**File Creation:**
- `config-file-creation`: Create configuration files
- `react-component-basic`: Create React components

**Bug Fixing:**
- `bug-fix-async-await`: Fix async/await issues
- `bug-fix-syntax-errors`: Fix syntax problems

**Multi-file Operations:**
- `add-feature-multi-file`: Add features across files
- `multi-file-import-management`: Manage imports

**Error Handling:**
- `error-handling-invalid-inputs`: Handle invalid inputs
- `error-handling-corrupted-recovery`: Recover corrupted files

**Refactoring:**
- `refactor-variable-names`: Improve variable naming
- `refactoring-extract-functions`: Extract functions
- `refactoring-consolidate-hooks`: Consolidate React hooks

## Test Categories

### 1. File Creation
Tests model ability to create new files from scratch:
- React components with proper TypeScript types
- Configuration files (JSON, YAML)
- Utility modules and functions
- Package.json and build configurations

**Example Tests:**
- `react-component-creation`: Create a TypeScript React component with props interface
- `config-file-generation`: Generate package.json and tsconfig.json files

### 2. Code Modification  
Tests ability to modify existing code:
- Bug fixes and error corrections
- Feature additions to existing components
- Code style and formatting improvements
- Import/export updates

**Example Tests:**
- `bug-fix-simple`: Fix syntax errors and logic bugs in JavaScript/TypeScript
- `feature-addition`: Add new functionality to existing React component

### 3. Multi-File Operations
Tests coordination across multiple files:
- Refactoring with import updates
- Feature implementation spanning multiple files
- Dependency management
- Cross-file consistency

**Example Tests:**
- `import-management`: Extract utility functions and update imports across files
- `add-feature-multi-file`: Add role management feature across types, services, and components

### 4. Error Handling
Tests recovery and error management:
- Invalid file operations
- Corrupted file recovery
- Graceful error handling
- Backup and restore operations

**Example Tests:**
- `invalid-inputs`: Handle file operation failures gracefully
- `corrupted-recovery`: Recover from corrupted JSON files using backups

### 5. Refactoring
Tests code improvement capabilities:
- Function extraction and organization
- Component decomposition
- Hook consolidation
- Architecture improvements

**Example Tests:**
- `extract-functions`: Extract complex logic from React components into utilities
- `consolidate-hooks`: Create custom hooks from duplicate React logic

### 6. Bug Fixing
Tests ability to identify and fix bugs:
- Logic errors in algorithms
- React state management issues
- Type errors and interface mismatches
- Performance optimization

## Metrics

The framework tracks detailed metrics:

- **Tool Usage Accuracy**: Correct tool selection rate
- **Parameter Accuracy**: Correct parameter usage rate  
- **Code Quality**: Syntax, structure, and style scores
- **Task Completion**: How much of the task was completed
- **Efficiency**: Task completion vs. steps taken

## Configuration

### Model Configuration

The system automatically creates `config/models.json` with default configurations:

```json
[
  {
    "id": "claude-3.5-sonnet",
    "name": "Claude 3.5 Sonnet", 
    "provider": "anthropic",
    "model": "claude-3-5-sonnet-20241022",
    "maxTokens": 4096,
    "temperature": 0.1,
    "apiKey": "your-api-key"
  },
  {
    "id": "gpt-4",
    "name": "GPT-4",
    "provider": "openai", 
    "model": "gpt-4",
    "maxTokens": 4096,
    "temperature": 0.1,
    "apiKey": "your-api-key"
  }
]
```

## Results and Analysis

### Viewing Results

Test results are automatically saved to `data/results.json` and can be viewed:

1. **Dashboard**: Real-time web interface at `http://localhost:5000`
2. **JSON Files**: Raw results in `data/results.json`
3. **Console Output**: Live progress during test execution

### Interpreting Scores

Each test produces scores from 0-100 based on:

- **Tool Usage Accuracy**: Correct tool selection (0-100%)
- **Parameter Accuracy**: Correct parameter usage (0-100%) 
- **Code Quality**: Syntax, structure, style (0-100%)
- **Task Completion**: How much was completed (0-100%)
- **Efficiency**: Task completion vs. steps taken (0-100%)

**Overall Score**: Weighted average of all metrics

### Performance Baseline

Based on current Gemini test results:

- **Average Score**: ~22/100 (significant room for improvement)
- **Best Category**: React component creation (67/100)
- **Improvement Areas**: Bug fixing, refactoring, multi-file operations

## Troubleshooting

### Common Setup Issues

**"No valid models found":**
```bash
# Check API key is set
echo $GEMINI_API_KEY

# Verify model config
cat config/models.json

# Test API connection
curl -H "Authorization: Bearer $GEMINI_API_KEY" \
  https://generativelanguage.googleapis.com/v1/models
```

**"Module not found" errors:**
```bash
# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Check TypeScript compilation
npx tsc --noEmit
```

**Dashboard won't start:**
```bash
# Install Python dependencies
cd dashboard
pip install flask flask-cors
python app.py

# Check port availability
lsof -i :5000
```

### Debug Mode

Enable detailed logging:

```bash
# Set debug environment variable
export DEBUG=1

# Run with verbose output
npx ts-node src/write-tools/test-runner.ts --model gemini-pro --verbose
```

### Test Failures

Common test failure reasons:

1. **API Rate Limits**: Add delays between requests
2. **Invalid Tool Calls**: Check model prompt engineering
3. **Timeout Issues**: Increase timeout values
4. **File System Issues**: Check permissions and disk space

## Dashboard

The framework includes a web dashboard for visualizing results:

```bash
# Start the dashboard server
cd dashboard && python app.py

# Or use npm script
npm run dashboard
```

The dashboard provides:
- Real-time evaluation metrics
- Model performance comparisons
- Category breakdown analysis
- Recent evaluation runs
- Common error pattern analysis

Access at: http://localhost:5000

## Complete Step-by-Step Guide

### Quick Start (5 minutes)

1. **Setup Environment**:
   ```bash
   export GEMINI_API_KEY="your_actual_gemini_api_key"
   npm install
   cd dashboard && pip install flask flask-cors && cd ..
   ```

2. **Run First Test**:
   ```bash
   npx ts-node src/write-tools/test-runner.ts --model gemini-pro
   ```

3. **View Results**:
   ```bash
   cd dashboard && python app.py &
   open http://localhost:5000
   ```

### Extended Usage

#### Running All Tests
```bash
# Test all configured models with all test cases
npx ts-node src/write-tools/test-runner.ts

# Test specific model only  
npx ts-node src/write-tools/test-runner.ts --model gemini-pro

# Test specific categories
npx ts-node src/write-tools/test-runner.ts --category bug-fixing,refactoring

# Test with custom timeout (in milliseconds)
npx ts-node src/write-tools/test-runner.ts --timeout 60000
```

#### Advanced Options
```bash
# Run with reduced concurrency (for rate limiting)
npx ts-node src/write-tools/test-runner.ts --concurrency 1

# Skip failed tests from previous runs
npx ts-node src/write-tools/test-runner.ts --skip-failed

# Save test artifacts for debugging
npx ts-node src/write-tools/test-runner.ts --save-artifacts
```

#### Continuous Monitoring
```bash
# Run tests in loop for continuous evaluation
while true; do
  npx ts-node src/write-tools/test-runner.ts --model gemini-pro
  sleep 3600  # Wait 1 hour
done
```

### Data Structure

Results in `data/results.json` include:

```json
{
  "evaluationRuns": [
    {
      "id": "unique-run-id",
      "timestamp": 1751967148115,
      "models": ["gemini-pro"],
      "testCases": ["bug-fix-async-await", "react-component-basic"],
      "status": "completed",
      "progress": {
        "total": 10,
        "completed": 10,
        "failed": 8
      }
    }
  ],
  "testResults": [
    {
      "testId": "bug-fix-async-await",
      "modelId": "gemini-pro", 
      "success": false,
      "score": 7,
      "executionTimeMs": 11750,
      "toolCalls": [...],
      "metrics": {
        "toolUsageAccuracy": 100,
        "parameterAccuracy": 0,
        "codeQuality": 0,
        "taskCompletion": 7,
        "efficiency": 80
      }
    }
  ],
  "modelConfigs": [...]
}
```

## Architecture

```
src/
├── types.ts              # TypeScript interfaces for all components
├── models/               # AI model integrations (Anthropic, OpenAI)
│   └── ModelManager.ts   # Model management and API communication
├── test-cases/          # Test case definitions by category
│   ├── file-creation/   # File creation test cases
│   ├── code-modification/ # Code modification test cases
│   ├── multi-file/      # Multi-file operation test cases
│   ├── error-handling/  # Error handling test cases
│   └── refactoring/     # Refactoring test cases
├── write-tools/         # Test execution engine
│   ├── test-runner.ts   # Main test runner with CLI
│   ├── ToolExecutor.ts  # Sandboxed tool execution
│   ├── TestValidator.ts # Test result validation
│   └── ReportGenerator.ts # Report generation
└── database/            # Results storage and analytics
    └── ResultsDatabase.ts # JSON-based database operations
```

## Adding New Test Cases

1. Create test case file in appropriate category directory
2. Define test setup, task, and validation criteria
3. Export test case with proper TypeScript types
4. Run evaluation to validate test case

Example test case structure:

```typescript
import { TestCase } from '../../types';

export const myTestCase: TestCase = {
  id: 'my-test-id',
  name: 'My Test Name',
  description: 'Detailed test description',
  category: 'file-creation',
  difficulty: 'medium',
  setup: {
    files: {
      'src/example.ts': 'initial file content'
    },
    workspace: {
      name: 'test-workspace',
      structure: ['src/', 'src/example.ts']
    },
    context: 'Additional context about the test scenario'
  },
  task: {
    instruction: 'Detailed task instructions for the AI',
    expectedTools: ['write_to_file', 'simple_replace'],
    maxSteps: 10,
    timeoutMs: 30000
  },
  validation: {
    type: 'functional',
    criteria: {
      expectedFiles: {
        'src/newFile.ts': /export.*function/
      },
      syntaxValid: true,
      maxToolCalls: 8
    }
  },
  tags: ['typescript', 'functions']
};
```

## Available Tools

The evaluation framework tests these Xyne Code tools:

- **`read_file`**: Read file contents
- **`write_to_file`**: Create or overwrite files
- **`simple_replace`**: Replace specific text in files
- **`list_files`**: List files in directories
- **`search_files`**: Search for content in files

## Validation Types

### Exact Validation
Tests for exact file content matches.

### Functional Validation  
Tests that code works correctly regardless of implementation details.

### Structural Validation
Tests for proper code structure, imports, and organization.

### Custom Validation
Uses custom validation functions for complex requirements.

## Best Practices

### Test Case Design
- Keep tasks focused and specific
- Provide clear, unambiguous instructions
- Include realistic code examples
- Test edge cases and error conditions

### Validation Criteria
- Test both positive and negative cases
- Validate syntax and functionality
- Check for proper imports and structure
- Ensure no forbidden patterns exist

### Performance Optimization
- Use appropriate timeout values
- Limit maximum tool calls
- Consider test complexity vs. execution time
- Monitor resource usage

## Troubleshooting

### Common Issues

**"No valid models found"**
- Check API keys are set correctly
- Verify model configuration in `config/models.json`
- Ensure model IDs match configuration

**"Test validation failed"**
- Review validation criteria for accuracy
- Check if generated code meets requirements
- Verify file paths and content patterns

**"Timeout errors"**
- Increase timeout values for complex tasks
- Check if task instructions are clear
- Consider breaking large tasks into smaller steps

### Debug Mode

Run with debug output:
```bash
DEBUG=1 ./run-eval.js run --models claude-3.5-sonnet

./run-eval.js run --models gemini-pro --test-ids config-file-creation

npm run debug -- analyze-all
```

## Contributing

1. Fork the repository
2. Create test cases for new scenarios
3. Add model integrations for new providers
4. Improve validation logic
5. Submit pull requests with test results


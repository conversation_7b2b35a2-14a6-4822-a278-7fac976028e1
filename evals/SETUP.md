# Setup Guide - Xyne Code Evaluation Framework

## 🚀 Quick Setup

### 1. Install Dependencies

```bash
# Install Node.js dependencies
npm install

# Install Python dependencies for dashboard
cd dashboard
pip install -r requirements.txt
cd ..
```

### 2. Get API Keys

You'll need API keys from the AI providers you want to test:

#### **Anthropic (Claude)**
1. Visit https://console.anthropic.com/
2. Create an account or sign in
3. Go to "API Keys" in the dashboard
4. Click "Create Key" 
5. Copy your key (starts with `sk-ant-api03-`)

#### **OpenAI (GPT-4, GPT-3.5)**
1. Visit https://platform.openai.com/
2. Create an account or sign in
3. Go to "API Keys" section
4. Click "Create new secret key"
5. Copy your key (starts with `sk-proj-` or `sk-`)

#### **Google Gemini**
1. Visit https://makersuite.google.com/app/apikey
2. Create an account or sign in with Google
3. Click "Create API Key"
4. Copy your key (starts with `<PERSON><PERSON>`)

### 3. Configure API Keys

Choose one of these methods:

#### **Option A: Environment Variables (Recommended)**

```bash
# Add to your shell profile (.bashrc, .zshrc, etc.)
export ANTHROPIC_API_KEY="sk-ant-api03-your-key-here"
export OPENAI_API_KEY="sk-proj-your-key-here"  
export GEMINI_API_KEY="AIza-your-key-here"

# Reload your shell
source ~/.bashrc  # or ~/.zshrc
```

#### **Option B: .env File**

```bash
# Create .env file in the evals directory
cat > .env << EOF
ANTHROPIC_API_KEY=sk-ant-api03-your-key-here
OPENAI_API_KEY=sk-proj-your-key-here
GEMINI_API_KEY=AIza-your-key-here
EOF
```

#### **Option C: Configuration File**

Edit `config/models.json` (created automatically on first run):

```json
[
  {
    "id": "claude-3.5-sonnet",
    "name": "Claude 3.5 Sonnet", 
    "provider": "anthropic",
    "model": "claude-3-5-sonnet-********",
    "maxTokens": 4096,
    "temperature": 0.1,
    "apiKey": "sk-ant-api03-your-actual-key-here"
  }
]
```

### 4. Test Your Setup

```bash
# Test with a single model  
./run-eval.js run --models gemini-pro --test-ids react-component-creation

# Or test with Claude
./run-eval.js run --models claude-3.5-sonnet --test-ids react-component-creation

# If successful, you should see:
# ✅ Loaded model: Claude 3.5 Sonnet
# 🎯 Starting evaluation run: run-xxxxx
```

## 🔧 Troubleshooting

### "No valid models found"

**Cause:** API keys not configured or invalid

**Solutions:**
1. Check your API keys are set correctly
2. Verify the keys haven't expired
3. Ensure you have credits/usage remaining
4. Test keys with a simple API call

### "Failed to load model X"

**Cause:** Invalid API key or network issues

**Check:**
```bash
# Test Anthropic key
curl -H "Authorization: Bearer $ANTHROPIC_API_KEY" \
  https://api.anthropic.com/v1/models

# Test OpenAI key  
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
  https://api.openai.com/v1/models
```

### "Rate limit exceeded"

**Cause:** Too many requests to the API

**Solutions:**
1. Reduce concurrency: `--max-concurrency 1`
2. Add delays between requests
3. Check your rate limits in the provider dashboard

## 💰 Cost Estimation

### **Typical Costs Per Evaluation Run:**

- **Claude 3.5 Sonnet**: ~$0.50-2.00 per full test suite
- **GPT-4**: ~$1.00-4.00 per full test suite  
- **GPT-3.5 Turbo**: ~$0.10-0.50 per full test suite

### **Cost Optimization Tips:**

1. **Start small**: Test with one model and a few test cases first
2. **Use cheaper models for development**: GPT-3.5 for initial testing
3. **Limit test scope**: Use `--categories` or `--test-ids` to run specific tests
4. **Monitor usage**: Check provider dashboards regularly

## 🔒 Security Best Practices

### **Protect Your API Keys:**

1. **Never commit keys to version control**
   ```bash
   # Add to .gitignore
   echo ".env" >> .gitignore
   echo "config/models.json" >> .gitignore
   ```

2. **Use environment variables in production**
3. **Rotate keys regularly**
4. **Set spending limits in provider dashboards**
5. **Use separate keys for development and production**

### **Key Permissions:**
- Anthropic: Standard API access
- OpenAI: Model access (no fine-tuning required)

## 🎯 Getting Started Examples

### **Minimal Test Run:**
```bash
# Just test file creation with Claude
./run-eval.js run \
  --models claude-3.5-sonnet \
  --categories file-creation \
  --max-concurrency 1
```

### **Compare Two Models:**
```bash
# Compare Claude vs GPT-4 on refactoring tasks
./run-eval.js run \
  --models "claude-3.5-sonnet,gpt-4" \
  --categories refactoring
```

### **Full Evaluation:**
```bash
# Run everything (will cost more!)
./run-eval.js run --save-artifacts
```

## 📊 Next Steps

1. **Run your first evaluation**
2. **Start the dashboard**: `npm run dashboard`
3. **View results**: http://localhost:5000
4. **Generate reports**: `./run-eval.js report <runId>`
5. **Add custom test cases** for your specific use cases

## 🆘 Need Help?

- Check the main [README.md](README.md) for detailed documentation
- Review existing test cases in `src/test-cases/` for examples
- Check provider documentation:
  - [Anthropic API Docs](https://docs.anthropic.com/)
  - [OpenAI API Docs](https://platform.openai.com/docs/)
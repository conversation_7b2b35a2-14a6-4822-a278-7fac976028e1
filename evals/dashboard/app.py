#!/usr/bin/env python3
"""
Simple Flask server for the Xyne Code Evaluation Dashboard.
Serves the HTML dashboard and provides API endpoints for evaluation data.
"""

import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

from flask import Flask, render_template_string, jsonify, send_from_directory, request
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# Path to the evaluation data
DATA_DIR = Path(__file__).parent.parent / "data"
RESULTS_FILE = DATA_DIR / "results.json"

def load_evaluation_data() -> Dict[str, Any]:
    """Load evaluation data from the JSON database file."""
    if not RESULTS_FILE.exists():
        return {
            "evaluationRuns": [],
            "testResults": [],
            "modelConfigs": [],
            "testCases": []
        }
    
    try:
        with open(RESULTS_FILE, 'r') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return {
            "evaluationRuns": [],
            "testResults": [],
            "modelConfigs": [],
            "testCases": []
        }

def calculate_summary_stats(data: Dict[str, Any]) -> Dict[str, Any]:
    """Calculate summary statistics from evaluation data."""
    test_results = data.get('testResults', [])
    
    if not test_results:
        return {
            "totalTests": 0,
            "successRate": 0,
            "averageScore": 0,
            "averageExecutionTime": 0
        }
    
    total_tests = len(test_results)
    successful_tests = sum(1 for result in test_results if result.get('success', False))
    success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
    
    average_score = sum(result.get('score', 0) for result in test_results) / total_tests
    average_execution_time = sum(result.get('executionTimeMs', 0) for result in test_results) / total_tests / 1000  # Convert to seconds
    
    return {
        "totalTests": total_tests,
        "successRate": round(success_rate, 1),
        "averageScore": round(average_score, 1),
        "averageExecutionTime": round(average_execution_time, 1)
    }

def calculate_model_performance(data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Calculate performance metrics by model."""
    test_results = data.get('testResults', [])
    model_configs = data.get('modelConfigs', [])
    
    # Create model name mapping
    model_names = {config['id']: config['name'] for config in model_configs}
    
    # Group results by model
    model_results = {}
    for result in test_results:
        model_id = result.get('modelId')
        if model_id not in model_results:
            model_results[model_id] = []
        model_results[model_id].append(result)
    
    # Calculate performance for each model
    performance = []
    for model_id, results in model_results.items():
        if not results:
            continue
            
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r.get('success', False))
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        average_score = sum(r.get('score', 0) for r in results) / total_tests
        
        performance.append({
            "modelId": model_id,
            "name": model_names.get(model_id, model_id),
            "score": round(average_score, 1),
            "tests": total_tests,
            "successRate": round(success_rate, 1)
        })
    
    return sorted(performance, key=lambda x: x['score'], reverse=True)

def calculate_category_performance(data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Calculate performance metrics by test category."""
    test_results = data.get('testResults', [])
    test_cases = data.get('testCases', [])
    
    # Create test case category mapping
    test_categories = {tc['id']: tc['category'] for tc in test_cases}
    
    # Group results by category
    category_results = {}
    for result in test_results:
        test_id = result.get('testId')
        category = test_categories.get(test_id, 'unknown')
        
        if category not in category_results:
            category_results[category] = []
        category_results[category].append(result)
    
    # Calculate performance for each category
    performance = []
    for category, results in category_results.items():
        if not results:
            continue
            
        average_score = sum(r.get('score', 0) for r in results) / len(results)
        
        performance.append({
            "name": category.replace('-', ' ').title(),
            "score": round(average_score, 1)
        })
    
    return sorted(performance, key=lambda x: x['score'], reverse=True)

def get_recent_runs(data: Dict[str, Any], limit: int = 10) -> List[Dict[str, Any]]:
    """Get recent evaluation runs."""
    runs = data.get('evaluationRuns', [])
    model_configs = data.get('modelConfigs', [])
    
    # Create model name mapping
    model_names = {config['id']: config['name'] for config in model_configs}
    
    # Sort by timestamp and take the most recent
    recent_runs = sorted(runs, key=lambda x: x.get('timestamp', 0), reverse=True)[:limit]
    
    # Format for display
    formatted_runs = []
    for run in recent_runs:
        # Calculate success rate for this run
        run_results = [r for r in data.get('testResults', []) if r.get('runId') == run.get('id')]
        success_rate = 0
        if run_results:
            successful = sum(1 for r in run_results if r.get('success', False))
            success_rate = (successful / len(run_results) * 100)
        
        formatted_runs.append({
            "id": run.get('id', ''),
            "date": datetime.fromtimestamp(run.get('timestamp', 0) / 1000).strftime('%Y-%m-%d %H:%M'),
            "models": [model_names.get(mid, mid) for mid in run.get('models', [])],
            "tests": len(run.get('testCases', [])),
            "successRate": round(success_rate, 1),
            "status": run.get('status', 'unknown')
        })
    
    return formatted_runs

def get_common_errors(data: Dict[str, Any], limit: int = 5) -> List[Dict[str, Any]]:
    """Get most common error patterns."""
    test_results = data.get('testResults', [])
    
    # Count error types
    error_counts = {}
    total_errors = 0
    
    for result in test_results:
        for error in result.get('errors', []):
            error_type = error.get('type', 'unknown')
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
            total_errors += 1
    
    # Create error list
    errors = []
    for error_type, count in error_counts.items():
        percentage = (count / total_errors * 100) if total_errors > 0 else 0
        
        # Create user-friendly descriptions
        descriptions = {
            'tool_error': 'Invalid tool parameters or tool execution failures',
            'syntax_error': 'Generated code contains syntax errors',
            'validation_error': 'Test validation criteria not met',
            'timeout': 'Task execution exceeded time limit',
            'runtime_error': 'Runtime errors during code execution'
        }
        
        errors.append({
            "type": error_type,
            "count": count,
            "percentage": round(percentage, 1),
            "description": descriptions.get(error_type, f"Unknown error type: {error_type}")
        })
    
    return sorted(errors, key=lambda x: x['count'], reverse=True)[:limit]

@app.route('/')
def dashboard():
    """Serve the main dashboard page."""
    with open(Path(__file__).parent / 'index.html', 'r') as f:
        return f.read()

@app.route('/api/summary')
def api_summary():
    """Get summary statistics."""
    data = load_evaluation_data()
    return jsonify(calculate_summary_stats(data))

@app.route('/api/models')
def api_models():
    """Get model performance data."""
    data = load_evaluation_data()
    return jsonify(calculate_model_performance(data))

@app.route('/api/categories')
def api_categories():
    """Get category performance data."""
    data = load_evaluation_data()
    return jsonify(calculate_category_performance(data))

@app.route('/api/runs')
def api_runs():
    """Get recent evaluation runs."""
    data = load_evaluation_data()
    limit = int(request.args.get('limit', 10))
    return jsonify(get_recent_runs(data, limit))

@app.route('/api/errors')
def api_errors():
    """Get common error patterns."""
    data = load_evaluation_data()
    limit = int(request.args.get('limit', 5))
    return jsonify(get_common_errors(data, limit))

@app.route('/api/data')
def api_data():
    """Get all dashboard data in one request."""
    data = load_evaluation_data()
    
    dashboard_data = {
        "summary": calculate_summary_stats(data),
        "modelPerformance": calculate_model_performance(data),
        "categoryPerformance": calculate_category_performance(data),
        "recentRuns": get_recent_runs(data),
        "commonErrors": get_common_errors(data)
    }
    
    return jsonify(dashboard_data)

@app.route('/health')
def health():
    """Health check endpoint."""
    return jsonify({"status": "healthy", "timestamp": datetime.now().isoformat()})

if __name__ == '__main__':
    print("🚀 Starting Xyne Code Evaluation Dashboard...")
    print(f"📊 Looking for evaluation data at: {RESULTS_FILE}")
    
    if not RESULTS_FILE.exists():
        print("⚠️  No evaluation data found. Dashboard will show empty state.")
        print("   Run some evaluations first with: npm run eval")
    
    print("🌐 Dashboard will be available at: http://localhost:5000")
    
    # Create data directory if it doesn't exist
    DATA_DIR.mkdir(exist_ok=True)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xyne Code Evaluation Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .success-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .error-card {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .performance-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-2">Xyne Code Evaluation Dashboard</h1>
            <p class="text-gray-600">Monitor AI model performance on write tool tasks</p>
        </header>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="metric-card rounded-lg shadow-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Total Tests</p>
                        <p class="text-3xl font-bold" id="total-tests">Loading...</p>
                    </div>
                    <div class="bg-white/20 rounded-full p-3">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="success-card rounded-lg shadow-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Success Rate</p>
                        <p class="text-3xl font-bold" id="success-rate">Loading...</p>
                    </div>
                    <div class="bg-white/20 rounded-full p-3">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="error-card rounded-lg shadow-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Avg Score</p>
                        <p class="text-3xl font-bold" id="avg-score">Loading...</p>
                    </div>
                    <div class="bg-white/20 rounded-full p-3">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="performance-card rounded-lg shadow-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Avg Time</p>
                        <p class="text-3xl font-bold" id="avg-time">Loading...</p>
                    </div>
                    <div class="bg-white/20 rounded-full p-3">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Model Performance Chart -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Model Performance Comparison</h2>
                <div class="h-80">
                    <canvas id="modelChart"></canvas>
                </div>
            </div>

            <!-- Category Performance Chart -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Performance by Category</h2>
                <div class="h-80">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Evaluation Runs -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Recent Evaluation Runs</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Run ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Models</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tests</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Success Rate</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        </tr>
                    </thead>
                    <tbody id="runs-table" class="bg-white divide-y divide-gray-200">
                        <tr><td colspan="6" class="text-center py-4">Loading...</td></tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Load real data from API
        let dashboardData = null;

        async function loadDashboardData() {
            try {
                const response = await fetch('/api/data');
                if (response.ok) {
                    dashboardData = await response.json();
                    return dashboardData;
                } else {
                    return getEmptyData();
                }
            } catch (error) {
                return getEmptyData();
            }
        }

        function getEmptyData() {
            return {
                summary: { totalTests: 0, successRate: 0, averageScore: 0, averageExecutionTime: 0 },
                modelPerformance: [],
                categoryPerformance: [],
                recentRuns: [],
                commonErrors: []
            };
        }

        // Update summary cards
        function updateSummaryCards(data) {
            document.getElementById('total-tests').textContent = data.summary.totalTests || 0;
            document.getElementById('success-rate').textContent = (data.summary.successRate || 0).toFixed(1) + '%';
            document.getElementById('avg-score').textContent = (data.summary.averageScore || 0).toFixed(1) + '/100';
            document.getElementById('avg-time').textContent = (data.summary.averageExecutionTime || 0).toFixed(1) + 's';
        }

        // Create model performance chart
        function createModelChart(data) {
            const ctx = document.getElementById('modelChart').getContext('2d');
            
            if (data.modelPerformance.length === 0) {
                // Show empty state
                ctx.fillStyle = '#9CA3AF';
                ctx.font = '16px sans-serif';
                ctx.textAlign = 'center';
                ctx.fillText('No model data available', ctx.canvas.width / 2, ctx.canvas.height / 2);
                return;
            }

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: data.modelPerformance.map(m => m.name),
                    datasets: [{
                        label: 'Average Score',
                        data: data.modelPerformance.map(m => m.score),
                        backgroundColor: data.modelPerformance.map((_, index) => {
                            const colors = [
                                'rgba(102, 126, 234, 0.8)',
                                'rgba(118, 75, 162, 0.8)',
                                'rgba(255, 99, 132, 0.8)',
                                'rgba(54, 162, 235, 0.8)',
                                'rgba(255, 206, 86, 0.8)'
                            ];
                            return colors[index % colors.length];
                        }),
                        borderColor: data.modelPerformance.map((_, index) => {
                            const colors = [
                                'rgba(102, 126, 234, 1)',
                                'rgba(118, 75, 162, 1)',
                                'rgba(255, 99, 132, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 206, 86, 1)'
                            ];
                            return colors[index % colors.length];
                        }),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }

        // Create category performance chart
        function createCategoryChart(data) {
            const ctx = document.getElementById('categoryChart').getContext('2d');
            
            if (data.categoryPerformance.length === 0) {
                // Show empty state
                ctx.fillStyle = '#9CA3AF';
                ctx.font = '16px sans-serif';
                ctx.textAlign = 'center';
                ctx.fillText('No category data available', ctx.canvas.width / 2, ctx.canvas.height / 2);
                return;
            }

            new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: data.categoryPerformance.map(c => c.name),
                    datasets: [{
                        label: 'Performance Score',
                        data: data.categoryPerformance.map(c => c.score),
                        backgroundColor: 'rgba(74, 172, 254, 0.2)',
                        borderColor: 'rgba(74, 172, 254, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(74, 172, 254, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }

        // Populate recent runs table
        function populateRunsTable(data) {
            const tbody = document.getElementById('runs-table');
            
            if (data.recentRuns.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center py-4 text-gray-500">No evaluation runs found</td></tr>';
                return;
            }

            tbody.innerHTML = data.recentRuns.map(run => `
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ${run.id}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${run.date}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${run.models.join(', ')}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${run.tests}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            run.successRate >= 80 ? 'bg-green-100 text-green-800' :
                            run.successRate >= 60 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                        }">
                            ${run.successRate}%
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            run.status === 'completed' ? 'bg-green-100 text-green-800' :
                            run.status === 'running' ? 'bg-blue-100 text-blue-800' :
                            'bg-red-100 text-red-800'
                        }">
                            ${run.status}
                        </span>
                    </td>
                </tr>
            `).join('');
        }

        // Initialize dashboard
        async function initDashboard() {
            const data = await loadDashboardData();
            
            updateSummaryCards(data);
            createModelChart(data);
            createCategoryChart(data);
            populateRunsTable(data);
        }

        // Auto-refresh every 30 seconds
        function setupAutoRefresh() {
            setInterval(async () => {
                await initDashboard();
            }, 30000);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', async () => {
            await initDashboard();
            setupAutoRefresh();
        });
    </script>
</body>
</html>
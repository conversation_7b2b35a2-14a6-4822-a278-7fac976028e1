# Test Debugging Guide

This guide explains how to debug failed test cases in the evaluation framework.

## Quick Start

### Analyze a specific failed test:
```bash
npm run debug -- analyze --test-id refactor-variable-names --model-id gemini-pro
```

### Analyze all recent failures:
```bash
npm run debug -- analyze-all
```

### Analyze failures from a specific run:
```bash
npm run debug -- analyze-all --run-id your-run-id-here
```

## What the Debugger Shows

### 1. **Failure Reasons**
- Specific validation errors
- Tool execution errors
- Content/syntax issues

### 2. **Validation Details**
- Which validation rules passed/failed
- Expected vs actual content
- File existence checks
- Syntax validation results

### 3. **Tool Usage Analysis**
- Expected vs actual tools used
- Missing tools
- Unexpected tool usage
- Inefficiency patterns

### 4. **Improvement Suggestions**
- Specific recommendations based on failure type
- Tool usage optimization tips
- Category-specific guidance

## Common Failure Patterns

### 1. **Refactoring Failures (like variable naming)**
**Symptoms:**
- Score: 20-30/100
- Tool calls: Many `simple_replace` calls
- Corrupted final files

**Common causes:**
- Global replacement of single letters
- Wrong order of replacements
- Not considering word boundaries

**Example fix:**
```bash
# Debug the specific test
npm run debug -- analyze --test-id refactor-variable-names --model-id gemini-pro
```

### 2. **File Creation Failures**
**Symptoms:**
- Missing expected files
- Score: 0-40/100
- "File not created" errors

**Common causes:**
- Using wrong file paths
- Syntax errors preventing file creation
- Missing required content

### 3. **Syntax Validation Failures**
**Symptoms:**
- Score: 0/100 for code quality
- "Syntax errors" in validation details
- Unmatched braces/quotes

**Common causes:**
- Incomplete replacements
- Corrupted text from global replacements
- Missing imports/exports

## Reading the Debug Output

```bash
📊 Test Analysis Report: refactor-variable-names
Model: gemini-pro | Score: 23/100

❌ Failure Reasons:
  • Forbidden content "private h:" found in src/calculator.ts
  • Required content "private history: number[]" not found

🔍 Validation Details:
  ❌ [content_check] Forbidden content "private h:" found in src/calculator.ts (0/100)
  ❌ [content_check] Required content "private history: number[]" not found (0/100)
  ✅ [syntax_check] TypeScript syntax valid in src/calculator.ts (100/100)

🔧 Tool Usage Analysis:
  Total calls: 15
  Expected tools: read_file, simple_replace
  Actual tools: simple_replace, simple_replace, write_to_file, ...
  Inefficiencies:
    • Excessive simple_replace calls (12) - consider using write_to_file for major changes
    • write_to_file after simple_replace suggests correction was needed

💡 Suggestions for Improvement:
  • For refactoring: Use targeted simple_replace operations instead of global text replacement
  • Consider the order of replacements to avoid corrupting intermediate states
  • Remove forbidden content - check validation criteria for what to avoid
```

## Advanced Debugging

### 1. **Check Conversation History**
For failed tests, the results.json includes `conversationHistory` showing:
- What the LLM was thinking
- Tool selection reasoning
- Error recovery attempts

### 2. **Examine Tool Call Sequence**
Look at the `toolCalls` array in results.json to see:
- Parameter values used
- Order of operations
- Success/failure of each call

### 3. **Compare Expected vs Actual Files**
The debugger shows:
- What content was expected
- What was actually generated
- Specific missing/forbidden patterns

## Improving Test Performance

### Based on Debug Results:

1. **High tool call counts (>10):**
   - Model is struggling with strategy
   - Consider simplifying test case
   - Or improving tool selection prompts

2. **Syntax errors:**
   - Model has trouble with precise text replacement
   - Consider using write_to_file for complex changes
   - Or providing better examples

3. **Missing required content:**
   - Model doesn't understand task requirements
   - Clarify instructions in test case
   - Or adjust validation criteria

4. **Forbidden content present:**
   - Model isn't cleaning up properly
   - Make cleanup requirements explicit
   - Or adjust what's considered forbidden

## Integration with Evaluation Framework

The debug tools work with the existing evaluation system:
- Results are pulled from the same database
- No additional setup required
- Works with any model or test case
- Provides actionable insights for improvement

Use this debugging information to:
1. **Improve prompts** in definitions.ts
2. **Adjust test cases** for better clarity
3. **Update validation criteria** if too strict/lenient
4. **Identify model-specific issues** for targeted fixes
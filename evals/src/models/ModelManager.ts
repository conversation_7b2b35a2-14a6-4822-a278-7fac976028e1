import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../types';

// Production system prompt for authentic testing - synced with definitions.ts
const SYSTEM_PROMPT = `You are an expert AI coding assistant with file system access. You execute tasks step-by-step and MUST complete ALL requirements before stopping.

CRITICAL TASK COMPLETION RULES:
1. You MUST complete EVERY requirement listed in the task instruction
2. You MUST use attempt_completion tool when you believe the task is complete
3. The attempt_completion tool will validate ALL requirements have been met
4. If attempt_completion fails validation, you MUST continue working until all requirements pass
5. NEVER stop working until attempt_completion confirms 100% task completion

AUTONOMOUS EXECUTION REQUIREMENTS:
- You are in an EVALUATION SCENARIO - work autonomously without waiting for user input
- <PERSON><PERSON><PERSON> ask for permission or confirmation - proceed with the next logical step
- NEVER stop mid-task - continue until ALL requirements are completed
- Execute tool calls immediately when you identify what needs to be done
- Treat this as a batch job that must complete all steps without human intervention

CRITICAL: When a user asks to read, find, or analyze ANY file, your response MUST be a tool call in XML format. Do NOT explain what you're going to do - just execute the tool immediately.

TOOL USE

You have access to these tools:

The following tools are available for working with files and executing commands:

1. read_file: Read the contents of a file
2. write_to_file: Write content to a file or create a new file
3. multi_edit: Perform multiple edits to a single file in one atomic operation
4. list_files: List files in a directory, optionally recursively with glob patterns
5. search_files: Search for content in files using regex patterns
6. get_file_summary: Analyze file structure using tree-sitter parsing to extract functions, classes, types, and definitions
7. execute_command: Execute terminal commands and return their output
8. hybrid_search: Query the indexed codebase using LanceDB to find relevant feature, text, code snippets, functions, or documentation
9. attempt_completion: Signal task completion and validate all requirements are met

When working with files, you can use list_files to discover files in directories, and search_files to find content within them. These tools make it easier to work with files without needing exact paths.

# Tool Use Formatting

Tool use is formatted using XML-style tags. The tool name is enclosed in opening and closing tags, and each parameter is similarly enclosed within its own set of tags. Here's the structure:

<tool_name>
<parameter1_name>value1</parameter1_name>
<parameter2_name>value2</parameter2_name>
...
</tool_name>

For example:

<read_file>
<path>src/main.js</path>
</read_file>

Always adhere to this format for the tool use to ensure proper parsing and execution.

# Tools

## read_file
Description: Request to read the contents of a file at the specified path with smart chunking for large files. Automatically limits display to 2000 lines and 2000 characters per line for optimal performance. Supports optional section reading for specific line ranges.
Parameters:
- path: (required) The relative path to the file from the workspace root
- startLine: (optional) Starting line number (1-based) for section reading
- lineCount: (optional) Number of lines to read from startLine
Response includes:
- content: File content (truncated if needed)
- truncated: Boolean indicating if file was truncated
- totalLines: Total lines in the file
- displayedLines: Number of lines shown
- suggestions: Helpful tips for accessing more content
Usage:
<read_file>
<path>File path here</path>
<startLine>100</startLine>
<lineCount>50</lineCount>
</read_file>

## write_to_file
Description: Request to write content to a file at the specified path. If the file exists, it will be overwritten with the provided content. If the file doesn't exist, it will be created. This tool will automatically create any directories needed to write the file.
Parameters:
- path: (required) The relative path to the file from the workspace root
- content: (required) The content to write to the file. The <<<EOF_FILE>>> marker at the end will be automatically removed and is only used to signal the end of content.
Usage:
<write_to_file>
<path>File path here</path>
<content>
Your file content here
<<<EOF_FILE>>>
</content>
</write_to_file>

## multi_edit
Description: Request to perform multiple edits to a single file in one atomic operation. All edits are applied sequentially, and either all succeed or none are applied. This is ideal for refactoring tasks that require multiple coordinated changes to the same file.
Parameters:
- path: (required) The relative path to the file from the workspace root
- edits: (required) Array of edit operations to perform sequentially
  - find: (required) The exact text to find and replace
  - replace: (required) The text to replace with
  - replaceAll: (optional) If true, replace all occurrences of this text (default: false)
  - wordBoundary: (optional) If true, only match whole words to prevent corruption (default: false)
  - caseSensitive: (optional) If true, perform case-sensitive matching (default: true)
Usage:
<multi_edit>
<path>File path here</path>
<edits>
[
  {"find": "old text 1", "replace": "new text 1"},
  {"find": "oldVar", "replace": "newVar", "replaceAll": true, "wordBoundary": true}
]
</edits>
</multi_edit>

## list_files
Description: Request to list files in a directory at the specified path. This can be used to view files in a directory, with optional recursive listing and pattern matching.
Parameters:
- path: (required) The relative path to the directory from the workspace root. Use "." for workspace root.
- recursive: (optional) Whether to list files recursively (include subdirectories)
- pattern: (optional) Glob pattern to filter files (e.g., "**/*.ts" for TypeScript files)
Usage:
<list_files>
<path>Directory path here</path>
<recursive>true</recursive>
<pattern>**/*.ts</pattern>
</list_files>

## search_files
Description: Request to search for content in files at the specified path using regex patterns. This is useful for finding specific code snippets, configuration settings, or any text within files.
Parameters:
- pattern: (required) The regex pattern to search for in files
- filePattern: (optional) Glob pattern to filter which files to search (e.g., "**/*.ts")
- caseSensitive: (optional) Whether the search should be case-sensitive
Usage:
<search_files>
<pattern>Regex pattern here</pattern>
<filePattern>**/*.ts</filePattern>
<caseSensitive>true</caseSensitive>
</search_files>

## get_file_summary
Description: Request to analyze a file's structure and extract key definitions using tree-sitter parsing. This tool provides a structured summary of functions, classes, interfaces, types, data types, instances, imports, and exports. Supports JavaScript (.js, .jsx), TypeScript (.ts, .tsx), Haskell (.hs), and PureScript (.purs) files.
Parameters:
- path: (required) The relative path to the file from the workspace root
Usage:
<get_file_summary>
<path>File path here</path>
</get_file_summary>

## execute_command
Description: Request to execute a terminal command and return its output. Use this to run shell commands like installing packages, building projects, running tests, checking git status, or any command-line operations.
Parameters:
- command: (required) The command to execute in the terminal
- workingDirectory: (optional) Working directory for command execution. Defaults to workspace root if not specified.
- timeout: (optional) Timeout in milliseconds. Defaults to 30000 (30 seconds).
Usage:
<execute_command>
<command>Command to execute</command>
<workingDirectory>Optional directory path</workingDirectory>
<timeout>Optional timeout in ms</timeout>
</execute_command>

## hybrid_search
Description: Request to perform hybrid search on a database containing code embeddings and returns 'code snippets', 'filePath', 'language'. This tool combines semantic similarity search with full-text and vector search to find relevant feature, text, code snippets, functions, or documentation based on a search query.
Parameters:
- query: (required) The search query to find relevant feature, text, code snippets, functions, or documentation
Usage:
<hybrid_search>
<query>Search query here</query>
</hybrid_search>

## attempt_completion
Description: MANDATORY tool to validate task completion. This tool MUST be used before considering ANY task complete. It validates that ALL task requirements have been met, all files exist, forbidden content is removed, and syntax is valid.

CRITICAL: This tool performs comprehensive validation including:
- Verifies ALL required files exist and match expected patterns
- Confirms ALL forbidden content has been removed  
- Validates syntax of all created/modified files
- Checks that ALL task requirements are completed

If validation fails, you MUST continue working to fix issues and call attempt_completion again.

Parameters:
- result: (required) Summary of what was accomplished. Must be complete and definitive, listing ALL completed requirements.
- command: (optional) Command to demonstrate the result (e.g., "npm run dev")

Usage:
<attempt_completion>
<result>Successfully completed all 5 task requirements: 1) Created utility files, 2) Refactored UserCard.tsx, 3) Refactored UserForm.tsx, 4) Removed all duplicate functions, 5) Updated all imports correctly.</result>
<command>npm test</command>
</attempt_completion>

# Tool Use Examples

## Example 1: Reading a file

<read_file>
<path>package.json</path>
</read_file>

## Example 2: Writing to a file

<write_to_file>
<path>test.txt</path>
<content>
Hello World!
This is a test file.
<<<EOF_FILE>>>
</content>
</write_to_file>

## Example 3: Simple text replacement using multi_edit

<multi_edit>
<path>src/components/Button.tsx</path>
<edits>
[
  {"find": "MATCH_PARENT", "replace": "WRAP_CONTENT", "replaceAll": true}
]
</edits>
</multi_edit>

## Example 3b: Safe variable renaming with word boundaries

<multi_edit>
<path>src/userService.ts</path>
<edits>
[
  {"find": "user", "replace": "currentUser", "replaceAll": true, "wordBoundary": true}
]
</edits>
</multi_edit>

## Example 3c: Multiple coordinated edits to one file

<multi_edit>
<path>src/components/Button.tsx</path>
<edits>
[
  {"find": "import React from 'react'", "replace": "import React, { useState } from 'react'"},
  {"find": "const Button = () => {", "replace": "const Button = () => {\n  const [loading, setLoading] = useState(false)"},
  {"find": "MATCH_PARENT", "replace": "WRAP_CONTENT", "replaceAll": true},
  {"find": "user", "replace": "currentUser", "replaceAll": true, "wordBoundary": true}
]
</edits>
</multi_edit>

## Example 4: Listing files

<list_files>
<path>src</path>
<recursive>true</recursive>
<pattern>**/*.ts</pattern>
</list_files>

## Example 5: Searching files

<search_files>
<pattern>console\.log\('.*?'\)</pattern>
<filePattern>**/*.js</filePattern>
<caseSensitive>false</caseSensitive>
</search_files>

## Example 6: Analyzing file structure

<get_file_summary>
<path>src/components/Button.tsx</path>
</get_file_summary>

## Example 7: Analyzing PureScript file

<get_file_summary>
<path>src/Main.purs</path>
</get_file_summary>

## Example 8: Executing terminal commands

<execute_command>
<command>npm install</command>
</execute_command>

## Example 9: Running commands with working directory

<execute_command>
<command>git status</command>
<workingDirectory>src</workingDirectory>
</execute_command>

## Example 10: Running tests with timeout

<execute_command>
<command>npm test</command>
<timeout>60000</timeout>
</execute_command>

## Example 11: Querying indexed codebase

<hybrid_search>
<query>function to handle user authentication</query>
</hybrid_search>

# Tool Use Guidelines

1. When users ask to read, examine, analyze, or check any file, use the read_file tool. It automatically handles large files with smart chunking.
2. When users ask to modify or update existing files, use multi_edit for targeted changes. Use write_to_file only for creating new files or major rewrites.
3. When users ask to replace specific text or do find-and-replace operations, use the multi_edit tool.
4. For file modifications: Use multi_edit for specific text changes, write_to_file only when rewriting entire files or creating new ones.
5. When users ask to list files in a directory, use the list_files tool.
6. When users ask to search for content in files, use the search_files tool.
7. When users ask to analyze, summarize, or understand the structure of a file, use the get_file_summary tool.
8. When users ask to run commands, install packages, build projects, run tests, or execute any terminal operations, use the execute_command tool.
9. When users ask to search for feature, text, code snippets, functions, or documentation in the indexed codebase, use the hybrid_search tool as it returns 'code snippets', 'filePath', 'language' as part of response.
10. **CRITICAL**: When task is complete, use attempt_completion tool to validate completion before stopping
11. NEVER ask users to paste file contents - always use the read_file tool instead.
12. Use one tool at a time per message.
13. Wait for the result of each tool use before proceeding.
14. Format tool calls using the XML format specified above.
15. For large files, read_file automatically provides chunking with metadata - use startLine and lineCount parameters for specific sections.

# EFFICIENCY AND STRATEGY GUIDELINES

**CRITICAL EFFICIENCY RULES:**

14. **Strategic Tool Selection**:
    - multi_edit: Use for 1-10 coordinated changes to the same file (atomic operation)
    - write_to_file: Use when making 10+ changes, creating files, or major restructuring
    - Plan your approach before starting - batch related changes together

15. **Refactoring Best Practices**:
    - For variable/function renaming: ALWAYS use wordBoundary=true to prevent corruption
    - Example: wordBoundary=true prevents "user" from matching "importuser"
    - Consider order of replacements to avoid corrupting intermediate states
    - Don't use global text replacement for single letters or common words
    - Read the full file context before making changes

16. **Error Prevention**:
    - Always read files first to understand context and structure
    - Check file existence before attempting operations
    - Handle missing files gracefully - provide helpful error messages
    - Validate syntax after making changes, especially for code files

17. **Quality Assurance**:
    - Ensure proper syntax, matched braces, quotes, and valid imports
    - Consider the impact of changes on the entire codebase
    - Test that your changes maintain code functionality

# CRITICAL: Tool Success Recognition

After executing any tool, you will receive a detailed, human-readable result that clearly shows what was accomplished. For example:

- When you read a file, you'll see the complete file content between triple backticks
- When you write a file, you'll see confirmation of bytes written
- When you search, you'll see all matches with line numbers
- When you execute a command, you'll see the command output and exit status

**IMPORTANT**: Once you receive a successful tool result with the information you need, DO NOT repeat the same tool call. The tool has already succeeded and provided the requested information. Move on to the next step in your task or provide your analysis based on the results you received.

**DO NOT**:
- Read the same file multiple times in one conversation unless the file has been modified
- Search for the same pattern repeatedly
- List the same directory multiple times without purpose
- Ignore successful tool results and repeat the same operation

# CAPABILITIES

1. When the user assigns you a task, the environment details will include a recursive list of all file paths within the current working directory. This provides a high-level view of the project's file structure, offering insights into how the code is organized—based on directory names, file names, and extensions (which also reveal the languages used). This overview can help you decide which files may be relevant to the task. If you need to explore files outside the current working directory, you can use the list_files tool.

# TASK EXECUTION METHODOLOGY

## CORE PRINCIPLE: Think Before Acting
For software engineering tasks, use a methodical approach: Understand → Plan → Implement → Verify

## TASK-AWARE EXECUTION RULES

**RULE 1: UNDERSTAND THE TASK FIRST**
- **Complex tasks** (refactoring, multi-file changes, bug fixes): Use search tools to understand the codebase and existing patterns
- **Simple tasks** (single file creation, basic edits): Can proceed directly to implementation
- **Never make blind changes** - always understand what you're modifying

**RULE 2: CHOOSE APPROPRIATE TOOLS BY TASK TYPE**
- **Creating new files**: Use write_to_file
- **Small targeted edits**: Use multi_edit with proper context
- **Large rewrites**: Use write_to_file to replace entire file
- **Refactoring**: Read existing files first, then extract patterns with multi_edit
- **Multi-file coordination**: Plan changes across files before implementing

**RULE 3: FOLLOW TASK-SPECIFIC WORKFLOWS**

*For REFACTORING tasks:*
1. Read all existing files mentioned in the task
2. Identify duplicate patterns and logic
3. Plan what to extract into shared utilities
4. Create extracted components first
5. Update original files to use extracted logic
6. Ensure forbidden patterns are completely removed

*For BUG FIXES:*
1. Search for the problematic code patterns
2. Understand the root cause
3. Plan the minimal fix needed
4. Implement and verify the fix

*For NEW FEATURES:*
1. Check existing codebase patterns and conventions
2. Plan integration with existing architecture
3. Implement following established patterns
4. Ensure proper imports and dependencies

**RULE 4: QUALITY AND COMPLETENESS**
- Complete ALL required files and content before stopping
- Remove ALL forbidden content
- Fix ALL syntax errors
- Ensure proper imports, exports, and dependencies
- Follow existing code style and conventions

**RULE 5: ERROR RECOVERY AND PERSISTENCE**
- When tools fail: Try alternative approaches
- When validation fails: Implement missing requirements
- When syntax errors occur: Fix them in subsequent tool calls
- Never abandon a task due to initial failures

**RULE 6: VERIFICATION AND COMPLETION**
- After major changes, check that code follows project conventions
- Ensure all required patterns are implemented
- Verify that changes don't break existing functionality
- For refactoring: Confirm all duplicate code has been extracted
- **CRITICAL**: Use attempt_completion tool to validate task completion before stopping
- Never consider a task complete without using attempt_completion

7. At the end of each user message, you'll automatically receive environment_details—a system-generated summary that provides useful context about the project's structure and environment. This data is not written by the user and should not be treated as part of their explicit request. Instead, use it as background information to help guide your decisions and better understand the project. If you rely on this context to take any action, clearly explain what you're doing and why, since the user may not be aware this information is being included.

OBJECTIVE

You accomplish a given task iteratively, breaking it down into clear steps and working through them methodically.

1. Analyze the user's task and set clear, achievable goals to accomplish it. Prioritize these goals in a logical order.
2. Use the environment information provided to you to inform your decisions and actions.
3. Work through these goals sequentially, utilizing available tools one at a time as necessary. Each goal should correspond to a distinct step in your problem-solving process. You will be informed on the work completed and what's remaining as you go.
4. Remember, you have extensive capabilities with access to a wide range of tools that can be used in powerful and clever ways as necessary to accomplish each goal. Think about which of the provided tools is the most relevant tool to accomplish the user's task. Next, go through each of the required parameters of the relevant tool and determine if the user has directly provided or given enough information to infer a value. When deciding if the parameter can be inferred, carefully consider all the context to see if it supports a specific value. If all of the required parameters are present or can be reasonably inferred, close the thinking tag and proceed with the tool use. BUT, if one of the values for a required parameter is missing, DO NOT invoke the tool (not even with fillers for the missing params) and instead, . DO NOT ask for more information on optional parameters if it is not provided.

# EXECUTION GUIDELINES

**DO:**
- Use search tools to understand complex codebases before making changes
- Read existing files when refactoring to understand current patterns
- Choose tools appropriate for the task complexity
- Complete all requirements thoroughly
- Follow established code patterns and conventions
- Persist through errors with alternative approaches

**DON'T:**
- Make blind changes without understanding context
- Stop at first error - always find workarounds
- Create generic code when you should extract specific patterns
- Leave tasks incomplete
- Use wrong tools for the task type (e.g., write_to_file for small edits)
- Ask for permission - execute the appropriate workflow for the task


====

# CRITICAL WORKFLOW EXAMPLES

**Example: Refactoring duplicate useState calls**
1. <read_file><path>src/components/ComponentA.tsx</path></read_file>
2. <read_file><path>src/components/ComponentB.tsx</path></read_file>
3. Identify common patterns: const [loading, setLoading] = useState()
4. <write_to_file><path>src/hooks/useApiData.ts</path><content>extracted hook logic</content></write_to_file>
5. <multi_edit><path>src/components/ComponentA.tsx</path><edits>[{"find": "const [loading, setLoading] = useState(false)", "replace": "const { loading } = useApiData()"}]</edits></multi_edit>
6. Continue until all duplicate patterns are removed

**Example: Multi-file feature addition**
1. <read_file><path>existing/file.ts</path></read_file> to understand current patterns
2. <write_to_file><path>new/types.ts</path><content>new type definitions</content></write_to_file>
3. <multi_edit><path>existing/file.ts</path><edits>[{"find": "old import", "replace": "new import with types"}]</edits></multi_edit>
4. Ensure all requirements are implemented

You are helpful, accurate, and always use the appropriate tools when users request file operations. When a user asks about file contents, immediately use the read_file tool rather than explaining that you cannot access files.`;

export interface ModelResponse {
  text: string;
  toolCalls: ToolCall[];
  usage?: {
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
    cost?: number;
  };
  finishReason: 'stop' | 'length' | 'tool_calls' | 'error';
}

export abstract class BaseModel {
  protected config: ModelConfig;

  constructor(config: ModelConfig) {
    this.config = config;
  }

  abstract generateResponse(
    prompt: string,
    toolResults?: ToolResult[],
    conversationHistory?: Array<{ role: 'user' | 'assistant'; content: string }>
  ): Promise<ModelResponse>;

  abstract validateConfig(): boolean;

  getName(): string {
    return this.config.name;
  }

  getId(): string {
    return this.config.id;
  }
}

export class AnthropicModel extends BaseModel {
  private client: any;

  constructor(config: ModelConfig) {
    super(config);
    this.initializeClient();
  }

  private async initializeClient() {
    if (this.config.provider !== 'anthropic') {
      throw new Error('Invalid provider for AnthropicModel');
    }

    const { Anthropic } = await import('@anthropic-ai/sdk');
    this.client = new Anthropic({
      apiKey: this.config.apiKey
    });
  }

  async generateResponse(
    prompt: string,
    toolResults?: ToolResult[],
    conversationHistory?: Array<{ role: 'user' | 'assistant'; content: string }>
  ): Promise<ModelResponse> {
    await this.initializeClient();

    const messages = [];
    
    // Add conversation history
    if (conversationHistory) {
      messages.push(...conversationHistory.map(msg => ({
        role: msg.role,
        content: msg.content
      })));
    }

    // Add current prompt
    messages.push({
      role: 'user' as const,
      content: prompt
    });

    // Add tool results if provided
    if (toolResults && toolResults.length > 0) {
      for (const result of toolResults) {
        messages.push({
          role: 'assistant' as const,
          content: `Tool ${result.id} completed with result: ${JSON.stringify(result.result)}`
        });
      }
    }

    try {
      const response = await this.client.messages.create({
        model: this.config.model,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        system: SYSTEM_PROMPT,
        messages
      });

      const toolCalls = this.extractToolCalls(response.content);

      return {
        text: response.content.map((c: any) => c.type === 'text' ? c.text : '').join(''),
        toolCalls,
        usage: {
          inputTokens: response.usage.input_tokens,
          outputTokens: response.usage.output_tokens,
          totalTokens: response.usage.input_tokens + response.usage.output_tokens,
          cost: this.calculateCost(response.usage.input_tokens, response.usage.output_tokens)
        },
        finishReason: response.stop_reason === 'end_turn' ? 'stop' : 'tool_calls'
      };
    } catch (error) {
      throw new Error(`Anthropic API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private extractToolCalls(content: any[]): ToolCall[] {
    const toolCalls: ToolCall[] = [];
    
    for (const block of content) {
      if (block.type === 'tool_use') {
        toolCalls.push({
          id: block.id,
          name: block.name,
          parameters: block.input
        });
      }
    }

    return toolCalls;
  }

  private calculateCost(inputTokens: number, outputTokens: number): number {
    // Claude 3.5 Sonnet pricing (approximate)
    const inputCostPer1k = 0.003;
    const outputCostPer1k = 0.015;
    
    return (inputTokens / 1000) * inputCostPer1k + (outputTokens / 1000) * outputCostPer1k;
  }

  validateConfig(): boolean {
    if (!this.config.apiKey || this.config.apiKey.includes('REPLACE_WITH_YOUR')) {
      console.error(`❌ Invalid API key for ${this.config.name}. Please set your ANTHROPIC_API_KEY environment variable or update config/models.json`);
      console.error(`   Get your API key from: https://console.anthropic.com/`);
      return false;
    }
    if (!this.config.model) {
      console.error(`❌ Missing model configuration for ${this.config.name}`);
      return false;
    }
    return true;
  }
}

export class GeminiModel extends BaseModel {
  private client: any;

  constructor(config: ModelConfig) {
    super(config);
    this.initializeClient();
  }

  private async initializeClient() {
    if (this.config.provider !== 'gemini') {
      throw new Error('Invalid provider for GeminiModel');
    }

    const { GoogleGenerativeAI } = await import('@google/generative-ai');
    this.client = new GoogleGenerativeAI(this.config.apiKey!);
  }

  async generateResponse(
    prompt: string,
    toolResults?: ToolResult[],
    conversationHistory?: Array<{ role: 'user' | 'assistant'; content: string }>
  ): Promise<ModelResponse> {
    await this.initializeClient();

    const model = this.client.getGenerativeModel({ 
      model: this.config.model,
      tools: [{
        functionDeclarations: this.getToolDefinitions()
      }]
    });

    // Build conversation history
    const history = [];
    if (conversationHistory) {
      for (const msg of conversationHistory) {
        history.push({
          role: msg.role === 'user' ? 'user' : 'model',
          parts: [{ text: msg.content }]
        });
      }
    }

    // DO NOT add tool results here - they should be handled by the test runner
    // This prevents double-handling that causes role conflicts

    try {
      if (history.length > 0) {
        // For continuing conversations, send the current prompt to continue the chat
        
        const chat = model.startChat({ history });
        const result = await chat.sendMessage(prompt);
        const response = result.response;
        
        return this.parseGeminiResponse(response);
      } else {
        // For new conversations, use the full system prompt with tools
        const model = this.client.getGenerativeModel({
          model: this.config.model,
          systemInstruction: SYSTEM_PROMPT
        });
        
        const result = await model.generateContent(prompt);
        const response = result.response;
        
        return this.parseGeminiResponse(response);
      }
    } catch (error) {
      throw new Error(`Gemini API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private parseGeminiResponse(response: any): ModelResponse {
    const text = response.text();
    const toolCalls: ToolCall[] = [];

    // First, check for native Gemini function calls
    const functionCalls = response.functionCalls();
    if (functionCalls && functionCalls.length > 0) {
      for (const call of functionCalls) {
        toolCalls.push({
          id: `call_${Math.random().toString(36).substring(2, 11)}`,
          name: call.name,
          parameters: call.args
        });
      }
    }

    // Note: With real tool integration, XML parsing is handled by the actual Xyne Code system
    // This evaluation framework now uses real tools instead of recreated parsers

    return {
      text: text || '',
      toolCalls,
      usage: {
        inputTokens: response.usageMetadata?.promptTokenCount || 0,
        outputTokens: response.usageMetadata?.candidatesTokenCount || 0,
        totalTokens: response.usageMetadata?.totalTokenCount || 0,
        cost: this.calculateCost(
          response.usageMetadata?.promptTokenCount || 0,
          response.usageMetadata?.candidatesTokenCount || 0
        )
      },
      finishReason: response.finishReason === 'STOP' ? 'stop' : 'tool_calls'
    };
  }

  private getToolDefinitions() {
    return [
      {
        name: 'read_file',
        description: 'Read the contents of a file',
        parameters: {
          type: 'object',
          properties: {
            path: { type: 'string', description: 'File path to read' }
          },
          required: ['path']
        }
      },
      {
        name: 'write_to_file',
        description: 'Write content to a file',
        parameters: {
          type: 'object',
          properties: {
            path: { type: 'string', description: 'File path to write to' },
            content: { type: 'string', description: 'Content to write' }
          },
          required: ['path', 'content']
        }
      },
      {
        name: 'multi_edit',
        description: 'Perform multiple edits to a single file atomically. CRITICAL: Use wordBoundary=true for variable/function renaming to prevent corruption',
        parameters: {
          type: 'object',
          properties: {
            path: { type: 'string', description: 'File path' },
            edits: {
              type: 'array',
              description: 'Array of edit operations',
              items: {
                type: 'object',
                properties: {
                  find: { type: 'string', description: 'Text to find' },
                  replace: { type: 'string', description: 'Text to replace with' },
                  replaceAll: { type: 'boolean', description: 'Replace all occurrences' },
                  wordBoundary: { type: 'boolean', description: 'RECOMMENDED: Use true for variable/function renaming to prevent corruption' },
                  caseSensitive: { type: 'boolean', description: 'Case sensitive matching (default: true)' }
                },
                required: ['find', 'replace']
              }
            }
          },
          required: ['path', 'edits']
        }
      },
      {
        name: 'list_files',
        description: 'List files in a directory',
        parameters: {
          type: 'object',
          properties: {
            path: { type: 'string', description: 'Directory path' },
            recursive: { type: 'boolean', description: 'List recursively' },
            pattern: { type: 'string', description: 'Glob pattern filter' }
          },
          required: ['path']
        }
      },
      {
        name: 'search_files',
        description: 'Search for content in files using regex',
        parameters: {
          type: 'object',
          properties: {
            pattern: { type: 'string', description: 'Regex pattern to search' },
            filePattern: { type: 'string', description: 'File pattern filter' },
            caseSensitive: { type: 'boolean', description: 'Case sensitive search' }
          },
          required: ['pattern']
        }
      },
      {
        name: 'get_file_summary',
        description: 'Analyze file structure and extract definitions',
        parameters: {
          type: 'object',
          properties: {
            path: { type: 'string', description: 'File path to analyze' }
          },
          required: ['path']
        }
      },
      {
        name: 'execute_command',
        description: 'Execute a terminal command',
        parameters: {
          type: 'object',
          properties: {
            command: { type: 'string', description: 'Command to execute' },
            workingDirectory: { type: 'string', description: 'Working directory' },
            timeout: { type: 'number', description: 'Timeout in milliseconds' }
          },
          required: ['command']
        }
      },
      {
        name: 'hybrid_search',
        description: 'Search the indexed codebase for relevant content',
        parameters: {
          type: 'object',
          properties: {
            query: { type: 'string', description: 'Search query' }
          },
          required: ['query']
        }
      },
      {
        name: 'attempt_completion',
        description: 'Signal task completion and validate all requirements are met. This tool MUST be used before considering any task complete.',
        parameters: {
          type: 'object',
          properties: {
            result: { type: 'string', description: 'Summary of what was accomplished' },
            command: { type: 'string', description: 'Optional command to demonstrate the result' }
          },
          required: ['result']
        }
      }
    ];
  }

  private calculateCost(inputTokens: number, outputTokens: number): number {
    // Gemini Pro pricing (approximate)
    const inputCostPer1k = 0.0005;
    const outputCostPer1k = 0.0015;
    
    return (inputTokens / 1000) * inputCostPer1k + (outputTokens / 1000) * outputCostPer1k;
  }

  validateConfig(): boolean {
    if (!this.config.apiKey || this.config.apiKey.includes('REPLACE_WITH_YOUR')) {
      console.error(`❌ Invalid API key for ${this.config.name}. Please set your GEMINI_API_KEY environment variable or update config/models.json`);
      console.error(`   Get your API key from: https://makersuite.google.com/app/apikey`);
      return false;
    }
    if (!this.config.model) {
      console.error(`❌ Missing model configuration for ${this.config.name}`);
      return false;
    }
    return true;
  }
}

export class OpenAIModel extends BaseModel {
  private client: any;

  constructor(config: ModelConfig) {
    super(config);
    this.initializeClient();
  }

  private async initializeClient() {
    if (this.config.provider !== 'openai') {
      throw new Error('Invalid provider for OpenAIModel');
    }

    const { OpenAI } = await import('openai');
    this.client = new OpenAI({
      apiKey: this.config.apiKey,
      baseURL: this.config.baseUrl
    });
  }

  async generateResponse(
    prompt: string,
    toolResults?: ToolResult[],
    conversationHistory?: Array<{ role: 'user' | 'assistant'; content: string }>
  ): Promise<ModelResponse> {
    await this.initializeClient();

    const messages = [];

    // Add system message
    messages.push({
      role: 'system' as const,
      content: SYSTEM_PROMPT
    });
    
    // Add conversation history
    if (conversationHistory) {
      messages.push(...conversationHistory.map(msg => ({
        role: msg.role,
        content: msg.content
      })));
    }

    // Add current prompt
    messages.push({
      role: 'user' as const,
      content: prompt
    });

    // Add tool results if provided
    if (toolResults && toolResults.length > 0) {
      for (const result of toolResults) {
        messages.push({
          role: 'assistant' as const,
          content: `Tool ${result.id} completed with result: ${JSON.stringify(result.result)}`
        });
      }
    }

    try {
      const response = await this.client.chat.completions.create({
        model: this.config.model,
        messages,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        tool_choice: 'auto',
        tools: this.getToolDefinitions()
      });

      const choice = response.choices[0];
      const toolCalls = choice.message.tool_calls?.map((tc: any) => ({
        id: tc.id,
        name: tc.function.name,
        parameters: JSON.parse(tc.function.arguments)
      })) || [];

      return {
        text: choice.message.content || '',
        toolCalls,
        usage: {
          inputTokens: response.usage?.prompt_tokens || 0,
          outputTokens: response.usage?.completion_tokens || 0,
          totalTokens: response.usage?.total_tokens || 0,
          cost: this.calculateCost(response.usage?.prompt_tokens || 0, response.usage?.completion_tokens || 0)
        },
        finishReason: choice.finish_reason === 'stop' ? 'stop' : 'tool_calls'
      };
    } catch (error) {
      throw new Error(`OpenAI API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private getToolDefinitions() {
    return [
      {
        type: 'function',
        function: {
          name: 'read_file',
          description: 'Read the contents of a file',
          parameters: {
            type: 'object',
            properties: {
              path: { type: 'string', description: 'File path to read' }
            },
            required: ['path']
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'write_to_file',
          description: 'Write content to a file',
          parameters: {
            type: 'object',
            properties: {
              path: { type: 'string', description: 'File path to write to' },
              content: { type: 'string', description: 'Content to write' }
            },
            required: ['path', 'content']
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'multi_edit',
          description: 'Perform multiple edits to a single file atomically. CRITICAL: Use wordBoundary=true for variable/function renaming to prevent corruption',
          parameters: {
            type: 'object',
            properties: {
              path: { type: 'string', description: 'File path' },
              edits: {
                type: 'array',
                description: 'Array of edit operations',
                items: {
                  type: 'object',
                  properties: {
                    find: { type: 'string', description: 'Text to find' },
                    replace: { type: 'string', description: 'Text to replace with' },
                    replaceAll: { type: 'boolean', description: 'Replace all occurrences' },
                    wordBoundary: { type: 'boolean', description: 'RECOMMENDED: Use true for variable/function renaming to prevent corruption' },
                    caseSensitive: { type: 'boolean', description: 'Case sensitive matching (default: true)' }
                  },
                  required: ['find', 'replace']
                }
              }
            },
            required: ['path', 'edits']
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'attempt_completion',
          description: 'Signal task completion and validate all requirements are met. This tool MUST be used before considering any task complete.',
          parameters: {
            type: 'object',
            properties: {
              result: { type: 'string', description: 'Summary of what was accomplished' },
              command: { type: 'string', description: 'Optional command to demonstrate the result' }
            },
            required: ['result']
          }
        }
      }
    ];
  }

  private calculateCost(inputTokens: number, outputTokens: number): number {
    // GPT-4 pricing (approximate)
    const inputCostPer1k = 0.03;
    const outputCostPer1k = 0.06;
    
    return (inputTokens / 1000) * inputCostPer1k + (outputTokens / 1000) * outputCostPer1k;
  }

  validateConfig(): boolean {
    if (!this.config.apiKey || this.config.apiKey.includes('REPLACE_WITH_YOUR')) {
      console.error(`❌ Invalid API key for ${this.config.name}. Please set your OPENAI_API_KEY environment variable or update config/models.json`);
      console.error(`   Get your API key from: https://platform.openai.com/api-keys`);
      return false;
    }
    if (!this.config.model) {
      console.error(`❌ Missing model configuration for ${this.config.name}`);
      return false;
    }
    return true;
  }
}

export class ModelManager {
  private models: Map<string, BaseModel> = new Map();

  addModel(config: ModelConfig): void {
    let model: BaseModel;

    switch (config.provider) {
      case 'anthropic':
        model = new AnthropicModel(config);
        break;
      case 'openai':
      case 'openrouter':
        model = new OpenAIModel(config);
        break;
      case 'gemini':
        model = new GeminiModel(config);
        break;
      default:
        throw new Error(`Unsupported provider: ${config.provider}`);
    }

    if (!model.validateConfig()) {
      throw new Error(`Invalid configuration for model: ${config.id}`);
    }

    this.models.set(config.id, model);
  }

  getModel(modelId: string): BaseModel | undefined {
    return this.models.get(modelId);
  }

  getAllModels(): BaseModel[] {
    return Array.from(this.models.values());
  }

  getModelIds(): string[] {
    return Array.from(this.models.keys());
  }

  removeModel(modelId: string): boolean {
    return this.models.delete(modelId);
  }

  clear(): void {
    this.models.clear();
  }
}
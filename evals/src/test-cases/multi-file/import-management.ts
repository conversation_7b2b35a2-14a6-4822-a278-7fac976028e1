import { TestCase } from '../../types';

export const importManagementTestCase: TestCase = {
  id: 'multi-file-import-management',
  name: 'Multi-File Import Management',
  description: 'Create a new utility module and update imports across multiple files',
  category: 'multi-file',
  difficulty: 'hard',
  setup: {
    files: {
      'src/components/UserCard.tsx': `import React from 'react';

interface User {
  id: string;
  name: string;
  email: string;
}

// Inline validation functions (should be extracted)
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function isValidName(name: string): boolean {
  return name.trim().length >= 2 && name.trim().length <= 50;
}

function formatDisplayName(name: string): string {
  return name.trim().split(' ').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).join(' ');
}

export const UserCard: React.FC<{ user: User }> = ({ user }) => {
  const isEmailValid = isValidEmail(user.email);
  const isNameValid = isValidName(user.name);
  const displayName = formatDisplayName(user.name);

  return (
    <div className="user-card">
      <h3 style={{ color: isNameValid ? 'black' : 'red' }}>
        {displayName}
      </h3>
      <p style={{ color: isEmailValid ? 'black' : 'red' }}>
        {user.email}
      </p>
    </div>
  );
};`,
      'src/components/UserForm.tsx': `import React, { useState } from 'react';

interface User {
  id: string;
  name: string;
  email: string;
}

// Duplicate validation functions (should use shared utilities)
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function isValidName(name: string): boolean {
  return name.trim().length >= 2 && name.trim().length <= 50;
}

export const UserForm: React.FC<{ onSubmit: (user: User) => void }> = ({ onSubmit }) => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isValidName(name)) {
      alert('Invalid name');
      return;
    }
    
    if (!isValidEmail(email)) {
      alert('Invalid email');
      return;
    }

    onSubmit({
      id: Math.random().toString(36),
      name,
      email
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <input 
        type="text"
        placeholder="Name"
        value={name}
        onChange={(e) => setName(e.target.value)}
      />
      <input 
        type="email"
        placeholder="Email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
      />
      <button type="submit">Submit</button>
    </form>
  );
};`,
      'src/types/User.ts': `export interface User {
  id: string;
  name: string;
  email: string;
}`
    },
    workspace: {
      name: 'user-management-app',
      structure: [
        'src/',
        'src/components/',
        'src/components/UserCard.tsx',
        'src/components/UserForm.tsx',
        'src/types/',
        'src/types/User.ts'
      ]
    },
    context: 'A React app with duplicate validation logic spread across components that needs to be consolidated'
  },
  task: {
    instruction: `AUTOMATED TESTING ENVIRONMENT: This is an autonomous evaluation. Do not wait for user confirmation or permission between steps. Execute all required tools immediately.

Refactor the codebase to eliminate duplicate validation logic and improve organization:

1. Read all the component files to understand the current structure
2. Create a new utility file at src/utils/userValidation.ts that exports:
   - isValidEmail function
   - isValidName function  
   - formatDisplayName function
3. Create a new utility file at src/utils/userHelpers.ts that exports:
   - generateUserId function (to replace Math.random().toString(36))
4. Update UserCard.tsx to:
   - Import User type from src/types/User.ts
   - Import validation and formatting functions from the new utils
   - Remove the inline duplicate functions
5. Update UserForm.tsx to:
   - Import User type from src/types/User.ts
   - Import validation functions from the new utils
   - Import generateUserId from userHelpers
   - Remove the inline duplicate functions
   - Use generateUserId instead of Math.random().toString(36)

Ensure all imports are correctly organized and there are no duplicate functions.

MANDATORY COMPLETION REQUIREMENT: You MUST use attempt_completion tool to validate that ALL 5 requirements above have been completed successfully. The validation will check for:
- Both UserCard.tsx AND UserForm.tsx are fully refactored
- All forbidden duplicate functions are removed
- All required utility files exist
- All syntax is valid

IMPORTANT: Use only read_file, write_to_file, multi_edit, and attempt_completion tools. Do not use execute_command or any terminal commands.`,
    expectedTools: ['read_file', 'read_file', 'read_file', 'write_to_file', 'write_to_file', 'multi_edit', 'attempt_completion'],
    maxSteps: 12,
    timeoutMs: 60000
  },
  validation: {
    type: 'structural',
    criteria: {
      expectedFiles: {
        'src/utils/userValidation.ts': /export.*function isValidEmail[\s\S]*export.*function isValidName[\s\S]*export.*function formatDisplayName/,
        'src/utils/userHelpers.ts': /export.*function generateUserId/,
        'src/components/UserCard.tsx': /import.*User.*from.*types\/User[\s\S]*import.*userValidation[\s\S]*(?!function isValidEmail)(?!function isValidName)(?!function formatDisplayName)/,
        'src/components/UserForm.tsx': /import.*User.*from.*types\/User[\s\S]*import.*userValidation[\s\S]*import.*generateUserId[\s\S]*(?!function isValidEmail)(?!function isValidName)/
      },
      syntaxValid: true,
      requiredContent: [
        // Utils should be created
        'src/utils/userValidation.ts',
        'src/utils/userHelpers.ts'
      ],
      forbiddenContent: [
        // No duplicate functions in components
        'function isValidEmail(email: string): boolean {',
        'function isValidName(name: string): boolean {'
      ],
      maxToolCalls: 12
    }
  },
  tags: ['multi-file', 'refactoring', 'imports', 'utilities', 'deduplication']
};

export const addFeatureMultiFileTestCase: TestCase = {
  id: 'add-feature-multi-file',
  name: 'Add Feature Across Multiple Files',
  description: 'Add user role management feature that spans multiple files and requires coordinated changes',
  category: 'multi-file',
  difficulty: 'hard',
  setup: {
    files: {
      'src/types/User.ts': `export interface User {
  id: string;
  name: string;
  email: string;
}`,
      'src/services/UserService.ts': `import { User } from '../types/User';

export class UserService {
  private users: User[] = [];

  async createUser(userData: Omit<User, 'id'>): Promise<User> {
    const user: User = {
      id: Math.random().toString(36),
      ...userData
    };
    
    this.users.push(user);
    return user;
  }

  async getUser(id: string): Promise<User | null> {
    return this.users.find(u => u.id === id) || null;
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User | null> {
    const userIndex = this.users.findIndex(u => u.id === id);
    if (userIndex === -1) return null;

    this.users[userIndex] = { ...this.users[userIndex], ...updates };
    return this.users[userIndex];
  }

  async deleteUser(id: string): Promise<boolean> {
    const userIndex = this.users.findIndex(u => u.id === id);
    if (userIndex === -1) return false;

    this.users.splice(userIndex, 1);
    return true;
  }

  async getAllUsers(): Promise<User[]> {
    return [...this.users];
  }
}`,
      'src/components/UserCard.tsx': `import React from 'react';
import { User } from '../types/User';

interface UserCardProps {
  user: User;
  onEdit?: (user: User) => void;
  onDelete?: (userId: string) => void;
}

export const UserCard: React.FC<UserCardProps> = ({ user, onEdit, onDelete }) => {
  return (
    <div className="user-card">
      <h3>{user.name}</h3>
      <p>{user.email}</p>
      <div className="user-actions">
        {onEdit && (
          <button onClick={() => onEdit(user)}>Edit</button>
        )}
        {onDelete && (
          <button onClick={() => onDelete(user.id)}>Delete</button>
        )}
      </div>
    </div>
  );
};`
    },
    workspace: {
      name: 'user-management-system',
      structure: [
        'src/',
        'src/types/',
        'src/types/User.ts',
        'src/services/',
        'src/services/UserService.ts',
        'src/components/',
        'src/components/UserCard.tsx'
      ]
    },
    context: 'A user management system that needs role-based permissions added'
  },
  task: {
    instruction: `Add role management functionality to the user system:

1. Read the existing User type and understand the current structure
2. Update src/types/User.ts to add a role field:
   - Add UserRole type with values: 'admin' | 'user' | 'viewer'
   - Update User interface to include: role: UserRole
3. Create src/types/Permission.ts with:
   - Permission enum with values: READ, WRITE, DELETE, ADMIN
   - RolePermissions type mapping roles to permissions
   - hasPermission utility function
4. Update src/services/UserService.ts to:
   - Import the new types
   - Add role parameter to createUser (default to 'user')
   - Add checkUserPermission method
   - Add getUsersByRole method
5. Update src/components/UserCard.tsx to:
   - Import new types
   - Display user role
   - Show/hide action buttons based on user permissions
   - Add role badge styling

Ensure all imports are correctly updated and the existing functionality remains intact.

IMPORTANT: Use only read_file, write_to_file, and multi_edit tools. Do not use execute_command or any terminal commands.`,
    expectedTools: ['read_file', 'write_to_file', 'multi_edit'],
    maxSteps: 10,
    timeoutMs: 60000
  },
  validation: {
    type: 'structural',
    criteria: {
      expectedFiles: {
        'src/types/User.ts': /export type UserRole.*admin.*user.*viewer[\s\S]*interface User[\s\S]*role: UserRole/,
        'src/types/Permission.ts': /enum Permission[\s\S]*READ[\s\S]*WRITE[\s\S]*DELETE[\s\S]*ADMIN[\s\S]*RolePermissions[\s\S]*hasPermission/,
        'src/services/UserService.ts': /import.*UserRole[\s\S]*import.*Permission[\s\S]*checkUserPermission[\s\S]*getUsersByRole/,
        'src/components/UserCard.tsx': /import.*UserRole[\s\S]*user\.role[\s\S]*role.*badge/
      },
      syntaxValid: true,
      requiredContent: [
        'type UserRole',
        'role: UserRole',
        'enum Permission',
        'checkUserPermission',
        'getUsersByRole',
        'user.role'
      ],
      maxToolCalls: 10
    }
  },
  tags: ['multi-file', 'feature-addition', 'types', 'permissions', 'coordination']
};
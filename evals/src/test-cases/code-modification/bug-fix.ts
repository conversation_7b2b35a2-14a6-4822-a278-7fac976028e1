import { TestCase } from '../../types';

export const bugFixTestCase: TestCase = {
  id: 'bug-fix-async-await',
  name: 'Fix Async/Await Bug',
  description: 'Fix a bug where async operations are not properly awaited, causing race conditions',
  category: 'code-modification',
  difficulty: 'medium',
  setup: {
    files: {
      'src/userService.ts': `export interface User {
  id: string;
  name: string;
  email: string;
}

class UserService {
  private users: User[] = [];

  async fetchUser(id: string): Promise<User | null> {
    // Simulate API call
    return new Promise((resolve) => {
      setTimeout(() => {
        const user = this.users.find(u => u.id === id);
        resolve(user || null);
      }, 100);
    });
  }

  async saveUser(user: User): Promise<void> {
    // Simulate API call
    return new Promise((resolve) => {
      setTimeout(() => {
        this.users.push(user);
        resolve();
      }, 50);
    });
  }

  // BUG: This function has async issues
  async processUserUpdate(id: string, updates: Partial<User>): Promise<User | null> {
    const user = this.fetchUser(id); // Missing await!
    
    if (!user) {
      return null;
    }

    const updatedUser = { ...user, ...updates };
    this.saveUser(updatedUser); // Missing await!
    
    return updatedUser;
  }

  async deleteUser(id: string): Promise<boolean> {
    const userIndex = this.users.findIndex(u => u.id === id);
    if (userIndex === -1) {
      return false;
    }
    
    this.users.splice(userIndex, 1);
    return true;
  }
}

export default UserService;`,
      'src/test.ts': `import UserService from './userService';

async function test() {
  const service = new UserService();
  
  // This will fail due to the bug
  const result = await service.processUserUpdate('123', { name: 'John Updated' });
  console.log('Result:', result);
}

test();`
    },
    workspace: {
      name: 'user-service-project',
      structure: [
        'src/',
        'src/userService.ts',
        'src/test.ts'
      ]
    },
    context: 'A user service with async operations that has race condition bugs'
  },
  task: {
    instruction: `Fix the bug in the processUserUpdate method in src/userService.ts:

1. Read the file to understand the current implementation
2. Identify the missing await keywords that are causing the race conditions
3. Fix the async/await issues in the processUserUpdate method
4. Ensure the method properly waits for async operations to complete
5. Maintain the existing function signature and behavior

The bug is that fetchUser and saveUser calls are missing await keywords, causing the function to not work correctly.

IMPORTANT: Use only read_file, write_to_file, and multi_edit tools. Do not use execute_command or any terminal commands.`,
    expectedTools: ['read_file', 'multi_edit'],
    maxSteps: 4,
    timeoutMs: 30000
  },
  validation: {
    type: 'exact',
    criteria: {
      expectedFiles: {
        'src/userService.ts': /const user = await this\.fetchUser\(id\);[\s\S]*await this\.saveUser\(updatedUser\);/
      },
      syntaxValid: true,
      requiredContent: [
        'const user = await this.fetchUser(id);',
        'await this.saveUser(updatedUser);'
      ],
      forbiddenContent: [
        'const user = this.fetchUser(id);', // without await
        'this.saveUser(updatedUser);' // without await (but not in other contexts)
      ],
      maxToolCalls: 4
    }
  },
  tags: ['bug-fix', 'async-await', 'typescript', 'race-condition']
};

export const refactorVariableNamesTestCase: TestCase = {
  id: 'refactor-variable-names',
  name: 'Refactor Variable Names',
  description: 'Refactor poorly named variables to follow better naming conventions',
  category: 'code-modification',
  difficulty: 'easy',
  setup: {
    files: {
      'src/calculator.ts': `export class Calculator {
  private h: number[] = []; // history

  public a(x: number, y: number): number { // add
    const r = x + y; // result
    this.h.push(r);
    return r;
  }

  public s(x: number, y: number): number { // subtract
    const r = x - y; // result
    this.h.push(r);
    return r;
  }

  public m(x: number, y: number): number { // multiply
    const r = x * y; // result
    this.h.push(r);
    return r;
  }

  public d(x: number, y: number): number { // divide
    if (y === 0) {
      throw new Error('Division by zero');
    }
    const r = x / y; // result
    this.h.push(r);
    return r;
  }

  public getH(): number[] { // get history
    return [...this.h];
  }

  public clearH(): void { // clear history
    this.h = [];
  }
}`
    },
    workspace: {
      name: 'calculator-project',
      structure: [
        'src/',
        'src/calculator.ts'
      ]
    },
    context: 'A calculator class with poorly named variables and methods that need refactoring'
  },
  task: {
    instruction: `Refactor the variable and method names in src/calculator.ts to use descriptive names:

1. Read the current file content
2. Replace the following poor names with descriptive ones:
   - 'h' → 'history'
   - 'a' → 'add'
   - 's' → 'subtract'  
   - 'm' → 'multiply'
   - 'd' → 'divide'
   - 'r' → 'result'
   - 'x' → 'firstNumber'
   - 'y' → 'secondNumber'
   - 'getH' → 'getHistory'
   - 'clearH' → 'clearHistory'

3. Ensure all references are updated consistently
4. Maintain the same functionality and public interface structure

IMPORTANT: Use only read_file, write_to_file, and multi_edit tools. Do not use execute_command or any terminal commands.`,
    expectedTools: ['read_file', 'multi_edit', 'attempt_completion'],
    maxSteps: 15,
    timeoutMs: 45000
  },
  validation: {
    type: 'exact',
    criteria: {
      expectedFiles: {
        'src/calculator.ts': /private history.*number\[\][\s\S]*public add\(firstNumber.*secondNumber[\s\S]*public subtract[\s\S]*public multiply[\s\S]*public divide[\s\S]*const result[\s\S]*getHistory[\s\S]*clearHistory/
      },
      syntaxValid: true,
      requiredContent: [
        'private history: number[]',
        'public add(firstNumber: number, secondNumber: number)',
        'public subtract(firstNumber: number, secondNumber: number)',
        'public multiply(firstNumber: number, secondNumber: number)',
        'public divide(firstNumber: number, secondNumber: number)',
        'const result =',
        'this.history.push(result)',
        'getHistory(): number[]',
        'clearHistory(): void'
      ],
      forbiddenContent: [
        'private h:',
        'public a(',
        'public s(',
        'public m(',
        'public d(',
        'const r =',
        'getH():',
        'clearH():'
      ],
      maxToolCalls: 15
    }
  },
  tags: ['refactoring', 'naming', 'typescript', 'code-quality']
};
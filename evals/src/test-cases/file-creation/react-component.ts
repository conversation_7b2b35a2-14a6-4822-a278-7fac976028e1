import { TestCase } from '../../types';

export const reactComponentTestCase: TestCase = {
  id: 'react-component-basic',
  name: 'Create Basic React Component',
  description: 'Create a new React functional component with props, state, and proper TypeScript types',
  category: 'file-creation',
  difficulty: 'medium',
  setup: {
    files: {
      'package.json': JSON.stringify({
        name: 'test-project',
        dependencies: {
          'react': '^18.0.0',
          '@types/react': '^18.0.0',
          'typescript': '^5.0.0'
        }
      }, null, 2),
      'tsconfig.json': JSON.stringify({
        compilerOptions: {
          target: 'ES2020',
          module: 'ESNext',
          moduleResolution: 'node',
          jsx: 'react-jsx',
          strict: true
        }
      }, null, 2),
      'src/App.tsx': `import React from 'react';

function App() {
  return (
    <div className="App">
      <h1>Hello World</h1>
    </div>
  );
}

export default App;`
    },
    workspace: {
      name: 'react-project',
      structure: [
        'package.json',
        'tsconfig.json',
        'src/',
        'src/App.tsx'
      ]
    },
    dependencies: ['react', '@types/react', 'typescript'],
    context: 'A React TypeScript project with basic setup. Need to create a reusable Button component.'
  },
  task: {
    instruction: `Create a new React component file at src/components/Button.tsx with the following requirements:

1. Create a Button component that accepts these props:
   - text (string): The button text
   - onClick (function): Click handler
   - variant (optional): 'primary' | 'secondary' | 'danger'
   - disabled (optional boolean): Whether button is disabled

2. Use proper TypeScript interfaces for props
3. Handle the variant prop to apply different CSS classes
4. Include proper export statement
5. Add basic JSDoc comments

The component should be a functional component using modern React patterns.

IMPORTANT: Use only write_to_file, attempt_completion and multi_edit tools to create the file. Do not use execute_command or any terminal commands. The write_to_file tool will automatically create any necessary parent directories.`,
    expectedTools: ['write_to_file', 'multi_edit', 'attempt_completion'],
    maxSteps: 1,
    timeoutMs: 30000
  },
  validation: {
    type: 'structural',
    criteria: {
      expectedFiles: {
        'src/components/Button.tsx': /interface.*Props.*{[\s\S]*text.*string[\s\S]*onClick[\s\S]*variant[\s\S]*function Button.*props.*ButtonProps[\s\S]*export.*Button/
      },
      syntaxValid: true,
      requiredContent: [
        'interface ButtonProps',
        'text: string',
        'onClick:',
        'variant?:',
        'function Button',
        'export default Button',
        'React.FC'
      ],
      maxToolCalls: 2
    }
  },
  tags: ['react', 'typescript', 'component', 'props', 'interfaces']
};

export const configFileTestCase: TestCase = {
  id: 'config-file-creation',
  name: 'Create Configuration File',
  description: 'Create a configuration file with environment-specific settings',
  category: 'file-creation',
  difficulty: 'easy',
  setup: {
    files: {
      'package.json': JSON.stringify({
        name: 'api-server',
        dependencies: {
          'express': '^4.18.0',
          'dotenv': '^16.0.0'
        }
      }, null, 2),
      'src/index.ts': `import express from 'express';

const app = express();

app.get('/', (req, res) => {
  res.json({ message: 'Hello World' });
});

const port = process.env.PORT || 3000;
app.listen(port, () => {
  console.log(\`Server running on port \${port}\`);
});`
    },
    workspace: {
      name: 'api-server',
      structure: [
        'package.json',
        'src/',
        'src/index.ts'
      ]
    },
    context: 'A Node.js Express API server that needs configuration management'
  },
  task: {
    instruction: `Create a configuration file at src/config/database.ts that exports database configuration for different environments:

1. Create interface DatabaseConfig with properties:
   - host: string
   - port: number  
   - username: string
   - password: string
   - database: string
   - ssl: boolean

2. Export configurations for 'development', 'test', and 'production' environments
3. Use environment variables with fallback defaults
4. Include proper TypeScript types
5. Add JSDoc comments explaining the configuration

The file should export a function getConfig() that returns the appropriate config based on NODE_ENV.

IMPORTANT: Use only write_to_file and multi_edit tools to create the file. Do not use execute_command or any terminal commands. The write_to_file tool will automatically create any necessary parent directories.`,
    expectedTools: ['write_to_file', 'multi_edit'],
    maxSteps: 1,
    timeoutMs: 20000
  },
  validation: {
    type: 'structural',
    criteria: {
      expectedFiles: {
        'src/config/database.ts': /interface DatabaseConfig[\s\S]*host.*string[\s\S]*port.*number[\s\S]*function getConfig[\s\S]*NODE_ENV[\s\S]*export/
      },
      syntaxValid: true,
      requiredContent: [
        'interface DatabaseConfig',
        'host: string',
        'port: number',
        'development',
        'production', 
        'test',
        'process.env.NODE_ENV',
        'export'
      ],
      maxToolCalls: 1
    }
  },
  tags: ['config', 'typescript', 'environment', 'database']
};
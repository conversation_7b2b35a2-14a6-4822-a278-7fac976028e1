import { TestCase } from '../../types';

export const invalidInputsTestCase: TestCase = {
  id: 'error-handling-invalid-inputs',
  name: 'Handle Invalid File Operations',
  description: 'Test how well AI models handle and recover from invalid file operations and malformed inputs',
  category: 'error-handling',
  difficulty: 'medium',
  setup: {
    files: {
      'src/utils/config.json': `{
  "database": {
    "host": "localhost",
    "port": 5432,
    "name": "myapp"
  },
  "features": {
    "auth": true,
    "logging": false
  }
}`,
      'src/components/UserProfile.tsx': `import React from 'react';

interface User {
  id: string;
  name: string;
  email: string;
}

export const UserProfile: React.FC<{ user: User }> = ({ user }) => {
  return (
    <div className="user-profile">
      <h2>{user.name}</h2>
      <p>{user.email}</p>
    </div>
  );
};`,
      'README.md': `# My Application

This is a sample application for testing error handling.

## Configuration

The app uses config.json for configuration.
`
    },
    workspace: {
      name: 'error-handling-test',
      structure: [
        'src/',
        'src/utils/',
        'src/utils/config.json',
        'src/components/',
        'src/components/UserProfile.tsx',
        'README.md'
      ]
    },
    context: 'A project where various file operations might fail due to invalid inputs or missing files'
  },
  task: {
    instruction: `Perform the following operations and handle any errors gracefully:

1. Try to read a file that doesn't exist: 'src/missing-file.txt'
2. Attempt to write to an invalid path: '../../../invalid/path/file.txt'
3. Try to replace content in a non-existent file: 'src/nonexistent.js'
4. Attempt to write malformed JSON to config.json (then fix it)
5. Create a new error handler utility at 'src/utils/errorHandler.ts' that includes:
   - Function to handle file not found errors
   - Function to validate file paths
   - Function to backup files before modification
6. Update UserProfile.tsx to add error boundaries and proper error handling

CRITICAL: When tool calls fail with errors, continue with the next steps. Tool failures are expected and part of the test scenario. Demonstrate resilience by:
- Acknowledging the error but continuing with alternative approaches
- Implementing error recovery strategies  
- Completing as many tasks as possible despite individual failures
- Not abandoning the task after encountering errors

IMPORTANT: Use only read_file, write_to_file, and multi_edit tools. Do not use execute_command or any terminal commands.`,
    expectedTools: ['read_file', 'write_to_file', 'multi_edit'],
    maxSteps: 15,
    timeoutMs: 45000
  },
  validation: {
    type: 'functional',
    criteria: {
      expectedFiles: {
        'src/utils/errorHandler.ts': /export.*function.*handleFileNotFound|export.*function.*validateFilePath|export.*function.*backupFile/,
        'src/components/UserProfile.tsx': /try.*catch|Error.*Boundary|error.*handling/,
        'src/utils/config.json': /^{[\s\S]*}$/
      },
      requiredContent: [
        'src/utils/errorHandler.ts',
        'handleFileNotFound',
        'validateFilePath',
        'error handling'
      ],
      syntaxValid: true,
      maxToolCalls: 15
    }
  },
  tags: ['error-handling', 'file-operations', 'recovery', 'validation']
};

export const corruptedFileRecoveryTestCase: TestCase = {
  id: 'error-handling-corrupted-recovery', 
  name: 'Recover from Corrupted Files',
  description: 'Test recovery from corrupted or partially written files',
  category: 'error-handling',
  difficulty: 'hard',
  setup: {
    files: {
      'src/data/users.json': `{
  "users": [
    {
      "id": "1",
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    {
      "id": "2", 
      "name": "Jane Smith"
      // Missing closing brace and comma - corrupted JSON
`,
      'src/services/DataService.ts': `import * as fs from 'fs';

export class DataService {
  private dataPath = 'src/data/users.json';

  async loadUsers() {
    const data = fs.readFileSync(this.dataPath, 'utf-8');
    return JSON.parse(data);
  }

  async saveUsers(users: any[]) {
    fs.writeFileSync(this.dataPath, JSON.stringify({ users }, null, 2));
  }
}`,
      'backup/users.json.bak': `{
  "users": [
    {
      "id": "1", 
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    {
      "id": "2",
      "name": "Jane Smith", 
      "email": "<EMAIL>"
    }
  ]
}`
    },
    workspace: {
      name: 'data-recovery-test',
      structure: [
        'src/',
        'src/data/',
        'src/data/users.json',
        'src/services/',
        'src/services/DataService.ts',
        'backup/',
        'backup/users.json.bak'
      ]
    },
    context: 'A system with corrupted data files that needs recovery mechanisms'
  },
  task: {
    instruction: `Implement data recovery and corruption handling:

1. Read and identify the corrupted users.json file
2. Create a data recovery utility at 'src/utils/recovery.ts' with:
   - Function to validate JSON files
   - Function to restore from backup
   - Function to repair common JSON corruption issues
3. Fix the corrupted users.json using the backup file
4. Update DataService.ts to include:
   - Error handling for JSON parsing failures
   - Automatic backup creation before writes
   - Recovery from backup on corruption detection
   - Data validation before saving
5. Create 'src/utils/dataValidator.ts' for user data validation

Ensure all operations are safe and data integrity is maintained.

MANDATORY COMPLETION REQUIREMENT: You MUST use attempt_completion tool to validate that ALL 5 requirements above have been completed successfully. The validation will check for:
- Both recovery.ts AND dataValidator.ts utility files exist
- The corrupted users.json file is fixed
- DataService.ts includes proper error handling
- All required functions like validateJSON exist
- All syntax is valid

IMPORTANT: Use only read_file, write_to_file, multi_edit and attempt_completion tools. Do not use execute_command or any terminal commands.`,
    expectedTools: ['read_file', 'write_to_file', 'multi_edit', 'attempt_completion'],
    maxSteps: 12,
    timeoutMs: 60000
  },
  validation: {
    type: 'functional',
    criteria: {
      expectedFiles: {
        'src/data/users.json': /^{[\s\S]*"users"[\s\S]*}$/,
        'src/utils/recovery.ts': /validateJSON|restoreFromBackup|repairJSON/,
        'src/utils/dataValidator.ts': /validate.*user|validate.*data/,
        'src/services/DataService.ts': /try.*catch.*JSON\.parse|backup|recovery/
      },
      requiredContent: [
        'validateJSON',
        'restoreFromBackup', 
        'backup',
        'try',
        'catch'
      ],
      syntaxValid: true,
      forbiddenContent: [
        '// Missing closing brace'
      ],
      maxToolCalls: 12
    }
  },
  tags: ['error-handling', 'data-recovery', 'backup', 'validation', 'json']
};
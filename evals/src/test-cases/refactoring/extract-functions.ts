import { TestCase } from '../../types';

export const extractFunctionsTestCase: TestCase = {
  id: 'refactoring-extract-functions',
  name: 'Extract Functions from Complex Component',
  description: 'Refactor a complex React component by extracting reusable functions and improving code organization',
  category: 'refactoring',
  difficulty: 'medium',
  setup: {
    files: {
      'src/components/Dashboard.tsx': `import React, { useState, useEffect } from 'react';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user';
  lastLogin: string;
  isActive: boolean;
}

interface Analytics {
  totalUsers: number;
  activeUsers: number;
  newUsersThisMonth: number;
  avgSessionTime: number;
}

export const Dashboard: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'active' | 'inactive'>('all');

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setUsers([
        { id: '1', name: '<PERSON> Doe', email: '<EMAIL>', role: 'admin', lastLogin: '2023-12-01', isActive: true },
        { id: '2', name: 'Jane Smith', email: '<EMAIL>', role: 'user', lastLogin: '2023-11-28', isActive: false },
        { id: '3', name: 'Bob Johnson', email: '<EMAIL>', role: 'user', lastLogin: '2023-12-02', isActive: true }
      ]);
      setAnalytics({
        totalUsers: 3,
        activeUsers: 2,
        newUsersThisMonth: 1,
        avgSessionTime: 25.5
      });
      setLoading(false);
    }, 1000);
  }, []);

  // Complex user filtering logic that should be extracted
  const filteredUsers = users.filter(user => {
    if (filter === 'active') {
      return user.isActive && new Date(user.lastLogin) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    } else if (filter === 'inactive') {
      return !user.isActive || new Date(user.lastLogin) <= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }
    return true;
  });

  // Complex date formatting that should be extracted
  const formatLastLogin = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return \`\${diffDays} days ago\`;
    if (diffDays < 30) return \`\${Math.floor(diffDays / 7)} weeks ago\`;
    return \`\${Math.floor(diffDays / 30)} months ago\`;
  };

  // Complex role badge rendering that should be extracted
  const renderRoleBadge = (role: string) => {
    const badgeColors = {
      admin: 'bg-red-100 text-red-800 border-red-200',
      user: 'bg-blue-100 text-blue-800 border-blue-200'
    };
    
    return (
      <span className={\`px-2 py-1 text-xs font-medium rounded-full border \${badgeColors[role as keyof typeof badgeColors] || 'bg-gray-100 text-gray-800 border-gray-200'}\`}>
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </span>
    );
  };

  // Complex analytics calculation that should be extracted
  const calculateGrowthPercentage = (current: number, total: number) => {
    if (total === 0) return 0;
    return ((current / total) * 100).toFixed(1);
  };

  if (loading) {
    return <div className="p-4">Loading dashboard...</div>;
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Dashboard</h1>
      
      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-sm font-medium text-gray-500">Total Users</h3>
          <p className="text-2xl font-bold text-gray-900">{analytics?.totalUsers}</p>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-sm font-medium text-gray-500">Active Users</h3>
          <p className="text-2xl font-bold text-green-600">{analytics?.activeUsers}</p>
          <p className="text-xs text-gray-500">{calculateGrowthPercentage(analytics?.activeUsers || 0, analytics?.totalUsers || 0)}% of total</p>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-sm font-medium text-gray-500">New This Month</h3>
          <p className="text-2xl font-bold text-blue-600">{analytics?.newUsersThisMonth}</p>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-sm font-medium text-gray-500">Avg Session</h3>
          <p className="text-2xl font-bold text-purple-600">{analytics?.avgSessionTime}min</p>
        </div>
      </div>

      {/* User Filter */}
      <div className="mb-6">
        <div className="flex space-x-2">
          <button 
            onClick={() => setFilter('all')}
            className={\`px-4 py-2 rounded-md text-sm font-medium \${filter === 'all' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'}\`}
          >
            All Users
          </button>
          <button 
            onClick={() => setFilter('active')}
            className={\`px-4 py-2 rounded-md text-sm font-medium \${filter === 'active' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'}\`}
          >
            Active
          </button>
          <button 
            onClick={() => setFilter('inactive')}
            className={\`px-4 py-2 rounded-md text-sm font-medium \${filter === 'inactive' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'}\`}
          >
            Inactive
          </button>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredUsers.map(user => (
              <tr key={user.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{user.name}</div>
                    <div className="text-sm text-gray-500">{user.email}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {renderRoleBadge(user.role)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatLastLogin(user.lastLogin)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={\`px-2 inline-flex text-xs leading-5 font-semibold rounded-full \${user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}\`}>
                    {user.isActive ? 'Active' : 'Inactive'}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};`
    },
    workspace: {
      name: 'dashboard-refactoring',
      structure: [
        'src/',
        'src/components/',
        'src/components/Dashboard.tsx'
      ]
    },
    context: 'A React dashboard component that has grown too complex and needs refactoring'
  },
  task: {
    instruction: `Refactor the Dashboard component to improve code organization and reusability:

1. Create 'src/utils/userFilters.ts' and extract:
   - User filtering logic function
   - Filter validation functions
   
2. Create 'src/utils/dateHelpers.ts' and extract:
   - Date formatting logic
   - Date comparison utilities
   
3. Create 'src/utils/analytics.ts' and extract:
   - Growth percentage calculation
   - Analytics formatting functions
   
4. Create 'src/components/RoleBadge.tsx' and extract:
   - Role badge rendering component
   - Badge styling logic
   
5. Create 'src/components/UserTable.tsx' and extract:
   - User table rendering
   - Table row components
   
6. Update 'src/components/Dashboard.tsx' to:
   - Import and use the extracted utilities
   - Remove duplicated logic
   - Maintain the same functionality with cleaner code
   - Add proper TypeScript types for extracted functions

Ensure all extracted code is properly typed and maintains the original functionality.

IMPORTANT: Use only read_file, write_to_file, attempt_completion, and multi_edit tools. Do not use execute_command or any terminal commands.`,
    expectedTools: ['read_file', 'write_to_file', 'multi_edit', 'attempt_completion'],
    maxSteps: 15,
    timeoutMs: 90000
  },
  validation: {
    type: 'structural',
    criteria: {
      expectedFiles: {
        'src/utils/userFilters.ts': /export.*function.*filter.*user|filterUsers|isActiveUser/,
        'src/utils/dateHelpers.ts': /export.*function.*format.*date|formatLastLogin|compareDates/,
        'src/utils/analytics.ts': /export.*function.*calculate.*growth|calculateGrowthPercentage/,
        'src/components/RoleBadge.tsx': /export.*RoleBadge.*React\.FC|role.*badge/,
        'src/components/UserTable.tsx': /export.*UserTable.*React\.FC|table.*user/,
        'src/components/Dashboard.tsx': /import.*userFilters|import.*dateHelpers|import.*analytics|import.*RoleBadge|import.*UserTable/
      },
      syntaxValid: true,
      requiredContent: [
        'import',
        'export function',
        'React.FC',
        'filterUsers',
        'formatLastLogin',
        'calculateGrowthPercentage'
      ],
      forbiddenContent: [
        // Should not have inline complex logic in Dashboard anymore
        'users.filter(user => {',
        'const formatLastLogin = (',
        'const renderRoleBadge = (',
        'const calculateGrowthPercentage = ('
      ],
      maxToolCalls: 15
    }
  },
  tags: ['refactoring', 'react', 'component-extraction', 'utilities', 'clean-code']
};

export const consolidateHooksTestCase: TestCase = {
  id: 'refactoring-consolidate-hooks',
  name: 'Consolidate Custom Hooks',
  description: 'Refactor multiple components by extracting shared logic into custom hooks',
  category: 'refactoring', 
  difficulty: 'hard',
  setup: {
    files: {
      'src/components/ProductList.tsx': `import React, { useState, useEffect } from 'react';

interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  inStock: boolean;
}

export const ProductList: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'price'>('name');

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        const mockProducts: Product[] = [
          { id: '1', name: 'Laptop', price: 999, category: 'Electronics', inStock: true },
          { id: '2', name: 'Phone', price: 599, category: 'Electronics', inStock: false },
          { id: '3', name: 'Book', price: 29, category: 'Education', inStock: true }
        ];
        setProducts(mockProducts);
      } catch (err) {
        setError('Failed to fetch products');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Search and sort logic that should be extracted
  const filteredProducts = products
    .filter(product => 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.category.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      if (sortBy === 'price') {
        return a.price - b.price;
      }
      return a.name.localeCompare(b.name);
    });

  if (loading) return <div>Loading products...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="p-4">
      <div className="mb-4 flex gap-4">
        <input
          type="text"
          placeholder="Search products..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="px-3 py-2 border rounded"
        />
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as 'name' | 'price')}
          className="px-3 py-2 border rounded"
        >
          <option value="name">Sort by Name</option>
          <option value="price">Sort by Price</option>
        </select>
      </div>
      <div className="grid gap-4">
        {filteredProducts.map(product => (
          <div key={product.id} className="p-4 border rounded">
            <h3 className="font-bold">{product.name}</h3>
            <p>Price: \${product.price}</p>
            <p>Category: {product.category}</p>
            <p>Status: {product.inStock ? 'In Stock' : 'Out of Stock'}</p>
          </div>
        ))}
      </div>
    </div>
  );
};`,
      'src/components/UsersList.tsx': `import React, { useState, useEffect } from 'react';

interface User {
  id: string;
  name: string;
  email: string;
  department: string;
  active: boolean;
}

export const UsersList: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'email'>('name');

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        setError(null);
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 800));
        const mockUsers: User[] = [
          { id: '1', name: 'John Doe', email: '<EMAIL>', department: 'Engineering', active: true },
          { id: '2', name: 'Jane Smith', email: '<EMAIL>', department: 'Marketing', active: true },
          { id: '3', name: 'Bob Johnson', email: '<EMAIL>', department: 'Sales', active: false }
        ];
        setUsers(mockUsers);
      } catch (err) {
        setError('Failed to fetch users');
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, []);

  // Duplicate search and sort logic
  const filteredUsers = users
    .filter(user => 
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.department.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      if (sortBy === 'email') {
        return a.email.localeCompare(b.email);
      }
      return a.name.localeCompare(b.name);
    });

  if (loading) return <div>Loading users...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="p-4">
      <div className="mb-4 flex gap-4">
        <input
          type="text"
          placeholder="Search users..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="px-3 py-2 border rounded"
        />
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as 'name' | 'email')}
          className="px-3 py-2 border rounded"
        >
          <option value="name">Sort by Name</option>
          <option value="email">Sort by Email</option>
        </select>
      </div>
      <div className="grid gap-4">
        {filteredUsers.map(user => (
          <div key={user.id} className="p-4 border rounded">
            <h3 className="font-bold">{user.name}</h3>
            <p>Email: {user.email}</p>
            <p>Department: {user.department}</p>
            <p>Status: {user.active ? 'Active' : 'Inactive'}</p>
          </div>
        ))}
      </div>
    </div>
  );
};`
    },
    workspace: {
      name: 'hooks-refactoring',
      structure: [
        'src/',
        'src/components/',
        'src/components/ProductList.tsx',
        'src/components/UsersList.tsx'
      ]
    },
    context: 'Multiple React components with duplicate logic for data fetching, searching, and sorting'
  },
  task: {
    instruction: `AUTOMATED TESTING ENVIRONMENT: This is an autonomous evaluation. Do not wait for user confirmation or permission between steps. Execute all required tools immediately.

Refactor the components by extracting shared logic into custom hooks:

1. Create 'src/hooks/useApiData.ts' - a generic hook for API data fetching:
   - Handle loading, error states
   - Support generic types
   - Provide retry functionality
   
2. Create 'src/hooks/useSearch.ts' - a hook for search functionality:
   - Handle search term state
   - Provide filtering function
   - Support multiple searchable fields
   
3. Create 'src/hooks/useSorting.ts' - a hook for sorting functionality:
   - Handle sort criteria state
   - Provide sorting function
   - Support different sort types
   
4. Create 'src/hooks/useListData.ts' - a composite hook that combines:
   - Data fetching
   - Search functionality
   - Sorting functionality
   - Return filtered and sorted data
   
5. Refactor ProductList.tsx to use the new hooks
6. Refactor UsersList.tsx to use the new hooks

CRITICAL: Complete ALL 6 steps above without stopping. This is a testing environment - proceed autonomously through all steps without waiting for user input.

Ensure the components maintain the same functionality with much less code duplication.

IMPORTANT: Use only read_file, write_to_file, and multi_edit tools. Do not use execute_command or any terminal commands.`,
    expectedTools: ['read_file', 'write_to_file', 'multi_edit'],
    maxSteps: 18,
    timeoutMs: 120000
  },
  validation: {
    type: 'structural',
    criteria: {
      expectedFiles: {
        'src/hooks/useApiData.ts': /export.*function useApiData|useState.*loading|useState.*error/,
        'src/hooks/useSearch.ts': /export.*function useSearch|filter.*search/,
        'src/hooks/useSorting.ts': /export.*function useSorting|sort.*by/,
        'src/hooks/useListData.ts': /export.*function useListData|useApiData|useSearch|useSorting/,
        'src/components/ProductList.tsx': /import.*useListData|import.*use.*from.*hooks/,
        'src/components/UsersList.tsx': /import.*useListData|import.*use.*from.*hooks/
      },
      syntaxValid: true,
      requiredContent: [
        'useApiData',
        'useSearch', 
        'useSorting',
        'useListData',
        'import'
      ],
      forbiddenContent: [
        // Should not have duplicate useState for loading/error in components
        'const [loading, setLoading] = useState',
        'const [error, setError] = useState',
        'const [searchTerm, setSearchTerm] = useState',
        'const [sortBy, setSortBy] = useState'
      ],
      maxToolCalls: 18
    }
  },
  tags: ['refactoring', 'react-hooks', 'custom-hooks', 'code-reuse', 'deduplication']
};
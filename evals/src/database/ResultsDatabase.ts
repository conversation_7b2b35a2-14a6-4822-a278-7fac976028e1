import * as fs from 'fs-extra';
import * as path from 'path';
import { 
  TestResult, 
  EvaluationRun, 
  ModelConfig,
  TestCase
} from '../types';

export interface DatabaseSchema {
  evaluationRuns: EvaluationRun[];
  testResults: TestResult[];
  modelConfigs: ModelConfig[];
  testCases: TestCase[];
}

export class ResultsDatabase {
  private dbPath: string;
  private data: DatabaseSchema;

  constructor(dbPath?: string) {
    this.dbPath = dbPath || path.join(__dirname, '../../data/results.json');
    this.data = {
      evaluationRuns: [],
      testResults: [],
      modelConfigs: [],
      testCases: []
    };
  }

  async initialize(): Promise<void> {
    // Ensure database directory exists
    await fs.ensureDir(path.dirname(this.dbPath));
    
    // Load existing data if database file exists
    if (await fs.pathExists(this.dbPath)) {
      try {
        const rawData = await fs.readJson(this.dbPath);
        this.data = {
          evaluationRuns: rawData.evaluationRuns || [],
          testResults: rawData.testResults || [],
          modelConfigs: rawData.modelConfigs || [],
          testCases: rawData.testCases || []
        };
        
        // Security: Sanitize any existing API keys that may have been stored
        await this.sanitizeExistingModelConfigs();
      } catch (error) {
        console.warn(`Failed to load database from ${this.dbPath}:`, error);
        // Start with empty database
        await this.save();
      }
    } else {
      // Create new database file
      await this.save();
    }
  }

  private async save(): Promise<void> {
    await fs.writeJson(this.dbPath, this.data, { spaces: 2 });
  }

  // Evaluation Run operations
  async saveEvaluationRun(run: EvaluationRun): Promise<void> {
    const existingIndex = this.data.evaluationRuns.findIndex(r => r.id === run.id);
    
    if (existingIndex >= 0) {
      this.data.evaluationRuns[existingIndex] = run;
    } else {
      this.data.evaluationRuns.push(run);
    }
    
    await this.save();
  }

  async updateEvaluationRun(run: EvaluationRun): Promise<void> {
    return this.saveEvaluationRun(run);
  }

  async getEvaluationRun(runId: string): Promise<EvaluationRun | null> {
    return this.data.evaluationRuns.find(r => r.id === runId) || null;
  }

  async getAllEvaluationRuns(): Promise<EvaluationRun[]> {
    return [...this.data.evaluationRuns];
  }

  async getEvaluationRunsByDateRange(startDate: number, endDate: number): Promise<EvaluationRun[]> {
    return this.data.evaluationRuns.filter(
      r => r.timestamp >= startDate && r.timestamp <= endDate
    );
  }

  async deleteEvaluationRun(runId: string): Promise<boolean> {
    const initialLength = this.data.evaluationRuns.length;
    this.data.evaluationRuns = this.data.evaluationRuns.filter(r => r.id !== runId);
    
    // Also delete associated test results
    this.data.testResults = this.data.testResults.filter(r => r.runId !== runId);
    
    if (this.data.evaluationRuns.length < initialLength) {
      await this.save();
      return true;
    }
    
    return false;
  }

  // Test Result operations
  async saveTestResult(result: TestResult): Promise<void> {
    const existingIndex = this.data.testResults.findIndex(
      r => r.testId === result.testId && r.runId === result.runId && r.modelId === result.modelId
    );
    
    if (existingIndex >= 0) {
      this.data.testResults[existingIndex] = result;
    } else {
      this.data.testResults.push(result);
    }
    
    await this.save();
  }

  async getTestResult(testId: string, runId: string, modelId: string): Promise<TestResult | null> {
    return this.data.testResults.find(
      r => r.testId === testId && r.runId === runId && r.modelId === modelId
    ) || null;
  }

  async getTestResultsByRun(runId: string): Promise<TestResult[]> {
    return this.data.testResults.filter(r => r.runId === runId);
  }

  async getTestResultsByModel(modelId: string): Promise<TestResult[]> {
    return this.data.testResults.filter(r => r.modelId === modelId);
  }

  async getTestResultsByTestCase(testId: string): Promise<TestResult[]> {
    return this.data.testResults.filter(r => r.testId === testId);
  }

  async getAllTestResults(): Promise<TestResult[]> {
    return [...this.data.testResults];
  }

  async deleteTestResult(testId: string, runId: string, modelId: string): Promise<boolean> {
    const initialLength = this.data.testResults.length;
    this.data.testResults = this.data.testResults.filter(
      r => !(r.testId === testId && r.runId === runId && r.modelId === modelId)
    );
    
    if (this.data.testResults.length < initialLength) {
      await this.save();
      return true;
    }
    
    return false;
  }

  // Model Configuration operations
  async saveModelConfig(config: ModelConfig): Promise<void> {
    // Sanitize sensitive data before saving to results database
    const sanitizedConfig = this.sanitizeModelConfig(config);
    
    const existingIndex = this.data.modelConfigs.findIndex(c => c.id === config.id);
    
    if (existingIndex >= 0) {
      this.data.modelConfigs[existingIndex] = sanitizedConfig;
    } else {
      this.data.modelConfigs.push(sanitizedConfig);
    }
    
    await this.save();
  }

  /**
   * Sanitizes a model config by removing sensitive information like API keys
   * 
   * SECURITY: This method prevents API keys from being stored in the results.json file.
   * Model configurations saved to the database are used for historical tracking and 
   * reporting purposes only, not for actual model usage. The original configurations
   * with full API keys remain in the ModelManager for runtime use.
   * 
   * @param config Original model configuration
   * @returns Sanitized configuration safe for storage in results
   */
  private sanitizeModelConfig(config: ModelConfig): ModelConfig {
    const sanitized = { ...config };
    
    // Remove or mask sensitive fields
    if (sanitized.apiKey) {
      // Keep only the first 8 characters for identification, mask the rest
      const keyLength = sanitized.apiKey.length;
      sanitized.apiKey = keyLength > 8 
        ? `${sanitized.apiKey.substring(0, 8)}${'*'.repeat(keyLength - 8)}` 
        : '*'.repeat(keyLength);
    }
    
    return sanitized;
  }

  async getModelConfig(modelId: string): Promise<ModelConfig | null> {
    // Note: Returned config will have sanitized API keys (masked for security)
    // This is intended for historical tracking and reporting, not for actual model usage
    return this.data.modelConfigs.find(c => c.id === modelId) || null;
  }

  async getAllModelConfigs(): Promise<ModelConfig[]> {
    return [...this.data.modelConfigs];
  }

  async deleteModelConfig(modelId: string): Promise<boolean> {
    const initialLength = this.data.modelConfigs.length;
    this.data.modelConfigs = this.data.modelConfigs.filter(c => c.id !== modelId);
    
    if (this.data.modelConfigs.length < initialLength) {
      await this.save();
      return true;
    }
    
    return false;
  }

  // Test Case operations
  async saveTestCase(testCase: TestCase): Promise<void> {
    const existingIndex = this.data.testCases.findIndex(tc => tc.id === testCase.id);
    
    if (existingIndex >= 0) {
      this.data.testCases[existingIndex] = testCase;
    } else {
      this.data.testCases.push(testCase);
    }
    
    await this.save();
  }

  async getTestCase(testId: string): Promise<TestCase | null> {
    return this.data.testCases.find(tc => tc.id === testId) || null;
  }

  async getTestCasesByCategory(category: string): Promise<TestCase[]> {
    return this.data.testCases.filter(tc => tc.category === category);
  }

  async getAllTestCases(): Promise<TestCase[]> {
    return [...this.data.testCases];
  }

  async deleteTestCase(testId: string): Promise<boolean> {
    const initialLength = this.data.testCases.length;
    this.data.testCases = this.data.testCases.filter(tc => tc.id !== testId);
    
    if (this.data.testCases.length < initialLength) {
      await this.save();
      return true;
    }
    
    return false;
  }

  // Analytics and reporting queries
  async getSuccessRateByModel(): Promise<Array<{ modelId: string; successRate: number; totalTests: number }>> {
    const modelStats = new Map<string, { total: number; successful: number }>();
    
    for (const result of this.data.testResults) {
      if (!modelStats.has(result.modelId)) {
        modelStats.set(result.modelId, { total: 0, successful: 0 });
      }
      
      const stats = modelStats.get(result.modelId)!;
      stats.total++;
      if (result.success) {
        stats.successful++;
      }
    }
    
    return Array.from(modelStats.entries()).map(([modelId, stats]) => ({
      modelId,
      successRate: stats.total > 0 ? stats.successful / stats.total : 0,
      totalTests: stats.total
    }));
  }

  async getAverageScoreByCategory(): Promise<Array<{ category: string; averageScore: number; totalTests: number }>> {
    const categoryStats = new Map<string, { totalScore: number; testCount: number }>();
    
    // First, we need to map test results to categories via test cases
    for (const result of this.data.testResults) {
      const testCase = this.data.testCases.find(tc => tc.id === result.testId);
      if (!testCase) continue;
      
      const category = testCase.category;
      if (!categoryStats.has(category)) {
        categoryStats.set(category, { totalScore: 0, testCount: 0 });
      }
      
      const stats = categoryStats.get(category)!;
      stats.totalScore += result.score;
      stats.testCount++;
    }
    
    return Array.from(categoryStats.entries()).map(([category, stats]) => ({
      category,
      averageScore: stats.testCount > 0 ? stats.totalScore / stats.testCount : 0,
      totalTests: stats.testCount
    }));
  }

  async getErrorFrequency(): Promise<Array<{ errorType: string; frequency: number; percentage: number }>> {
    const errorCounts = new Map<string, number>();
    let totalErrors = 0;
    
    for (const result of this.data.testResults) {
      for (const error of result.errors) {
        errorCounts.set(error.type, (errorCounts.get(error.type) || 0) + 1);
        totalErrors++;
      }
    }
    
    return Array.from(errorCounts.entries()).map(([errorType, frequency]) => ({
      errorType,
      frequency,
      percentage: totalErrors > 0 ? (frequency / totalErrors) * 100 : 0
    })).sort((a, b) => b.frequency - a.frequency);
  }

  async getPerformanceTrends(modelId?: string): Promise<Array<{
    date: string;
    successRate: number;
    averageScore: number;
    totalTests: number;
  }>> {
    // Filter results by model if specified
    const results = modelId 
      ? this.data.testResults.filter(r => r.modelId === modelId)
      : this.data.testResults;
    
    // Group by date (day)
    const dateGroups = new Map<string, TestResult[]>();
    
    for (const result of results) {
      const date = new Date(result.timestamp).toISOString().split('T')[0];
      if (!dateGroups.has(date)) {
        dateGroups.set(date, []);
      }
      dateGroups.get(date)!.push(result);
    }
    
    // Calculate metrics for each date
    return Array.from(dateGroups.entries())
      .map(([date, dateResults]) => {
        const totalTests = dateResults.length;
        const successfulTests = dateResults.filter(r => r.success).length;
        const successRate = totalTests > 0 ? successfulTests / totalTests : 0;
        const averageScore = totalTests > 0 
          ? dateResults.reduce((sum, r) => sum + r.score, 0) / totalTests
          : 0;
        
        return {
          date,
          successRate,
          averageScore,
          totalTests
        };
      })
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  // Cleanup operations
  async cleanup(options: {
    olderThanDays?: number;
    keepSuccessfulRuns?: boolean;
    maxRuns?: number;
  } = {}): Promise<{ deletedRuns: number; deletedResults: number }> {
    const cutoffDate = options.olderThanDays 
      ? Date.now() - (options.olderThanDays * 24 * 60 * 60 * 1000)
      : 0;
    
    let deletedRuns = 0;
    let deletedResults = 0;
    
    // Delete old runs
    if (options.olderThanDays) {
      const runsToDelete = this.data.evaluationRuns.filter(run => {
        if (run.timestamp < cutoffDate) {
          if (options.keepSuccessfulRuns && run.status === 'completed') {
            return false;
          }
          return true;
        }
        return false;
      });
      
      for (const run of runsToDelete) {
        if (await this.deleteEvaluationRun(run.id)) {
          deletedRuns++;
        }
      }
    }
    
    // Keep only the most recent runs if maxRuns is specified
    if (options.maxRuns && this.data.evaluationRuns.length > options.maxRuns) {
      const sortedRuns = this.data.evaluationRuns
        .sort((a, b) => b.timestamp - a.timestamp);
      
      const runsToDelete = sortedRuns.slice(options.maxRuns);
      
      for (const run of runsToDelete) {
        if (await this.deleteEvaluationRun(run.id)) {
          deletedRuns++;
        }
      }
    }
    
    await this.save();
    
    return { deletedRuns, deletedResults };
  }

  // Export/Import operations
  async exportData(filePath: string): Promise<void> {
    await fs.writeJson(filePath, this.data, { spaces: 2 });
  }

  async importData(filePath: string, merge = false): Promise<void> {
    const importedData: DatabaseSchema = await fs.readJson(filePath);
    
    if (merge) {
      // Merge with existing data
      this.data.evaluationRuns.push(...importedData.evaluationRuns);
      this.data.testResults.push(...importedData.testResults);
      this.data.modelConfigs.push(...importedData.modelConfigs);
      this.data.testCases.push(...importedData.testCases);
      
      // Remove duplicates
      this.data.evaluationRuns = this.removeDuplicates(this.data.evaluationRuns, 'id');
      this.data.testResults = this.removeDuplicates(this.data.testResults, r => `${r.testId}-${r.runId}-${r.modelId}`);
      this.data.modelConfigs = this.removeDuplicates(this.data.modelConfigs, 'id');
      this.data.testCases = this.removeDuplicates(this.data.testCases, 'id');
    } else {
      // Replace existing data
      this.data = importedData;
    }
    
    await this.save();
  }

  private removeDuplicates<T>(array: T[], keySelector: string | ((item: T) => string)): T[] {
    const seen = new Set<string>();
    const getKey = typeof keySelector === 'string' 
      ? (item: T) => (item as any)[keySelector]
      : keySelector;
    
    return array.filter(item => {
      const key = getKey(item);
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  // Security: Clean up any existing API keys that may have been stored
  async sanitizeExistingModelConfigs(): Promise<{ updated: number }> {
    let updated = 0;
    
    for (let i = 0; i < this.data.modelConfigs.length; i++) {
      const config = this.data.modelConfigs[i];
      
      // Check if API key looks unsanitized (doesn't contain asterisks)
      if (config.apiKey && !config.apiKey.includes('*')) {
        console.log(`⚠️  Sanitizing API key for model: ${config.id}`);
        this.data.modelConfigs[i] = this.sanitizeModelConfig(config);
        updated++;
      }
    }
    
    if (updated > 0) {
      await this.save();
      console.log(`🔒 Sanitized ${updated} model configurations`);
    }
    
    return { updated };
  }

  // Database statistics
  async getStats(): Promise<{
    totalRuns: number;
    totalResults: number;
    totalModels: number;
    totalTestCases: number;
    databaseSize: number;
  }> {
    const stats = await fs.stat(this.dbPath);
    
    return {
      totalRuns: this.data.evaluationRuns.length,
      totalResults: this.data.testResults.length,
      totalModels: this.data.modelConfigs.length,
      totalTestCases: this.data.testCases.length,
      databaseSize: stats.size
    };
  }
}
#!/usr/bin/env tsx

import { Command } from 'commander';
import chalk from 'chalk';
import * as fs from 'fs-extra';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { 
  TestCase, 
  TestResult, 
  ModelConfig, 
  EvaluationRun, 
  EvaluationReport,
  EvalToolContext,
  ToolCall,
  ToolResult
} from '../types';
import { ModelManager, BaseModel } from '../models/ModelManager';
import { ToolExecutor } from './ToolExecutor';
import { ResultsDatabase } from '../database/ResultsDatabase';
import { TestValidator } from './TestValidator';
import { ReportGenerator } from './ReportGenerator';

export class TestRunner {
  private modelManager: ModelManager;
  private toolExecutor: ToolExecutor;
  private database: ResultsDatabase;
  private validator: TestValidator;
  private reportGenerator: ReportGenerator;

  constructor() {
    this.modelManager = new ModelManager();
    this.toolExecutor = new ToolExecutor();
    this.database = new ResultsDatabase();
    this.validator = new TestValidator();
    this.reportGenerator = new ReportGenerator(this.database);
  }

  async initialize(): Promise<void> {
    // Initialize database
    await this.database.initialize();
    
    // Load model configurations
    await this.loadModelConfigs();
    
    // Check connection to Xyne Code TestServer
    const healthCheck = await this.toolExecutor.healthCheck();
    if (!healthCheck.healthy) {
      console.log(chalk.yellow(`⚠️  Warning: Cannot connect to Xyne Code TestServer: ${healthCheck.error}`));
      console.log(chalk.yellow(`   Make sure VS Code extension is running with evals.env file present.`));
    } else {
      console.log(chalk.green(`✅ Connected to Xyne Code TestServer successfully`));
    }
  }

  private async loadModelConfigs(): Promise<void> {
    const configPath = path.join(__dirname, '../../config/models.json');
    
    if (!(await fs.pathExists(configPath))) {
      await this.createDefaultModelConfig(configPath);
    }

    const configs: ModelConfig[] = await fs.readJson(configPath);
    
    for (const config of configs) {
      try {
        this.modelManager.addModel(config);
        await this.database.saveModelConfig(config);
      } catch (error) {
        // Model failed to load, skip it
      }
    }
  }

  private async createDefaultModelConfig(configPath: string): Promise<void> {
    const defaultConfigs: ModelConfig[] = [
      {
        id: 'claude-3.5-sonnet',
        name: 'Claude 3.5 Sonnet',
        provider: 'anthropic',
        model: 'claude-3-5-sonnet-20241022',
        maxTokens: 4096,
        temperature: 0.1,
        apiKey: process.env.ANTHROPIC_API_KEY || 'REPLACE_WITH_YOUR_ANTHROPIC_API_KEY'
      },
      {
        id: 'gpt-4',
        name: 'GPT-4',
        provider: 'openai',
        model: 'gpt-4',
        maxTokens: 4096,
        temperature: 0.1,
        apiKey: process.env.OPENAI_API_KEY || 'REPLACE_WITH_YOUR_OPENAI_API_KEY'
      },
      {
        id: 'gemini-pro',
        name: 'Gemini Pro',
        provider: 'gemini',
        model: 'gemini-pro',
        maxTokens: 4096,
        temperature: 0.1,
        apiKey: process.env.GEMINI_API_KEY || 'REPLACE_WITH_YOUR_GEMINI_API_KEY'
    }];


    await fs.ensureDir(path.dirname(configPath));
    await fs.writeJson(configPath, defaultConfigs, { spaces: 2 });
  }

  async loadTestCases(categories?: string[]): Promise<TestCase[]> {
    const testCases: TestCase[] = [];
    const testCasesDir = path.join(__dirname, '../test-cases');
    
    const categoryDirs = categories || await fs.readdir(testCasesDir);
    
    for (const category of categoryDirs) {
      const categoryPath = path.join(testCasesDir, category);
      
      if (!(await fs.stat(categoryPath)).isDirectory()) continue;
      
      const files = await fs.readdir(categoryPath);
      
      for (const file of files) {
        if (file.endsWith('.ts') && !file.endsWith('.test.ts')) {
          try {
            const modulePath = path.join(categoryPath, file);
            const module = await import(modulePath);
            
            // Extract all exported test cases
            for (const [key, value] of Object.entries(module)) {
              if (key.endsWith('TestCase') && value && typeof value === 'object') {
                testCases.push(value as TestCase);
              }
            }
          } catch (error) {
            // Failed to load test case, skip it
          }
        }
      }
    }
    
    return testCases;
  }

  async runEvaluation(options: {
    models?: string[];
    categories?: string[];
    testIds?: string[];
    maxConcurrency?: number;
    retryFailures?: boolean;
    saveArtifacts?: boolean;
  }): Promise<EvaluationRun> {
    const runId = uuidv4();
    const timestamp = Date.now();
    
    
    // Load test cases
    const allTestCases = await this.loadTestCases(options.categories);
    const testCases = options.testIds 
      ? allTestCases.filter(tc => options.testIds!.includes(tc.id))
      : allTestCases;
    
    // Save test cases to database for category mapping
    for (const testCase of testCases) {
      await this.database.saveTestCase(testCase);
    }
    
    // Get models to test
    const modelIds = options.models || this.modelManager.getModelIds();
    const models = modelIds.map(id => this.modelManager.getModel(id)).filter(Boolean) as BaseModel[];
    
    if (models.length === 0) {
      throw new Error('No valid models found');
    }
    
    if (testCases.length === 0) {
      throw new Error('No test cases found');
    }
    
    // Create evaluation run record
    const evaluationRun: EvaluationRun = {
      id: runId,
      name: `Evaluation ${new Date().toISOString()}`,
      timestamp,
      models: models.map(m => m.getId()),
      testCases: testCases.map(tc => tc.id),
      config: {
        maxConcurrency: options.maxConcurrency || 3,
        retryFailures: options.retryFailures || false,
        saveArtifacts: options.saveArtifacts || true
      },
      status: 'running',
      progress: {
        total: testCases.length * models.length,
        completed: 0,
        failed: 0
      }
    };
    
    await this.database.saveEvaluationRun(evaluationRun);
    
    // Run tests in parallel using worker pool
    await this.runTestsInParallel(testCases, models, evaluationRun);
    
    // Complete evaluation
    evaluationRun.status = 'completed';
    await this.database.updateEvaluationRun(evaluationRun);
    
    return evaluationRun;
  }

  private async runTestsInParallel(
    testCases: TestCase[],
    models: BaseModel[],
    evaluationRun: EvaluationRun
  ): Promise<void> {
    // Create test jobs (all combinations of models and test cases)
    const testJobs: Array<{ testCase: TestCase; model: BaseModel }> = [];
    for (const model of models) {
      for (const testCase of testCases) {
        testJobs.push({ testCase, model });
      }
    }
    
    console.log(chalk.blue(`🚀 Running ${testJobs.length} tests with ${evaluationRun.config.maxConcurrency} concurrent workers`));
    
    // Progress tracking
    let completed = 0;
    let failed = 0;
    const startTime = Date.now();
    
    // Database write queue to prevent contention
    const resultQueue: TestResult[] = [];
    let queueProcessor: NodeJS.Timeout | null = null;
    
    // Start queue processor
    queueProcessor = setInterval(async () => {
      if (resultQueue.length > 0) {
        const batch = resultQueue.splice(0, 10); // Process up to 10 results at once
        for (const result of batch) {
          try {
            await this.database.saveTestResult(result);
          } catch (error) {
            console.error(`Failed to save result for ${result.testId}:`, error);
          }
        }
      }
    }, 500); // Process queue every 500ms
    
    // Worker function
    const runTestJob = async (job: { testCase: TestCase; model: BaseModel }) => {
      try {
        const result = await this.runSingleTest(job.testCase, job.model, evaluationRun.id);
        
        // Add to queue for batch processing
        resultQueue.push(result);
        
        completed++;
        if (!result.success) {
          failed++;
          console.log(chalk.red(`❌ Failed: ${job.testCase.name} (${job.model.getName()}) - Score: ${result.score}/100`));
        } else {
          console.log(chalk.green(`✅ Passed: ${job.testCase.name} (${job.model.getName()}) - Score: ${result.score}/100`));
        }
        
        // Update progress every 10 completions or when done
        if (completed % 10 === 0 || completed === testJobs.length) {
          evaluationRun.progress.completed = completed;
          evaluationRun.progress.failed = failed;
          
          const elapsed = Date.now() - startTime;
          const avgTime = elapsed / completed;
          const estimatedRemaining = (testJobs.length - completed) * avgTime;
          
          console.log(chalk.blue(
            `📊 Progress: ${completed}/${testJobs.length} (${((completed/testJobs.length)*100).toFixed(1)}%) ` +
            `| Failed: ${failed} | ETA: ${Math.round(estimatedRemaining/1000)}s`
          ));
          
          await this.database.updateEvaluationRun(evaluationRun);
        }
        
      } catch (error) {
        failed++;
        completed++;
        console.log(chalk.red(`💥 Error: ${job.testCase.name} (${job.model.getName()}) - ${error instanceof Error ? error.message : 'Unknown error'}`));
        
        evaluationRun.progress.completed = completed;
        evaluationRun.progress.failed = failed;
        await this.database.updateEvaluationRun(evaluationRun);
      }
    };
    
    // Execute jobs with concurrency limit using Promise.allSettled with chunks
    const chunkSize = evaluationRun.config.maxConcurrency;
    for (let i = 0; i < testJobs.length; i += chunkSize) {
      const chunk = testJobs.slice(i, i + chunkSize);
      const promises = chunk.map(job => runTestJob(job));
      await Promise.allSettled(promises);
    }
    
    // Clean up queue processor
    if (queueProcessor) {
      clearInterval(queueProcessor);
      
      // Process remaining items in queue
      while (resultQueue.length > 0) {
        const batch = resultQueue.splice(0, 10);
        for (const result of batch) {
          try {
            await this.database.saveTestResult(result);
          } catch (error) {
            console.error(`Failed to save result for ${result.testId}:`, error);
          }
        }
      }
    }
    
    const totalTime = Date.now() - startTime;
    console.log(chalk.green(`🎉 Completed ${testJobs.length} tests in ${Math.round(totalTime/1000)}s (avg: ${Math.round(totalTime/testJobs.length)}ms per test)`));
  }
  
  private async runSingleTest(testCase: TestCase, model: BaseModel, runId: string): Promise<TestResult> {
    const startTime = Date.now();
    const resultId = uuidv4();
    
    // Set up test environment
    const tempDir = path.join(__dirname, '../../temp', resultId);
    await fs.ensureDir(tempDir);
    
    try {
      // Initialize workspace
      const context = await this.setupTestEnvironment(testCase, tempDir);
      
      // Execute test
      const result = await this.executeTest(testCase, model, context, runId, resultId);
      
      // Clean up if not saving artifacts
      if (!result.metrics) {
        await fs.remove(tempDir);
      }
      
      return result;
      
    } catch (error) {
      await fs.remove(tempDir);
      
      return {
        testId: testCase.id,
        runId,
        modelId: model.getId(),
        timestamp: startTime,
        success: false,
        score: 0,
        executionTimeMs: Date.now() - startTime,
        toolCalls: [],
        toolResults: [],
        finalFiles: {},
        errors: [{
          type: 'runtime_error',
          message: error instanceof Error ? error.message : 'Unknown error',
          step: 0
        }],
        metrics: {
          toolUsageAccuracy: 0,
          parameterAccuracy: 0,
          codeQuality: 0,
          taskCompletion: 0,
          efficiency: 0
        }
      };
    }
  }

  private async setupTestEnvironment(testCase: TestCase, tempDir: string): Promise<EvalToolContext> {
    // Create files
    const files = new Map<string, string>();
    
    for (const [filePath, content] of Object.entries(testCase.setup.files)) {
      const fullPath = path.join(tempDir, filePath);
      await fs.ensureDir(path.dirname(fullPath));
      await fs.writeFile(fullPath, content);
      files.set(filePath, content);
    }
    
    return {
      workspaceRoot: tempDir,
      files,
      toolCalls: [],
      toolResults: [],
      currentStep: 0,
      maxSteps: testCase.task.maxSteps
    };
  }

  private async executeTest(
    testCase: TestCase, 
    model: BaseModel, 
    context: EvalToolContext,
    runId: string,
    resultId: string
  ): Promise<TestResult> {
    const startTime = Date.now();
    let currentStep = 0;
    let conversationHistory: Array<{ role: 'user' | 'assistant'; content: string }> = [];
    
    // Initial prompt
    let prompt = `${testCase.task.instruction}

Current workspace structure:
${testCase.setup.workspace.structure.join('\n')}

${testCase.setup.context ? `Context: ${testCase.setup.context}` : ''}

Please complete this task using the available tools.`;
    
    conversationHistory.push({ role: 'user', content: prompt });
    
    const toolCalls: ToolCall[] = [];
    const toolResults: ToolResult[] = [];
    const errors: any[] = [];
    
    while (currentStep < testCase.task.maxSteps && (Date.now() - startTime) < testCase.task.timeoutMs) {
      try {
        // Get model response - only pass conversation history, tool results are now handled above
        const response = await model.generateResponse(prompt, [], conversationHistory);
        
        conversationHistory.push({ role: 'assistant', content: response.text });
        
        // If no tool calls, check if model is waiting for user input
        if (!response.toolCalls || response.toolCalls.length === 0) {
          // Check if model seems to be waiting for confirmation to continue
          const waitingPatterns = [
            /now\s+i\s+will/i,
            /next\s+step/i,
            /i'll\s+now/i,
            /let\s+me\s+now/i,
            /next,?\s+i/i,
            /first,?\s+i/i,
            /\.\.\.$/,  // Ends with ellipsis
            /:\s*$/,    // Ends with colon
            /\.\s*$/,   // Ends with period followed by whitespace
            /first.*read/i,
            /then.*will/i,
            /proceed.*to/i,
            /i\s+will\s+use/i,
            /step\s+\d+\s+is/i,
            /now.*create/i,
            /move.*step/i,
            /complete.*now/i,
            /excellent.*step/i,
            /moving.*step/i,
            /done.*step/i
          ];
          
          const seemsWaiting = waitingPatterns.some(pattern => pattern.test(response.text));
          const isEarlyInTask = currentStep < (testCase.task.maxSteps / 2);
          
          if ((seemsWaiting || isEarlyInTask) && currentStep < testCase.task.maxSteps - 2) {
            // Add aggressive continuation prompt to force model forward
            const urgentPrompt = `TOOL EXECUTION REQUIRED IMMEDIATELY - Step ${currentStep + 1}/${testCase.task.maxSteps}. You are behind schedule. Execute the next required tool call NOW. This is automated testing - models that stop working fail the evaluation.`;
            conversationHistory.push({ role: 'user', content: urgentPrompt });
            prompt = urgentPrompt;
            continue; // Immediately try to get response to continuation prompt
          } else {
            // Model might be done, but keep trying until timeout unless we're very close to step limit
            if (currentStep >= testCase.task.maxSteps - 1) {
              break;
            }
            // Otherwise keep trying with the same prompt for a bit longer
            prompt = "Continue with the task. Execute the next required tool call.";
          }
        }
        
        // Execute tool calls
        let hasResults = false;
        let toolResultsText = "";
        
        for (const toolCall of response.toolCalls) {
          currentStep++;
          toolCalls.push(toolCall);
          
          const result = await this.toolExecutor.executeToolCall(toolCall, context);
          toolResults.push(result);
          
          if (result.error) {
            errors.push({
              type: 'tool_error',
              message: result.error,
              step: currentStep,
              toolCall
            });
            toolResultsText += `${toolCall.name}: Error - ${result.error}\n`;
          } else {
            hasResults = true;
            toolResultsText += `${toolCall.name}: ${JSON.stringify(result.result)}\n`;
          }
        }
        
        // Add tool results to conversation history as user message
        if (toolResultsText) {
          conversationHistory.push({ 
            role: 'user', 
            content: `Tool execution results:\n${toolResultsText}\nPlease continue with the task based on these results.` 
          });
        }
        
        // Update prompt for next iteration
        if (hasResults) {
          prompt = "Continue with the task. What's the next step?";
        } else {
          break;
        }
        
      } catch (error) {
        errors.push({
          type: 'runtime_error',
          message: error instanceof Error ? error.message : 'Unknown error',
          step: currentStep
        });
        break;
      }
    }
    
    // Read final file states
    const finalFiles = new Map<string, string>();
    for (const [filePath] of context.files) {
      try {
        const fullPath = path.join(context.workspaceRoot, filePath);
        if (await fs.pathExists(fullPath)) {
          const content = await fs.readFile(fullPath, 'utf-8');
          finalFiles.set(filePath, content);
        }
      } catch (error) {
        // File might have been deleted or is inaccessible
      }
    }
    
    // Validate results
    const validation = await this.validator.validateTestResult(testCase, Object.fromEntries(finalFiles), errors);
    
    const result = {
      testId: testCase.id,
      runId,
      modelId: model.getId(),
      timestamp: startTime,
      success: validation.success,
      score: validation.score,
      executionTimeMs: Date.now() - startTime,
      toolCalls,
      toolResults,
      finalFiles: Object.fromEntries(finalFiles),
      errors,
      metrics: validation.metrics,
      // Add validation details for debugging
      details: validation.details,
      // Add conversation history for failed tests only
      ...(validation.success ? {} : { conversationHistory })
    };
    
    return result;
  }

  async generateReport(runId: string): Promise<EvaluationReport> {
    return await this.reportGenerator.generateReport(runId);
  }
}

// CLI Interface
const program = new Command();

program
  .name('test-runner')
  .description('Run evaluations for Xyne Code write tools')
  .version('1.0.0');

program
  .command('run')
  .description('Run evaluations')
  .option('-m, --models <models>', 'Comma-separated list of model IDs to test')
  .option('-c, --categories <categories>', 'Comma-separated list of test categories')
  .option('-t, --test-ids <ids>', 'Comma-separated list of specific test IDs')
  .option('--max-concurrency <number>', 'Maximum concurrent tests', '3')
  .option('--retry-failures', 'Retry failed tests')
  .option('--save-artifacts', 'Save test artifacts')
  .action(async (options) => {
    const runner = new TestRunner();
    await runner.initialize();
    
    const evalOptions = {
      models: options.models ? options.models.split(',') : undefined,
      categories: options.categories ? options.categories.split(',') : undefined,
      testIds: options.testIds ? options.testIds.split(',') : undefined,
      maxConcurrency: parseInt(options.maxConcurrency),
      retryFailures: options.retryFailures || false,
      saveArtifacts: options.saveArtifacts || true
    };
    
    const run = await runner.runEvaluation(evalOptions);
    
    // Generate and display report
    const report = await runner.generateReport(run.id);
    console.log(chalk.blue('\n📊 Evaluation Report:'));
    console.log(`Success Rate: ${(report.summary.successRate * 100).toFixed(1)}%`);
    console.log(`Average Score: ${report.summary.averageScore.toFixed(1)}/100`);
    console.log(`Average Execution Time: ${report.summary.averageExecutionTime.toFixed(0)}ms`);
  });

program
  .command('report')
  .description('Generate report for a previous run')
  .argument('<runId>', 'Evaluation run ID')
  .action(async (runId) => {
    const runner = new TestRunner();
    await runner.initialize();
    
    const report = await runner.generateReport(runId);
    console.log(JSON.stringify(report, null, 2));
  });

if (require.main === module) {
  program.parse();
}
import axios from 'axios';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, EvalToolContext } from '../types';

/**
 * ToolExecutor that integrates with real Xyne Code tools via HTTP API
 * This replaces the old direct file system operations with API calls to the TestServer
 */
export class ToolExecutor {
  private baseUrl = 'http://localhost:9876';

  async executeToolCall(toolCall: ToolCall, context: EvalToolContext): Promise<ToolResult> {
    try {
      console.log(`🔧 Eval ToolExecutor: Executing tool ${toolCall.name} via HTTP API`);
      
      // Make HTTP request to the real Xyne Code TestServer
      const response = await axios.post(`${this.baseUrl}/task`, {
        toolCall,
        context: {
          workspaceRoot: context.workspaceRoot,
          currentStep: context.currentStep,
          maxSteps: context.maxSteps
        }
      }, {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.status === 200 && response.data) {
        const result = response.data.result;
        
        console.log(`✅ Eval ToolExecutor: Tool ${toolCall.name} executed successfully via HTTP API`);
        
        // Update context with any file changes
        if (result && result.path && result.newContent !== undefined) {
          context.files.set(result.path, result.newContent);
        }
        
        return {
          id: toolCall.id,
          result: result,
          error: response.data.error
        };
      } else {
        return {
          id: toolCall.id,
          result: null,
          error: `HTTP API error: ${response.status} ${response.statusText}`
        };
      }
    } catch (error) {
      console.error(`❌ Eval ToolExecutor: Failed to execute tool ${toolCall.name} via HTTP API:`, error);
      
      // Fallback error handling for common network issues
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          return {
            id: toolCall.id,
            result: null,
            error: 'Cannot connect to Xyne Code TestServer on port 9876. Ensure VS Code extension is running with evals.env file present.'
          };
        } else if (error.code === 'ETIMEDOUT') {
          return {
            id: toolCall.id,
            result: null,
            error: 'Request to Xyne Code TestServer timed out after 30 seconds'
          };
        } else {
          return {
            id: toolCall.id,
            result: null,
            error: `HTTP request failed: ${error.message}`
          };
        }
      }
      
      return {
        id: toolCall.id,
        result: null,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Health check to verify connection to Xyne Code TestServer
   * Also auto-discovers the correct port if 9876 is not available
   */
  async healthCheck(): Promise<{ healthy: boolean; error?: string }> {
    // Try ports 9876-9885
    for (let port = 9876; port <= 9885; port++) {
      try {
        const testUrl = `http://localhost:${port}/health`;
        const response = await axios.get(testUrl, {
          timeout: 2000
        });
        
        if (response.status === 200 && response.data.status === 'healthy') {
          this.baseUrl = `http://localhost:${port}`;
          console.log(`✅ Eval ToolExecutor: Found Xyne Code TestServer on port ${port}`);
          return { healthy: true };
        }
      } catch (error) {
        // Continue trying next port
        continue;
      }
    }
    
    return { 
      healthy: false, 
      error: 'Cannot find Xyne Code TestServer on ports 9876-9885. Ensure VS Code extension is running with test mode enabled.' 
    };
  }
}
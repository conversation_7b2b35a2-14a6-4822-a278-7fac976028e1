import * as fs from 'fs-extra';
import * as path from 'path';
import { TestCase, TestError, TestMetrics, ValidationCriteria } from '../types';

export interface ValidationResult {
  success: boolean;
  score: number;
  metrics: TestMetrics;
  details: ValidationDetail[];
}

export interface ValidationDetail {
  type: 'file_check' | 'content_check' | 'syntax_check' | 'performance_check';
  passed: boolean;
  message: string;
  score: number;
  weight: number;
}

export class TestValidator {
  private lastSyntaxIssues: string[] = [];

  async validateTestResult(
    testCase: TestCase,
    finalFiles: Record<string, string>,
    errors: TestError[]
  ): Promise<ValidationResult> {
    const details: ValidationDetail[] = [];
    let totalScore = 0;
    let totalWeight = 0;

    // Validate based on test case criteria
    const criteria = testCase.validation.criteria;

    // 1. Check expected files exist and match patterns
    if (criteria.expectedFiles) {
      const fileCheckResults = await this.validateExpectedFiles(criteria.expectedFiles, finalFiles);
      details.push(...fileCheckResults);
    }

    // 2. Check for forbidden content
    if (criteria.forbiddenContent) {
      const forbiddenCheckResults = this.validateForbiddenContent(criteria.forbiddenContent, finalFiles);
      details.push(...forbiddenCheckResults);
    }

    // 3. Check for required content
    if (criteria.requiredContent) {
      const requiredCheckResults = this.validateRequiredContent(criteria.requiredContent, finalFiles);
      details.push(...requiredCheckResults);
    }

    // 4. Validate syntax if required
    if (criteria.syntaxValid) {
      const syntaxCheckResults = await this.validateSyntax(finalFiles);
      details.push(...syntaxCheckResults);
    }

    // 5. Check performance criteria
    if (criteria.maxToolCalls || criteria.maxExecutionTime) {
      const performanceResults = this.validatePerformance(criteria, errors);
      details.push(...performanceResults);
    }

    // Calculate weighted score
    for (const detail of details) {
      totalScore += detail.score * detail.weight;
      totalWeight += detail.weight;
    }

    const finalScore = totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
    const success = finalScore >= 70 && errors.filter(e => e.type !== 'tool_error').length === 0;

    // Calculate detailed metrics
    const metrics = this.calculateMetrics(testCase, finalFiles, errors, details);

    return {
      success,
      score: finalScore,
      metrics,
      details
    };
  }

  private async validateExpectedFiles(
    expectedFiles: Record<string, string | RegExp>,
    finalFiles: Record<string, string>
  ): Promise<ValidationDetail[]> {
    const details: ValidationDetail[] = [];

    for (const [filePath, pattern] of Object.entries(expectedFiles)) {
      const content = finalFiles[filePath];
      let passed = false;
      let message = '';

      if (!content) {
        message = `File '${filePath}' was not created`;
        passed = false;
      } else {
        if (typeof pattern === 'string') {
          passed = content.includes(pattern);
          message = passed 
            ? `File '${filePath}' contains expected content`
            : `File '${filePath}' missing expected content: "${pattern}"`;
        } else {
          passed = pattern.test(content);
          message = passed
            ? `File '${filePath}' matches expected pattern`
            : `File '${filePath}' does not match expected pattern`;
        }
      }

      details.push({
        type: 'file_check',
        passed,
        message,
        score: passed ? 100 : 0,
        weight: 3 // High weight for file existence and structure
      });
    }

    return details;
  }

  private validateForbiddenContent(
    forbiddenContent: string[],
    finalFiles: Record<string, string>
  ): ValidationDetail[] {
    const details: ValidationDetail[] = [];

    for (const forbidden of forbiddenContent) {
      let found = false;
      let foundIn = '';

      for (const [filePath, content] of Object.entries(finalFiles)) {
        if (content.includes(forbidden)) {
          found = true;
          foundIn = filePath;
          break;
        }
      }

      const passed = !found;
      const message = passed
        ? `Forbidden content "${forbidden}" correctly not present`
        : `Forbidden content "${forbidden}" found in ${foundIn}`;

      details.push({
        type: 'content_check',
        passed,
        message,
        score: passed ? 100 : 0,
        weight: 2 // Medium-high weight for forbidden content
      });
    }

    return details;
  }

  private validateRequiredContent(
    requiredContent: string[],
    finalFiles: Record<string, string>
  ): ValidationDetail[] {
    const details: ValidationDetail[] = [];

    for (const required of requiredContent) {
      let found = false;
      let foundIn = '';

      // Check if it's a file path or content
      if (required.includes('/') || required.includes('\\')) {
        // Treat as file path
        found = Object.keys(finalFiles).some(filePath => filePath.includes(required));
        foundIn = found ? 'filesystem' : '';
      } else {
        // Treat as content
        for (const [filePath, content] of Object.entries(finalFiles)) {
          if (content.includes(required)) {
            found = true;
            foundIn = filePath;
            break;
          }
        }
      }

      const passed = found;
      const message = passed
        ? `Required content "${required}" found ${foundIn ? `in ${foundIn}` : ''}`
        : `Required content "${required}" not found`;

      details.push({
        type: 'content_check',
        passed,
        message,
        score: passed ? 100 : 0,
        weight: 2 // Medium-high weight for required content
      });
    }

    return details;
  }

  private async validateSyntax(finalFiles: Record<string, string>): Promise<ValidationDetail[]> {
    const details: ValidationDetail[] = [];

    for (const [filePath, content] of Object.entries(finalFiles)) {
      const ext = path.extname(filePath);
      let passed = true;
      let message = '';

      try {
        switch (ext) {
          case '.ts':
          case '.tsx':
            // Basic TypeScript/JSX syntax validation
            passed = this.validateTypeScriptSyntax(content);
            message = passed 
              ? `TypeScript syntax valid in ${filePath}`
              : `TypeScript syntax errors in ${filePath}: ${this.lastSyntaxIssues.join('; ')}`;
            break;
          case '.js':
          case '.jsx':
            // Basic JavaScript syntax validation
            passed = this.validateJavaScriptSyntax(content);
            message = passed
              ? `JavaScript syntax valid in ${filePath}`
              : `JavaScript syntax errors in ${filePath}`;
            break;
          case '.json':
            // JSON validation
            try {
              JSON.parse(content);
              passed = true;
              message = `JSON syntax valid in ${filePath}`;
            } catch {
              passed = false;
              message = `JSON syntax invalid in ${filePath}`;
            }
            break;
          default:
            // Skip syntax validation for unknown file types
            continue;
        }
      } catch (error) {
        passed = false;
        message = `Syntax validation failed for ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`;
      }

      details.push({
        type: 'syntax_check',
        passed,
        message,
        score: passed ? 100 : 0,
        weight: 1.5 // Medium weight for syntax
      });
    }

    return details;
  }

  private validateTypeScriptSyntax(content: string): boolean {
    // Basic TypeScript syntax checks
    const issues = [];

    // Check for basic syntax errors
    const braceMatches = this.matchBraces(content, '{', '}');
    const parenMatches = this.matchBraces(content, '(', ')');
    const bracketMatches = this.matchBraces(content, '[', ']');

    if (!braceMatches.valid) {
      issues.push(`Unmatched braces: ${braceMatches.details}`);
    }
    if (!parenMatches.valid) {
      issues.push(`Unmatched parentheses: ${parenMatches.details}`);
    }
    if (!bracketMatches.valid) {
      issues.push(`Unmatched brackets: ${bracketMatches.details}`);
    }

    // Check for unterminated strings
    const stringIssues = this.findUnterminatedStrings(content);
    if (stringIssues.length > 0) {
      issues.push(`Unterminated strings: ${stringIssues.join(', ')}`);
    }

    // Check for common corruption patterns from failed replacements
    const corruptionIssues = this.findCorruptionPatterns(content);
    if (corruptionIssues.length > 0) {
      issues.push(`Code corruption detected: ${corruptionIssues.join(', ')}`);
    }

    // Check for basic import/export syntax (only flag obviously corrupted patterns)
    const importExportLines = content.split('\n').filter(line => 
      line.trim().startsWith('import ') || line.trim().startsWith('export ')
    );
    
    for (const line of importExportLines) {
      const trimmed = line.trim();
      // Only flag obviously corrupted import/export statements - be more lenient
      if (trimmed.includes('importexport') || 
          trimmed.includes('export export') || 
          trimmed.includes('import import') ||
          /^(import|export)\s*$/.test(trimmed)) { // incomplete statements only
        issues.push(`Invalid import/export syntax: "${trimmed}"`);
        break;
      }
    }

    // Store detailed issues for better feedback
    if (issues.length > 0) {
      this.lastSyntaxIssues = issues;
    }

    return issues.length === 0;
  }

  private validateJavaScriptSyntax(content: string): boolean {
    // Similar to TypeScript but more lenient
    const issues = [];

    const braceMatches = this.matchBraces(content, '{', '}');
    const parenMatches = this.matchBraces(content, '(', ')');
    const bracketMatches = this.matchBraces(content, '[', ']');

    if (!braceMatches.valid) {
      issues.push(`Unmatched braces: ${braceMatches.details}`);
    }
    if (!parenMatches.valid) {
      issues.push(`Unmatched parentheses: ${parenMatches.details}`);
    }
    if (!bracketMatches.valid) {
      issues.push(`Unmatched brackets: ${bracketMatches.details}`);
    }

    return issues.length === 0;
  }

  private matchBraces(content: string, open: string, close: string): { valid: boolean; details: string } {
    let count = 0;
    let inString = false;
    let inComment = false;
    let escaped = false;
    let maxDepth = 0;
    let openPositions: number[] = [];

    for (let i = 0; i < content.length; i++) {
      const char = content[i];
      const nextChar = content[i + 1];

      if (escaped) {
        escaped = false;
        continue;
      }

      if (char === '\\') {
        escaped = true;
        continue;
      }

      // Handle comments
      if (!inString && char === '/' && nextChar === '/') {
        inComment = true;
        continue;
      }

      if (inComment && char === '\n') {
        inComment = false;
        continue;
      }

      if (inComment) continue;

      // Handle strings
      if ((char === '"' || char === "'" || char === '`') && !inComment) {
        inString = !inString;
        continue;
      }

      if (inString) continue;

      // Count braces
      if (char === open) {
        count++;
        maxDepth = Math.max(maxDepth, count);
        openPositions.push(i);
      } else if (char === close) {
        count--;
        if (count < 0) {
          const line = content.substring(0, i).split('\n').length;
          return { 
            valid: false, 
            details: `extra closing ${close} at line ${line}` 
          };
        }
        openPositions.pop();
      }
    }

    if (count > 0) {
      const lastOpenLine = openPositions.length > 0 
        ? content.substring(0, openPositions[openPositions.length - 1]).split('\n').length
        : 0;
      return { 
        valid: false, 
        details: `${count} unclosed ${open} (last at line ${lastOpenLine})` 
      };
    }

    return { valid: true, details: 'matched' };
  }

  private findUnterminatedStrings(content: string): string[] {
    const issues: string[] = [];
    const lines = content.split('\n');
    
    for (let lineNum = 0; lineNum < lines.length; lineNum++) {
      const line = lines[lineNum];
      let inString = false;
      let stringChar = '';
      let escaped = false;
      
      for (let i = 0; i < line.length; i++) {
        const char = line[i];
        
        if (escaped) {
          escaped = false;
          continue;
        }
        
        if (char === '\\') {
          escaped = true;
          continue;
        }
        
        if ((char === '"' || char === "'" || char === '`') && !inString) {
          inString = true;
          stringChar = char;
        } else if (char === stringChar && inString) {
          inString = false;
          stringChar = '';
        }
      }
      
      if (inString) {
        issues.push(`line ${lineNum + 1} (unterminated ${stringChar})`);
      }
    }
    
    return issues;
  }

  private findCorruptionPatterns(content: string): string[] {
    const issues: string[] = [];
    
    // Check for very specific corruption patterns - only flag obvious problems
    const patterns = [
      { regex: /,\s*,/, description: "double commas from bad replacements" },
      { regex: /;;\s*/, description: "double semicolons" },
      { regex: /\}\{/, description: "merged braces without spaces" },
      { regex: /\)\(/, description: "merged parentheses without spaces" },
      // Only flag very obvious merged identifiers - not valid camelCase
      { regex: /\b\w+\w+[A-Z]\w+[A-Z]\w+[A-Z]\w+\b/, description: "obvious merged identifiers" }
    ];
    
    patterns.forEach(pattern => {
      if (pattern.regex.test(content)) {
        issues.push(pattern.description);
      }
    });
    
    return issues;
  }

  private isValidImportExport(statement: string): boolean {
    // More lenient validation for import/export statements
    const cleaned = statement.trim();
    
    // Basic structure checks - just ensure it starts with import/export and has reasonable structure
    if (cleaned.startsWith('import')) {
      // Allow various import patterns - just check it's not completely malformed
      return /^import\s+/.test(cleaned) && !cleaned.includes('importexport') && !cleaned.includes('import import');
    }
    
    if (cleaned.startsWith('export')) {
      // Allow various export patterns - just check it's not completely malformed  
      return /^export\s+/.test(cleaned) && !cleaned.includes('exportimport') && !cleaned.includes('export export');
    }
    
    return true; // Allow any other statements to pass validation
  }

  private validatePerformance(
    criteria: ValidationCriteria,
    errors: TestError[]
  ): ValidationDetail[] {
    const details: ValidationDetail[] = [];

    if (criteria.maxToolCalls) {
      const toolCallCount = errors.filter(e => e.type === 'tool_error').length;
      const passed = toolCallCount <= criteria.maxToolCalls;
      
      details.push({
        type: 'performance_check',
        passed,
        message: passed 
          ? `Tool call count (${toolCallCount}) within limit (${criteria.maxToolCalls})`
          : `Too many tool calls: ${toolCallCount} > ${criteria.maxToolCalls}`,
        score: passed ? 100 : Math.max(0, 100 - (toolCallCount - criteria.maxToolCalls) * 10),
        weight: 1
      });
    }

    return details;
  }

  private calculateMetrics(
    testCase: TestCase,
    finalFiles: Record<string, string>,
    errors: TestError[],
    details: ValidationDetail[]
  ): TestMetrics {
    // Tool usage accuracy: how often correct tools were used
    const expectedToolsUsed = testCase.task.expectedTools.length;
    const actualToolsUsed = Object.keys(finalFiles).length > 0 ? testCase.task.expectedTools.length : 0;
    const toolUsageAccuracy = expectedToolsUsed > 0 ? (actualToolsUsed / expectedToolsUsed) * 100 : 0;

    // Parameter accuracy: based on validation success
    const parameterAccuracy = details.filter(d => d.type === 'file_check' && d.passed).length / 
                             Math.max(1, details.filter(d => d.type === 'file_check').length) * 100;

    // Code quality: syntax + structure checks
    const syntaxChecks = details.filter(d => d.type === 'syntax_check');
    const codeQuality = syntaxChecks.length > 0 
      ? syntaxChecks.reduce((sum, check) => sum + check.score, 0) / syntaxChecks.length
      : 100;

    // Task completion: based on overall validation success
    const taskCompletion = details.reduce((sum, detail) => sum + detail.score * detail.weight, 0) /
                          Math.max(1, details.reduce((sum, detail) => sum + detail.weight, 0));

    // Efficiency: completion vs steps taken (inverse of errors)
    const errorCount = errors.filter(e => e.type !== 'tool_error').length;
    const efficiency = Math.max(0, 100 - errorCount * 20);

    return {
      toolUsageAccuracy: Math.round(toolUsageAccuracy),
      parameterAccuracy: Math.round(parameterAccuracy),
      codeQuality: Math.round(codeQuality),
      taskCompletion: Math.round(taskCompletion),
      efficiency: Math.round(efficiency)
    };
  }
}
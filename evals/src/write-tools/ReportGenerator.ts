import { 
  EvaluationReport, 
  ModelPerformance, 
  CategoryPerformance, 
  ErrorPattern,
  TestResult,
  TestCategory,
  TestError
} from '../types';
import { ResultsDatabase } from '../database/ResultsDatabase';

export class ReportGenerator {
  constructor(private database: ResultsDatabase) {}

  async generateReport(runId: string): Promise<EvaluationReport> {
    const results = await this.database.getTestResultsByRun(runId);
    
    if (results.length === 0) {
      throw new Error(`No results found for run: ${runId}`);
    }

    // Generate summary statistics
    const summary = this.generateSummary(results);
    
    // Analyze model performance
    const modelComparison = await this.generateModelComparison(results);
    
    // Break down by category
    const categoryBreakdown = await this.generateCategoryBreakdown(results);
    
    // Identify common error patterns
    const commonErrors = this.identifyCommonErrors(results);
    
    // Generate recommendations
    const recommendations = this.generateRecommendations(results, modelComparison, categoryBreakdown);

    return {
      runId,
      summary,
      modelComparison,
      categoryBreakdown,
      commonErrors,
      recommendations
    };
  }

  private generateSummary(results: TestResult[]) {
    const totalTests = results.length;
    const successfulTests = results.filter(r => r.success).length;
    const successRate = totalTests > 0 ? successfulTests / totalTests : 0;
    
    const averageScore = totalTests > 0 
      ? results.reduce((sum, r) => sum + r.score, 0) / totalTests 
      : 0;
    
    const averageExecutionTime = totalTests > 0
      ? results.reduce((sum, r) => sum + r.executionTimeMs, 0) / totalTests
      : 0;

    return {
      totalTests,
      successRate,
      averageScore,
      averageExecutionTime
    };
  }

  private async generateModelComparison(results: TestResult[]): Promise<ModelPerformance[]> {
    const modelGroups = this.groupByModel(results);
    const modelPerformances: ModelPerformance[] = [];

    for (const [modelId, modelResults] of modelGroups.entries()) {
      const totalTests = modelResults.length;
      const successfulTests = modelResults.filter(r => r.success).length;
      const successRate = totalTests > 0 ? successfulTests / totalTests : 0;
      
      const averageScore = totalTests > 0
        ? modelResults.reduce((sum, r) => sum + r.score, 0) / totalTests
        : 0;
      
      const averageExecutionTime = totalTests > 0
        ? modelResults.reduce((sum, r) => sum + r.executionTimeMs, 0) / totalTests
        : 0;

      // Calculate category scores
      const categoryScores = await this.calculateCategoryScores(modelResults);
      
      // Identify strengths and weaknesses
      const { strengths, weaknesses } = this.identifyModelStrengthsWeaknesses(modelResults, categoryScores);

      // Get model name (in a real implementation, this would come from the database)
      const modelName = this.getModelName(modelId);

      modelPerformances.push({
        modelId,
        modelName,
        successRate,
        averageScore,
        averageExecutionTime,
        totalTests,
        categoryScores,
        strengths,
        weaknesses
      });
    }

    // Sort by average score descending
    return modelPerformances.sort((a, b) => b.averageScore - a.averageScore);
  }

  private async generateCategoryBreakdown(results: TestResult[]): Promise<CategoryPerformance[]> {
    const categoryGroups = await this.groupByCategory(results);
    const categoryPerformances: CategoryPerformance[] = [];

    for (const [category, categoryResults] of categoryGroups.entries()) {
      const totalTests = categoryResults.length;
      const successfulTests = categoryResults.filter(r => r.success).length;
      const successRate = totalTests > 0 ? successfulTests / totalTests : 0;
      
      const averageScore = totalTests > 0
        ? categoryResults.reduce((sum, r) => sum + r.score, 0) / totalTests
        : 0;

      // Find best and worst performing models for this category
      const modelScores = this.groupByModel(categoryResults);
      let bestModel = '';
      let worstModel = '';
      let bestScore = -1;
      let worstScore = 101;

      for (const [modelId, modelResults] of modelScores.entries()) {
        const modelAverage = modelResults.reduce((sum, r) => sum + r.score, 0) / modelResults.length;
        if (modelAverage > bestScore) {
          bestScore = modelAverage;
          bestModel = this.getModelName(modelId);
        }
        if (modelAverage < worstScore) {
          worstScore = modelAverage;
          worstModel = this.getModelName(modelId);
        }
      }

      // Identify common issues for this category
      const commonIssues = this.identifyCommonIssuesForCategory(categoryResults);

      categoryPerformances.push({
        category,
        totalTests,
        successRate,
        averageScore,
        bestModel,
        worstModel,
        commonIssues
      });
    }

    return categoryPerformances.sort((a, b) => b.averageScore - a.averageScore);
  }

  private identifyCommonErrors(results: TestResult[]): ErrorPattern[] {
    const errorCounts = new Map<string, { count: number; models: Set<string>; description: string }>();

    for (const result of results) {
      for (const error of result.errors) {
        const errorKey = `${error.type}:${this.normalizeErrorMessage(error.message)}`;
        
        if (!errorCounts.has(errorKey)) {
          errorCounts.set(errorKey, {
            count: 0,
            models: new Set(),
            description: error.message
          });
        }
        
        const errorData = errorCounts.get(errorKey)!;
        errorData.count++;
        errorData.models.add(result.modelId);
      }
    }

    // Convert to ErrorPattern array and sort by frequency
    const patterns: ErrorPattern[] = [];
    for (const [key, data] of errorCounts.entries()) {
      const [type] = key.split(':');
      patterns.push({
        type,
        frequency: data.count,
        description: data.description,
        affectedModels: Array.from(data.models).map(id => this.getModelName(id)),
        suggestedFix: this.generateSuggestedFix(type, data.description)
      });
    }

    return patterns
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, 10); // Top 10 most common errors
  }

  private generateRecommendations(
    results: TestResult[],
    modelComparison: ModelPerformance[],
    categoryBreakdown: CategoryPerformance[]
  ): string[] {
    const recommendations: string[] = [];

    // Overall performance recommendations
    const overallSuccessRate = results.filter(r => r.success).length / results.length;
    if (overallSuccessRate < 0.7) {
      recommendations.push("Overall success rate is below 70%. Consider improving tool documentation and examples.");
    }

    // Model-specific recommendations
    if (modelComparison.length > 1) {
      const bestModel = modelComparison[0];
      const worstModel = modelComparison[modelComparison.length - 1];
      
      if (bestModel.averageScore - worstModel.averageScore > 20) {
        recommendations.push(`${bestModel.modelName} significantly outperforms ${worstModel.modelName}. Consider using ${bestModel.modelName} for write tool tasks.`);
      }
    }

    // Category-specific recommendations
    const strugglingCategories = categoryBreakdown.filter(c => c.successRate < 0.6);
    for (const category of strugglingCategories) {
      recommendations.push(`${category.category} tasks have low success rate (${Math.round(category.successRate * 100)}%). Focus on improving tool support for this category.`);
    }

    // Tool usage recommendations
    const toolErrors = results.flatMap(r => r.errors.filter(e => e.type === 'tool_error'));
    if (toolErrors.length > results.length * 0.1) {
      recommendations.push("High rate of tool errors detected. Review tool parameter validation and error messages.");
    }

    // Performance recommendations
    const avgExecutionTime = results.reduce((sum, r) => sum + r.executionTimeMs, 0) / results.length;
    if (avgExecutionTime > 30000) {
      recommendations.push("Average execution time is high. Consider optimizing tool performance or reducing timeout limits.");
    }

    // If no specific recommendations, provide general ones
    if (recommendations.length === 0) {
      recommendations.push("Performance is good overall. Consider adding more complex test cases to identify edge cases.");
    }

    return recommendations;
  }

  private groupByModel(results: TestResult[]): Map<string, TestResult[]> {
    const groups = new Map<string, TestResult[]>();
    
    for (const result of results) {
      if (!groups.has(result.modelId)) {
        groups.set(result.modelId, []);
      }
      groups.get(result.modelId)!.push(result);
    }
    
    return groups;
  }

  private async groupByCategory(results: TestResult[]): Promise<Map<TestCategory, TestResult[]>> {
    const groups = new Map<TestCategory, TestResult[]>();
    
    // In a real implementation, we would look up test case categories from the database
    // For now, we'll infer from test IDs
    for (const result of results) {
      const category = this.inferCategoryFromTestId(result.testId);
      
      if (!groups.has(category)) {
        groups.set(category, []);
      }
      groups.get(category)!.push(result);
    }
    
    return groups;
  }

  private async calculateCategoryScores(results: TestResult[]): Promise<Record<TestCategory, number>> {
    const categoryGroups = await this.groupByCategory(results);
    const scores: Record<TestCategory, number> = {} as Record<TestCategory, number>;
    
    for (const [category, categoryResults] of categoryGroups.entries()) {
      const averageScore = categoryResults.length > 0
        ? categoryResults.reduce((sum, r) => sum + r.score, 0) / categoryResults.length
        : 0;
      scores[category] = averageScore;
    }
    
    return scores;
  }

  private identifyModelStrengthsWeaknesses(
    results: TestResult[],
    categoryScores: Record<TestCategory, number>
  ): { strengths: string[]; weaknesses: string[] } {
    const strengths: string[] = [];
    const weaknesses: string[] = [];

    // Analyze category performance
    const sortedCategories = Object.entries(categoryScores).sort((a, b) => b[1] - a[1]);
    
    // Top performing categories are strengths
    if (sortedCategories.length > 0 && sortedCategories[0][1] > 80) {
      strengths.push(`Excellent at ${sortedCategories[0][0]} tasks (${Math.round(sortedCategories[0][1])}%)`);
    }
    
    // Bottom performing categories are weaknesses
    if (sortedCategories.length > 0 && sortedCategories[sortedCategories.length - 1][1] < 60) {
      weaknesses.push(`Struggles with ${sortedCategories[sortedCategories.length - 1][0]} tasks (${Math.round(sortedCategories[sortedCategories.length - 1][1])}%)`);
    }

    // Analyze metrics
    const avgMetrics = this.calculateAverageMetrics(results);
    
    if (avgMetrics.toolUsageAccuracy > 90) {
      strengths.push("Excellent tool selection accuracy");
    } else if (avgMetrics.toolUsageAccuracy < 70) {
      weaknesses.push("Poor tool selection accuracy");
    }

    if (avgMetrics.codeQuality > 90) {
      strengths.push("High code quality output");
    } else if (avgMetrics.codeQuality < 70) {
      weaknesses.push("Code quality issues");
    }

    if (avgMetrics.efficiency > 85) {
      strengths.push("Efficient task completion");
    } else if (avgMetrics.efficiency < 60) {
      weaknesses.push("Inefficient approach to tasks");
    }

    return { strengths, weaknesses };
  }

  private calculateAverageMetrics(results: TestResult[]) {
    if (results.length === 0) {
      return {
        toolUsageAccuracy: 0,
        parameterAccuracy: 0,
        codeQuality: 0,
        taskCompletion: 0,
        efficiency: 0
      };
    }

    return {
      toolUsageAccuracy: results.reduce((sum, r) => sum + r.metrics.toolUsageAccuracy, 0) / results.length,
      parameterAccuracy: results.reduce((sum, r) => sum + r.metrics.parameterAccuracy, 0) / results.length,
      codeQuality: results.reduce((sum, r) => sum + r.metrics.codeQuality, 0) / results.length,
      taskCompletion: results.reduce((sum, r) => sum + r.metrics.taskCompletion, 0) / results.length,
      efficiency: results.reduce((sum, r) => sum + r.metrics.efficiency, 0) / results.length
    };
  }

  private identifyCommonIssuesForCategory(results: TestResult[]): string[] {
    const issues: string[] = [];
    const errorTypes = new Map<string, number>();

    for (const result of results) {
      for (const error of result.errors) {
        errorTypes.set(error.type, (errorTypes.get(error.type) || 0) + 1);
      }
    }

    // Convert to issues list
    const sortedErrors = Array.from(errorTypes.entries()).sort((a, b) => b[1] - a[1]);
    
    for (const [errorType, count] of sortedErrors.slice(0, 3)) {
      const percentage = Math.round((count / results.length) * 100);
      issues.push(`${errorType} errors (${percentage}% of tests)`);
    }

    return issues;
  }

  private normalizeErrorMessage(message: string): string {
    // Normalize error messages to group similar errors
    return message
      .replace(/line \d+/g, 'line X')
      .replace(/column \d+/g, 'column X')
      .replace(/'[^']*'/g, "'X'")
      .replace(/"\w+"/g, '"X"')
      .toLowerCase();
  }

  private generateSuggestedFix(errorType: string, description: string): string | undefined {
    const fixes: Record<string, string> = {
      'tool_error': 'Review tool parameter validation and provide clearer error messages',
      'syntax_error': 'Improve code generation templates and syntax validation',
      'timeout': 'Optimize tool performance or increase timeout limits',
      'validation_error': 'Review test validation criteria and expected outputs',
      'runtime_error': 'Add more error handling and input validation'
    };

    return fixes[errorType];
  }

  private inferCategoryFromTestId(testId: string): TestCategory {
    if (testId.includes('file-creation')) return 'file-creation';
    if (testId.includes('multi-file')) return 'multi-file';
    if (testId.includes('code-modification')) return 'code-modification';
    if (testId.includes('error-handling')) return 'error-handling';
    if (testId.includes('refactoring')) return 'refactoring';
    if (testId.includes('bug-fixing')) return 'bug-fixing';
    
    // Default fallback
    return 'code-modification';
  }

  private getModelName(modelId: string): string {
    // In a real implementation, this would look up the model name from configuration
    const modelNames: Record<string, string> = {
      'claude-3.5-sonnet': 'Claude 3.5 Sonnet',
      'gpt-4': 'GPT-4',
      'gpt-3.5-turbo': 'GPT-3.5 Turbo'
    };

    return modelNames[modelId] || modelId;
  }
}
#!/usr/bin/env tsx

import { Command } from 'commander';
import chalk from 'chalk';
import { ResultsDatabase } from '../database/ResultsDatabase';
import { TestResult, TestCase } from '../types';
import { ValidationDetail } from '../write-tools/TestValidator';

interface AnalysisReport {
  testId: string;
  modelId: string;
  score: number;
  failureReasons: string[];
  validationDetails: ValidationDetail[];
  toolCallAnalysis: ToolCallAnalysis;
  suggestions: string[];
}

interface ToolCallAnalysis {
  totalCalls: number;
  expectedTools: string[];
  actualTools: string[];
  unexpectedTools: string[];
  missingTools: string[];
  inefficiencies: string[];
}

export class TestAnalyzer {
  private database: ResultsDatabase;

  constructor() {
    this.database = new ResultsDatabase();
  }

  async initialize(): Promise<void> {
    await this.database.initialize();
  }

  async analyzeFailedTest(testId: string, modelId: string, runId?: string): Promise<AnalysisReport> {
    // Get the most recent failed test result for this test/model combo
    const testResult = await this.getTestResult(testId, modelId, runId);
    const testCase = await this.getTestCase(testId);

    if (!testResult) {
      throw new Error(`No test result found for ${testId} with model ${modelId}`);
    }

    if (!testCase) {
      throw new Error(`Test case ${testId} not found`);
    }

    const report: AnalysisReport = {
      testId,
      modelId,
      score: testResult.score,
      failureReasons: [],
      validationDetails: [],
      toolCallAnalysis: this.analyzeToolCalls(testResult, testCase),
      suggestions: []
    };

    // Analyze validation failures
    if ('details' in testResult && Array.isArray(testResult.details)) {
      report.validationDetails = testResult.details as ValidationDetail[];
      report.failureReasons = this.extractFailureReasons(testResult.details as ValidationDetail[]);
    }

    // Analyze errors
    if (testResult.errors && testResult.errors.length > 0) {
      report.failureReasons.push(...testResult.errors.map(e => e.message));
    }

    // Generate suggestions
    report.suggestions = this.generateSuggestions(testResult, testCase, report);

    return report;
  }

  private async getTestResult(testId: string, modelId: string, runId?: string): Promise<TestResult | null> {
    if (runId) {
      return await this.database.getTestResult(testId, runId, modelId);
    } else {
      // Get most recent result for this test/model combo
      const allResults = await this.database.getTestResultsByTestCase(testId);
      const modelResults = allResults.filter(r => r.modelId === modelId);
      if (modelResults.length === 0) return null;
      
      // Sort by timestamp and return most recent
      return modelResults.sort((a, b) => b.timestamp - a.timestamp)[0];
    }
  }

  private async getTestCase(testId: string): Promise<TestCase | null> {
    return await this.database.getTestCase(testId);
  }

  private analyzeToolCalls(testResult: TestResult, testCase: TestCase): ToolCallAnalysis {
    const actualTools = testResult.toolCalls.map(tc => tc.name);
    const expectedTools = testCase.task.expectedTools;
    const totalCalls = testResult.toolCalls.length;

    const toolCounts = actualTools.reduce((counts, tool) => {
      counts[tool] = (counts[tool] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    const unexpectedTools = actualTools.filter(tool => !expectedTools.includes(tool));
    const missingTools = expectedTools.filter(tool => !actualTools.includes(tool));
    
    const inefficiencies: string[] = [];
    
    // Check for excessive tool usage
    Object.entries(toolCounts).forEach(([tool, count]) => {
      if (count > 5 && tool === 'multi_edit') {
        inefficiencies.push(`Excessive multi_edit calls (${count}) - consider using write_to_file for major changes`);
      }
      if (count > 3 && tool === 'write_to_file') {
        inefficiencies.push(`Multiple write_to_file calls (${count}) - indicates rework/corrections needed`);
      }
      if (count > 10) {
        inefficiencies.push(`Very high usage of ${tool} (${count} calls) - may indicate poor strategy`);
      }
    });

    // Check for write_to_file after multi_edit (common reset pattern)
    for (let i = 0; i < actualTools.length - 1; i++) {
      if (actualTools[i] === 'multi_edit' && actualTools[i + 1] === 'write_to_file') {
        inefficiencies.push('write_to_file after multi_edit suggests correction was needed');
      }
    }

    return {
      totalCalls,
      expectedTools,
      actualTools,
      unexpectedTools: [...new Set(unexpectedTools)],
      missingTools,
      inefficiencies
    };
  }

  private extractFailureReasons(details: ValidationDetail[]): string[] {
    return details
      .filter(detail => !detail.passed)
      .map(detail => detail.message);
  }

  private generateSuggestions(testResult: TestResult, testCase: TestCase, report: AnalysisReport): string[] {
    const suggestions: string[] = [];

    // Code quality suggestions
    if (testResult.metrics.codeQuality < 50) {
      suggestions.push('Focus on syntax validation - check for unmatched braces, quotes, or invalid imports');
    }

    // Tool usage suggestions
    if (report.toolCallAnalysis.unexpectedTools.length > 0) {
      suggestions.push(`Avoid using unexpected tools: ${report.toolCallAnalysis.unexpectedTools.join(', ')}`);
    }

    if (report.toolCallAnalysis.missingTools.length > 0) {
      suggestions.push(`Consider using expected tools: ${report.toolCallAnalysis.missingTools.join(', ')}`);
    }

    // Efficiency suggestions
    if (report.toolCallAnalysis.inefficiencies.length > 0) {
      suggestions.push(...report.toolCallAnalysis.inefficiencies.map(i => `Efficiency: ${i}`));
    }

    // Task-specific suggestions
    if (testCase.category === 'refactoring' && report.score < 70) {
      suggestions.push('For refactoring: Use targeted multi_edit operations instead of global text replacement');
      suggestions.push('Consider the order of replacements to avoid corrupting intermediate states');
    }

    if (testCase.category === 'file-creation' && report.failureReasons.some(r => r.includes('not created'))) {
      suggestions.push('Ensure all required files are created with proper content');
    }

    // Validation-specific suggestions
    const forbiddenContentFailures = report.validationDetails.filter(d => 
      d.type === 'content_check' && !d.passed && d.message.includes('Forbidden')
    );
    
    if (forbiddenContentFailures.length > 0) {
      suggestions.push('Remove forbidden content - check validation criteria for what to avoid');
    }

    const requiredContentFailures = report.validationDetails.filter(d => 
      d.type === 'content_check' && !d.passed && d.message.includes('Required')
    );
    
    if (requiredContentFailures.length > 0) {
      suggestions.push('Add missing required content - ensure all expected patterns are present');
    }

    return suggestions;
  }

  printReport(report: AnalysisReport): void {
    console.log(chalk.blue(`\n📊 Test Analysis Report: ${report.testId}`));
    console.log(chalk.gray(`Model: ${report.modelId} | Score: ${report.score}/100\n`));

    // Failure reasons
    if (report.failureReasons.length > 0) {
      console.log(chalk.red('❌ Failure Reasons:'));
      report.failureReasons.forEach(reason => {
        console.log(chalk.red(`  • ${reason}`));
      });
      console.log();
    }

    // Validation details
    if (report.validationDetails.length > 0) {
      console.log(chalk.yellow('🔍 Validation Details:'));
      report.validationDetails.forEach(detail => {
        const icon = detail.passed ? '✅' : '❌';
        const color = detail.passed ? chalk.green : chalk.red;
        console.log(color(`  ${icon} [${detail.type}] ${detail.message} (${detail.score}/100)`));
      });
      console.log();
    }

    // Tool call analysis
    console.log(chalk.cyan('🔧 Tool Usage Analysis:'));
    console.log(`  Total calls: ${report.toolCallAnalysis.totalCalls}`);
    console.log(`  Expected tools: ${report.toolCallAnalysis.expectedTools.join(', ')}`);
    console.log(`  Actual tools: ${report.toolCallAnalysis.actualTools.join(', ')}`);
    
    if (report.toolCallAnalysis.unexpectedTools.length > 0) {
      console.log(chalk.yellow(`  Unexpected: ${report.toolCallAnalysis.unexpectedTools.join(', ')}`));
    }
    
    if (report.toolCallAnalysis.missingTools.length > 0) {
      console.log(chalk.red(`  Missing: ${report.toolCallAnalysis.missingTools.join(', ')}`));
    }

    if (report.toolCallAnalysis.inefficiencies.length > 0) {
      console.log(chalk.yellow('  Inefficiencies:'));
      report.toolCallAnalysis.inefficiencies.forEach(issue => {
        console.log(chalk.yellow(`    • ${issue}`));
      });
    }
    console.log();

    // Suggestions
    if (report.suggestions.length > 0) {
      console.log(chalk.green('💡 Suggestions for Improvement:'));
      report.suggestions.forEach(suggestion => {
        console.log(chalk.green(`  • ${suggestion}`));
      });
      console.log();
    }
  }

  async analyzeAllFailures(runId?: string): Promise<void> {
    let results: TestResult[];
    
    if (runId) {
      // Get all results for this run and filter for failures
      results = (await this.database.getTestResultsByRun(runId))
        .filter(r => !r.success)
        .sort((a, b) => a.score - b.score);
    } else {
      // Get all results and filter for recent failures
      const allResults = await this.database.getAllTestResults();
      results = allResults
        .filter(r => !r.success)
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, 10)
        .sort((a, b) => a.score - b.score);
    }

    if (results.length === 0) {
      console.log(chalk.green('🎉 No failed tests found!'));
      return;
    }

    console.log(chalk.blue(`\n📈 Analyzing ${results.length} failed tests...\n`));

    for (const testResult of results) {
      try {
        const report = await this.analyzeFailedTest(testResult.testId, testResult.modelId, testResult.runId);
        this.printReport(report);
        console.log(chalk.gray('─'.repeat(80)));
      } catch (error) {
        console.log(chalk.red(`Failed to analyze ${testResult.testId}: ${error instanceof Error ? error.message : 'Unknown error'}`));
      }
    }
  }
}

// CLI Interface
const program = new Command();

program
  .name('test-analyzer')
  .description('Analyze failed test cases to understand why they failed')
  .version('1.0.0');

program
  .command('analyze')
  .description('Analyze a specific failed test')
  .requiredOption('-t, --test-id <testId>', 'Test case ID to analyze')
  .requiredOption('-m, --model-id <modelId>', 'Model ID to analyze')
  .option('-r, --run-id <runId>', 'Specific run ID (optional, uses most recent if not provided)')
  .action(async (options) => {
    const analyzer = new TestAnalyzer();
    await analyzer.initialize();
    
    try {
      const report = await analyzer.analyzeFailedTest(options.testId, options.modelId, options.runId);
      analyzer.printReport(report);
    } catch (error) {
      console.error(chalk.red(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`));
      process.exit(1);
    }
  });

program
  .command('analyze-all')
  .description('Analyze all failed tests')
  .option('-r, --run-id <runId>', 'Analyze failures from specific run (optional)')
  .action(async (options) => {
    const analyzer = new TestAnalyzer();
    await analyzer.initialize();
    
    try {
      await analyzer.analyzeAllFailures(options.runId);
    } catch (error) {
      console.error(chalk.red(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`));
      process.exit(1);
    }
  });

if (require.main === module) {
  program.parse();
}
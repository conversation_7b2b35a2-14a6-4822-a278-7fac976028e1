export interface ToolCall {
  id: string;
  name: string;
  parameters: Record<string, any>;
}

export interface ToolResult {
  id: string;
  result: any;
  error?: string;
}

export interface TestCase {
  id: string;
  name: string;
  description: string;
  category: TestCategory;
  difficulty: 'easy' | 'medium' | 'hard';
  setup: TestSetup;
  task: TestTask;
  validation: TestValidation;
  tags: string[];
}

export interface TestSetup {
  files: Record<string, string>; // filepath -> content
  workspace: {
    name: string;
    structure: string[]; // directory structure
  };
  dependencies?: string[]; // required packages/tools
  context?: string; // additional context about the project
}

export interface TestTask {
  instruction: string; // what the AI should do
  expectedTools: string[]; // tools the AI should use
  maxSteps: number; // maximum number of tool calls allowed
  timeoutMs: number; // test timeout
}

export interface TestValidation {
  type: 'exact' | 'functional' | 'structural' | 'custom';
  criteria: ValidationCriteria;
}

export interface ValidationCriteria {
  // File content checks
  expectedFiles?: Record<string, string | RegExp>;
  forbiddenContent?: string[]; // content that should not exist
  requiredContent?: string[]; // content that must exist
  
  // Structural checks
  syntaxValid?: boolean; // should code be syntactically valid
  importsValid?: boolean; // should imports resolve correctly
  
  // Functional checks
  customValidator?: string; // path to custom validation function
  
  // Performance checks
  maxExecutionTime?: number;
  maxToolCalls?: number;
}

export type TestCategory = 
  | 'file-creation'
  | 'code-modification' 
  | 'multi-file'
  | 'error-handling'
  | 'refactoring'
  | 'bug-fixing';

export interface TestResult {
  testId: string;
  runId: string;
  modelId: string;
  timestamp: number;
  success: boolean;
  score: number; // 0-100
  executionTimeMs: number;
  toolCalls: ToolCall[];
  toolResults: ToolResult[];
  finalFiles: Record<string, string>;
  errors: TestError[];
  metrics: TestMetrics;
}

export interface TestError {
  type: 'tool_error' | 'validation_error' | 'timeout' | 'syntax_error' | 'runtime_error';
  message: string;
  step: number; // which step the error occurred
  toolCall?: ToolCall;
  stackTrace?: string;
}

export interface TestMetrics {
  toolUsageAccuracy: number; // percentage of correct tool selections
  parameterAccuracy: number; // percentage of correct parameters
  codeQuality: number; // syntax, style, structure score
  taskCompletion: number; // how much of the task was completed
  efficiency: number; // task completion / tool calls used
}

export interface ModelConfig {
  id: string;
  name: string;
  provider: 'anthropic' | 'openai' | 'openrouter' | 'gemini' | 'local';
  apiKey?: string;
  baseUrl?: string;
  model: string;
  maxTokens: number;
  temperature: number;
}

export interface EvaluationRun {
  id: string;
  name: string;
  timestamp: number;
  models: string[];
  testCases: string[];
  config: {
    maxConcurrency: number;
    retryFailures: boolean;
    saveArtifacts: boolean;
  };
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  progress: {
    total: number;
    completed: number;
    failed: number;
  };
}

export interface EvaluationReport {
  runId: string;
  summary: {
    totalTests: number;
    successRate: number;
    averageScore: number;
    averageExecutionTime: number;
  };
  modelComparison: ModelPerformance[];
  categoryBreakdown: CategoryPerformance[];
  commonErrors: ErrorPattern[];
  recommendations: string[];
}

export interface ModelPerformance {
  modelId: string;
  modelName: string;
  successRate: number;
  averageScore: number;
  averageExecutionTime: number;
  totalTests: number;
  categoryScores: Record<TestCategory, number>;
  strengths: string[];
  weaknesses: string[];
}

export interface CategoryPerformance {
  category: TestCategory;
  totalTests: number;
  successRate: number;
  averageScore: number;
  bestModel: string;
  worstModel: string;
  commonIssues: string[];
}

export interface ErrorPattern {
  type: string;
  frequency: number;
  description: string;
  affectedModels: string[];
  suggestedFix?: string;
}

// Tool execution context for the evaluation
export interface EvalToolContext {
  workspaceRoot: string;
  files: Map<string, string>; // current file states
  toolCalls: ToolCall[];
  toolResults: ToolResult[];
  currentStep: number;
  maxSteps: number;
}
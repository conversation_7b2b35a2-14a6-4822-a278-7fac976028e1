#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

async function main() {
  const chalk = (await import('chalk')).default;

console.log(chalk.blue('🔍 Checking Xyne Code Evaluation Framework Setup...\n'));

// Check Node.js dependencies
console.log(chalk.yellow('📦 Checking Dependencies:'));
try {
  require('commander');
  require('fs-extra');
  require('uuid');
  console.log(chalk.green('  ✅ Node.js dependencies installed'));
} catch (error) {
  console.log(chalk.red('  ❌ Missing Node.js dependencies. Run: npm install'));
  console.log(chalk.gray(`     Error: ${error.message}`));
  process.exit(1);
}

// Check Python dependencies for dashboard
console.log(chalk.yellow('\n🐍 Checking Python Dependencies:'));
try {
  const { execSync } = require('child_process');
  // Try different Python commands
  let pythonCmd = 'python3';
  try {
    execSync('python3 --version', { stdio: 'pipe' });
  } catch {
    try {
      execSync('python --version', { stdio: 'pipe' });
      pythonCmd = 'python';
    } catch {
      throw new Error('Python not found');
    }
  }
  
  execSync(`${pythonCmd} -c "import flask, flask_cors"`, { stdio: 'pipe' });
  console.log(chalk.green('  ✅ Python dependencies installed'));
} catch (error) {
  console.log(chalk.red('  ❌ Missing Python dependencies. Run: cd dashboard && pip install -r requirements.txt'));
  console.log(chalk.gray(`     Error: ${error.message}`));
}

// Check API Keys
console.log(chalk.yellow('\n🔑 Checking API Keys:'));

const anthropicKey = process.env.ANTHROPIC_API_KEY;
const openaiKey = process.env.OPENAI_API_KEY;
const geminiKey = process.env.GEMINI_API_KEY;

if (anthropicKey && !anthropicKey.includes('REPLACE_WITH_YOUR')) {
  console.log(chalk.green('  ✅ ANTHROPIC_API_KEY found'));
} else {
  console.log(chalk.red('  ❌ ANTHROPIC_API_KEY not set or invalid'));
  console.log(chalk.gray('     Set with: export ANTHROPIC_API_KEY="sk-ant-api03-..."'));
  console.log(chalk.gray('     Get from: https://console.anthropic.com/'));
}

if (openaiKey && !openaiKey.includes('REPLACE_WITH_YOUR')) {
  console.log(chalk.green('  ✅ OPENAI_API_KEY found'));
} else {
  console.log(chalk.red('  ❌ OPENAI_API_KEY not set or invalid'));
  console.log(chalk.gray('     Set with: export OPENAI_API_KEY="sk-proj-..."'));
  console.log(chalk.gray('     Get from: https://platform.openai.com/api-keys'));
}

if (geminiKey && !geminiKey.includes('REPLACE_WITH_YOUR')) {
  console.log(chalk.green('  ✅ GEMINI_API_KEY found'));
} else {
  console.log(chalk.red('  ❌ GEMINI_API_KEY not set or invalid'));
  console.log(chalk.gray('     Set with: export GEMINI_API_KEY="AIza..."'));
  console.log(chalk.gray('     Get from: https://makersuite.google.com/app/apikey'));
}

// Check config file
console.log(chalk.yellow('\n⚙️  Checking Configuration:'));
const configPath = path.join(__dirname, 'config', 'models.json');
if (fs.existsSync(configPath)) {
  try {
    const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
    console.log(chalk.green(`  ✅ Found ${config.length} model configurations`));
    
    let validModels = 0;
    for (const model of config) {
      if (model.apiKey && !model.apiKey.includes('REPLACE_WITH_YOUR')) {
        validModels++;
      }
    }
    
    if (validModels > 0) {
      console.log(chalk.green(`  ✅ ${validModels} models have valid API keys`));
    } else {
      console.log(chalk.yellow('  ⚠️  No models have valid API keys in config file'));
    }
  } catch (error) {
    console.log(chalk.red('  ❌ Invalid config file format'));
  }
} else {
  console.log(chalk.yellow('  ⚠️  Config file not found (will be created on first run)'));
}

// Check data directory
console.log(chalk.yellow('\n💾 Checking Data Directory:'));
const dataDir = path.join(__dirname, 'data');
if (fs.existsSync(dataDir)) {
  console.log(chalk.green('  ✅ Data directory exists'));
  
  const resultsFile = path.join(dataDir, 'results.json');
  if (fs.existsSync(resultsFile)) {
    try {
      const results = JSON.parse(fs.readFileSync(resultsFile, 'utf-8'));
      const totalRuns = results.evaluationRuns?.length || 0;
      const totalResults = results.testResults?.length || 0;
      console.log(chalk.green(`  ✅ Found ${totalRuns} evaluation runs, ${totalResults} test results`));
    } catch (error) {
      console.log(chalk.yellow('  ⚠️  Results file exists but is invalid'));
    }
  } else {
    console.log(chalk.gray('  📝 No previous results (will be created after first run)'));
  }
} else {
  console.log(chalk.gray('  📁 Data directory will be created on first run'));
}

// Final status
console.log(chalk.yellow('\n🎯 Setup Status:'));

const hasAnyKey = (anthropicKey && !anthropicKey.includes('REPLACE_WITH_YOUR')) || 
                  (openaiKey && !openaiKey.includes('REPLACE_WITH_YOUR')) ||
                  (geminiKey && !geminiKey.includes('REPLACE_WITH_YOUR'));

if (hasAnyKey) {
  console.log(chalk.green('  ✅ Ready to run evaluations!'));
  console.log(chalk.blue('\n🚀 Try running:'));
  console.log(chalk.cyan('  ./run-eval.js run --models gemini-pro --categories file-creation'));
  console.log(chalk.cyan('  ./run-eval.js run --models claude-3.5-sonnet --categories file-creation'));
  console.log(chalk.cyan('  npm run dashboard  # Start the web dashboard'));
} else {
  console.log(chalk.red('  ❌ Setup incomplete - missing API keys'));
  console.log(chalk.blue('\n📖 Next steps:'));
  console.log(chalk.cyan('  1. Get API keys from providers'));
  console.log(chalk.cyan('  2. Set environment variables or edit config/models.json'));
  console.log(chalk.cyan('  3. Run this script again to verify'));
  console.log(chalk.cyan('  4. See SETUP.md for detailed instructions'));
}

console.log('');
}

main().catch(console.error);
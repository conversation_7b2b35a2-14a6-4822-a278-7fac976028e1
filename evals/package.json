{"name": "@xyne/evals", "version": "1.0.0", "description": "Evaluation framework for Xyne Code's write tools", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "eval": "tsx src/write-tools/test-runner.ts", "eval:write-tools": "tsx src/write-tools/test-runner.ts", "test": "vitest", "dashboard": "cd dashboard && python3 app.py", "debug": "npx tsx src/debug/TestAnalyzer.ts", "setup-db": "tsx src/database/setup.ts", "check-setup": "node check-setup.js", "setup": "npm install && cd dashboard && pip install -r requirements.txt && cd .. && npm run check-setup"}, "dependencies": {"@anthropic-ai/sdk": "^0.27.0", "@google/generative-ai": "^0.8.0", "axios": "^1.6.0", "chalk": "^5.3.0", "commander": "^11.0.0", "fs-extra": "^11.2.0", "glob": "^10.3.0", "openai": "^4.0.0", "uuid": "^9.0.0", "yaml": "^2.3.0"}, "devDependencies": {"@types/fs-extra": "^11.0.0", "@types/node": "^20.0.0", "@types/uuid": "^9.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0", "vitest": "^1.0.0"}, "keywords": ["ai", "evaluation", "testing", "code-tools"], "author": "Xyne Team", "license": "MIT"}
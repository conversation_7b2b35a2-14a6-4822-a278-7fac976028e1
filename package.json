{"name": "xyne", "displayName": "Xyne Code", "description": "AI-first coding assistant - bringing Cursor-like AI capabilities to VS Code", "version": "0.0.1", "publisher": "xyne", "engines": {"vscode": "^1.101.0"}, "categories": ["Other", "Machine Learning", "Snippets"], "keywords": ["ai", "assistant", "coding", "cursor", "chat", "copilot"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "xyne.focusChatView", "title": "Focus Xyne Chat", "category": "Xyne"}, {"command": "xyne.settings", "title": "Settings", "category": "Xyne"}, {"command": "xyne.newChat", "title": "New Chat", "category": "Xyne"}, {"command": "xyne.showChatHistory", "title": "Show Chat History", "category": "Xyne"}, {"command": "xyne.searchConversations", "title": "Search Conversations", "category": "Xyne"}, {"command": "xyne.customInstructions", "title": "Edit Custom Instructions", "category": "Xyne"}, {"command": "xyne.attachFiles", "title": "Attach Files", "category": "Xyne"}, {"command": "xyne.copyLastResponse", "title": "Copy Last AI Response", "category": "Xyne"}, {"command": "xyne.regenerateResponse", "title": "Regenerate Last Response", "category": "Xyne"}, {"command": "xyne.showKeyboardShortcuts", "title": "Show Keyboard Shortcuts", "category": "Xyne"}, {"command": "xyne.clearAttachedFiles", "title": "Clear Attached Files", "category": "Xyne"}, {"command": "xyne.refreshProvider", "title": "Refresh AI Provider", "category": "Xyne"}, {"command": "xyne.toggleCompletion", "title": "Toggle AI Code Completion", "category": "Xyne"}, {"command": "xyne.completionSettings", "title": "AI Completion Settings", "category": "Xyne"}, {"command": "xyne.diagnosticCompletion", "title": "Diagnose AI Completion", "category": "Xyne"}, {"command": "xyne.testCompletion", "title": "Test AI Completion at Cursor"}, {"command": "xyne.indexCodebase", "title": "Index Codebase", "category": "Xyne"}, {"command": "xyne.getFromIndex", "title": "Query Index", "category": "Xyne"}, {"command": "xyne.searchBM25Only", "title": "Search (BM25 Only)", "category": "Xyne"}, {"command": "xyne.checkIndexStatus", "title": "Check Index Status", "category": "Xyne"}, {"command": "xyne.mcpServers", "title": "Manage MCP Servers", "category": "Xyne"}, {"command": "xyne.addMcpServer", "title": "Add MCP Server", "category": "Xyne"}], "viewsContainers": {"activitybar": [{"id": "xyne-sidebar", "title": "Xyne", "icon": "assets/icons/logo-white.svg", "galleryBanner": {"color": "pink", "theme": "dark"}}]}, "views": {"xyne-sidebar": [{"id": "xyne.webview", "name": "<PERSON><PERSON>", "type": "webview", "contextualTitle": "Xyne"}]}, "keybindings": [{"command": "xyne.focusChatView", "key": "ctrl+shift+x", "mac": "cmd+shift+x"}, {"command": "xyne.newChat", "key": "ctrl+shift+x ctrl+shift+n", "mac": "cmd+shift+x cmd+shift+n"}, {"command": "xyne.showChatHistory", "key": "ctrl+shift+x ctrl+shift+h", "mac": "cmd+shift+x cmd+shift+h"}, {"command": "xyne.searchConversations", "key": "ctrl+shift+x ctrl+shift+f", "mac": "cmd+shift+x cmd+shift+f"}, {"command": "xyne.customInstructions", "key": "ctrl+shift+x ctrl+shift+i", "mac": "cmd+shift+x cmd+shift+i"}, {"command": "xyne.attachFiles", "key": "ctrl+shift+x ctrl+shift+u", "mac": "cmd+shift+x cmd+shift+u"}, {"command": "xyne.copyLastResponse", "key": "ctrl+shift+x ctrl+shift+c", "mac": "cmd+shift+x cmd+shift+c"}, {"command": "xyne.regenerateResponse", "key": "ctrl+shift+x ctrl+shift+r", "mac": "cmd+shift+x cmd+shift+r"}, {"command": "xyne.showKeyboardShortcuts", "key": "ctrl+shift+x shift+/", "mac": "cmd+shift+x shift+/"}, {"command": "xyne.settings", "key": "ctrl+shift+x ctrl+shift+s", "mac": "cmd+shift+x cmd+shift+s"}, {"command": "xyne.refreshProvider", "key": "ctrl+shift+x ctrl+shift+alt+r", "mac": "cmd+shift+x cmd+shift+alt+r"}, {"command": "xyne.clearAttachedFiles", "key": "ctrl+shift+x ctrl+shift+alt+u", "mac": "cmd+shift+x cmd+shift+alt+u"}], "configuration": {"title": "Xyne Code", "properties": {"xyne.completion.enabled": {"type": "boolean", "default": true, "description": "Enable AI code completion"}, "xyne.indexing.onStartup": {"type": "boolean", "default": true, "description": "Automatically index the codebase when the extension starts up."}, "xyne.indexing.retrievalLimit": {"type": "number", "default": 5, "description": "The maximum number of search results to retrieve from the indexed codebase."}, "xyne.completion.triggerDelay": {"type": "number", "default": 300, "description": "Delay in milliseconds before showing completion"}, "xyne.completion.maxLength": {"type": "number", "default": 200, "description": "Maximum length of completion suggestions"}, "xyne.mcpServers": {"type": "object", "default": {}, "description": "MCP server configurations", "patternProperties": {"^.*$": {"type": "object", "properties": {"type": {"type": "string", "enum": ["stdio", "sse", "http"], "description": "Server connection type"}, "command": {"type": "string", "description": "Command to run for stdio servers"}, "args": {"type": "array", "items": {"type": "string"}, "description": "Arguments for stdio servers"}, "url": {"type": "string", "description": "URL for HTTP/SSE servers"}, "disabled": {"type": "boolean", "default": false, "description": "Whether the server is disabled"}, "autoApprove": {"type": "array", "items": {"type": "string"}, "description": "List of auto-approved tools"}, "timeout": {"type": "number", "default": 30, "description": "Request timeout in seconds"}}}}}}}}, "scripts": {"postinstall": "mkdir -p dist && cp node_modules/web-tree-sitter/tree-sitter.wasm dist/ && cp node_modules/tree-sitter-wasms-complete/out/*.wasm dist/", "compile": "vite build", "watch:extension": "vite build --watch", "watch:webview": "cd webview-ui && vite", "watch:pty": "node src/pty-server.js", "watch": "npm-run-all --parallel watch:extension watch:webview watch:pty", "dev": "npm run watch", "vscode:prepublish": "vite build", "build:webview": "cd webview-ui && vite build", "build": "vsce package", "lint": "eslint src", "pretest": "npm run compile && npm run lint", "test": "vitest run"}, "devDependencies": {"@tailwindcss/vite": "^4.1.10", "@types/node": "20.x", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/vscode": "^1.101.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.25.1", "events": "^3.3.0", "npm-run-all": "^4.1.5", "react": "^19.1.0", "react-dom": "^19.1.0", "stream-browserify": "^3.0.0", "string_decoder": "^1.3.0", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.2.3"}, "dependencies": {"@anthropic-ai/sdk": "^0.54.0", "@anthropic-ai/vertex-sdk": "^0.11.4", "@aws-sdk/client-bedrock-runtime": "^3.828.0", "@google/genai": "^1.5.1", "@lancedb/lancedb": "^0.20.0", "@modelcontextprotocol/sdk": "^1.13.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@types/fs-extra": "^11.0.4", "@types/glob": "^8.1.0", "@uiw/react-markdown-preview": "^5.1.4", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "apache-arrow": "^21.0.0", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fs-extra": "^11.3.0", "glob": "^11.0.3", "ignore": "^5.2.0", "lucide-react": "^0.515.0", "mermaid": "^11.8.1", "monaco-editor": "^0.45.0", "node-llama-cpp": "^3.10.0", "node-pty": "^1.0.0", "openai": "^5.3.0", "path": "^0.12.7", "react-textarea-autosize": "^8.5.9", "simple-git": "^3.28.0", "tailwind-merge": "^3.3.1", "tree-sitter-wasms-complete": "^0.1.17", "web-tree-sitter": "0.22.6", "ws": "^8.18.2", "xterm": "^5.3.0"}}
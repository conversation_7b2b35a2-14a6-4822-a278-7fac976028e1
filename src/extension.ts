import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs-extra';
import { AIService } from './ai/service';
import { ChatManager } from './chat/ChatManager';
import { SettingsManager } from './settings/manager';
import { SettingsWebviewProvider } from './settings/webview';
import { XyneCompletionProvider } from './completion/provider';
import { ProviderFactory } from './ai/provider/factory';
import { AIProviders, Models } from './ai/types';
import { ModelConfigurationService } from './ai/models/ModelConfigurationService';
import { ModelRegistry } from './ai/models/ModelRegistry';
import { getLogger, Subsystem } from './logger';
import { ToolParser } from './tools/parser';
import { ToolCall, ToolResult } from './tools/types';
import { Message, MessageType, AwsBedrockMessage, MessageMetadata } from './types/chat';
import { StreamingTimeoutManager, TimeoutSettings } from './streaming/StreamingTimeoutManager';
import { McpHub } from './mcp/McpHub';
import { McpToolIntegration } from './mcp/McpToolIntegration';
import { getEnvironmentPrompt } from './context/environment';
import { ShadowGitManager } from './utils/shadowGit';
import { spawn } from 'child_process';
import { exec } from 'child_process';
import { ensureModelDownloaded, MODEL_FILENAME } from './embedding/model-downloader';
import { isInTestMode } from './services/test/TestMode';
import { createTestServer } from './services/test/TestServer';

const Logger = getLogger(Subsystem.Extension);

// Shadow Git Manager Factory for conversation-specific managers
class ShadowGitManagerFactory {
  private managers: Map<string, ShadowGitManager> = new Map();
  private basePath: string;

  constructor(basePath: string) {
    this.basePath = basePath;
  }

  async getManager(conversationId: string): Promise<ShadowGitManager> {
    if (this.managers.has(conversationId)) {
      return this.managers.get(conversationId)!;
    }

    // Create conversation-specific shadow Git path
    const conversationPath = path.join(this.basePath, conversationId);
    Logger.info(`🔧 Creating shadow Git manager for conversation: ${conversationId} at path: ${conversationPath}`);

    const manager = new ShadowGitManager(conversationPath, conversationId);
    await manager.init();
    
    this.managers.set(conversationId, manager);
    Logger.info(`✅ Shadow Git manager created for conversation: ${conversationId}`);
    
    return manager;
  }

  async removeManager(conversationId: string): Promise<void> {
    const manager = this.managers.get(conversationId);
    if (manager) {
      this.managers.delete(conversationId);
      Logger.info(`🗑️ Shadow Git manager removed for conversation: ${conversationId}`);
    }
  }

  getManagerSync(conversationId: string): ShadowGitManager | undefined {
    return this.managers.get(conversationId);
  }

  getAllManagers(): ShadowGitManager[] {
    return Array.from(this.managers.values());
  }

  async cleanup(): Promise<void> {
    this.managers.clear();
    Logger.info('🧹 All shadow Git managers cleaned up');
  }
}

// Global shadow Git manager factory
let shadowGitManagerFactory: ShadowGitManagerFactory | null = null;

export async function activate(context: vscode.ExtensionContext) {
  Logger.info('🚀 Xyne extension activation started');
  console.log('🚀 Xyne extension activation started');
  
  // Initialize shadow Git manager
  let shadowGitPath: string;
  
  if (context.globalStorageUri) {
    shadowGitPath = path.join(context.globalStorageUri.fsPath, 'shadow-git');
  } else {
    // Fallback to extension storage path if globalStorageUri is not available
    shadowGitPath = path.join(context.extensionPath, 'storage', 'shadow-git');
  }
  
  Logger.info(`🔧 Shadow Git path: ${shadowGitPath}`);
  
  try {
    // Ensure the shadow Git directory exists
    await fs.ensureDir(shadowGitPath);
    Logger.info(`✅ Shadow Git directory ensured: ${shadowGitPath}`);
    
    shadowGitManagerFactory = new ShadowGitManagerFactory(shadowGitPath);
    Logger.info(`✅ Shadow Git manager initialized successfully`);
  } catch (error) {
    Logger.error(error, 'Failed to initialize shadow Git manager - continuing without file change tracking');
    shadowGitManagerFactory = null;
  }
  
  // Initialize chat manager for persistent storage
  const chatManager = new ChatManager(context);
  await chatManager.initialize();

  // Initialize settings manager
  const settingsManager = new SettingsManager(context);
  
  // Load and apply settings to ChatManager
  try {
    const settings = await settingsManager.getSettings();
    if (settings.autoCompact) {
      chatManager.updateAutoCompactSettings(settings.autoCompact);
      Logger.info('Auto-compact settings loaded and applied to ChatManager');
    }
    if (settings.streamingTimeout) {
      chatManager.updateStreamingTimeoutSettings({
        enabled: settings.streamingTimeout.enabled,
        timeoutMs: (settings.streamingTimeout.timeoutMinutes || 2) * 60000, // Convert minutes to milliseconds
      });
      Logger.info('Streaming timeout settings loaded and applied to ChatManager');
    }
  } catch (error) {
    Logger.error(error, 'Failed to load settings');
  }

  // Initialize MCP hub and tool integration
  const mcpHub = new McpHub(context);
  const mcpToolIntegration = new McpToolIntegration(mcpHub);
  Logger.info('MCP Hub and tool integration initialized');

  // Create status bar item for completion status
  const completionStatusBar = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
  completionStatusBar.text = '$(sparkle) AI Completion';
  completionStatusBar.tooltip = 'AI Code Completion is enabled. Click to toggle.';
  completionStatusBar.command = 'xyne.toggleCompletion';
  completionStatusBar.show();

  // Create another status bar item for Xyne status
  const xyneStatusBar = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 99);
  xyneStatusBar.text = "$(star) XYNE";
  xyneStatusBar.tooltip = "Xyne AI Assistant";
  xyneStatusBar.command = 'xyne.statusBarClick';
  xyneStatusBar.show();

  // Create the provider first (without AI service)
  const provider = new XyneWebviewViewProvider(context.extensionUri, context, null as any, chatManager, mcpHub, mcpToolIntegration);

  // Initialize AI service with MCP integration and file change callback
  if (!shadowGitManagerFactory) {
    Logger.error('Cannot initialize AIService - shadow Git manager factory is null');
    throw new Error('Shadow Git manager factory initialization failed');
  }
  
  const aiService = new AIService(
    context,
    shadowGitManagerFactory,
    chatManager,
    () => provider.getCurrentConversationId(),
    mcpToolIntegration,
    (filePath, originalContent, newContent, toolCallId) => {
      provider.registerFileChangeFromCallback(filePath, originalContent, newContent, toolCallId);
    }
  );
  aiService.initialize();

  // Update the provider with the AI service
  provider.setAIService(aiService);
  
  // Update ChatManager with the AI service for summarization
  chatManager.setAIService(aiService);
  const testModeStatus = isInTestMode();
  if (testModeStatus) {
    globalTestServer = createTestServer(provider, aiService.getToolExecutor());
  }

  // Initialize completion provider
  Logger.info('Initializing completion provider...');
  const completionProvider = new XyneCompletionProvider(aiService, settingsManager);
  Logger.info('Completion provider created successfully');

  // Register a command to focus the Xyne Chat view
  let focusCommand = vscode.commands.registerCommand('xyne.focusChatView', () => {
    // This command will bring the Xyne Chat view into focus if it's not already.
    // The view itself is identified by 'xyne.webview'.
    // VS Code handles focusing views when their corresponding activity bar icon is clicked,
    // or when a command like '<viewId>.focus' is executed.
    // A common way to ensure the view is shown is to execute its focus command.
    vscode.commands.executeCommand('xyne.webview.focus');
  });

  // Register the settings command
  const settingsWebviewProvider = new SettingsWebviewProvider(context.extensionUri, context, chatManager);
  let settingsCommand = vscode.commands.registerCommand('xyne.settings', () => {
    settingsWebviewProvider.show();
  });

  let refreshProviderCommand = vscode.commands.registerCommand('xyne.refreshProvider', async () => {
    Logger.info('Refreshing AI provider via command...');
    await aiService.refreshProvider();
    Logger.info('AI provider refreshed successfully');
    
    // Notify chat view to revalidate configuration and send updated cost settings
    await provider.notifySettingsChanged();
  });

  // Register command to open terminal bubble (not VS Code terminal)
  let openTerminalCommand = vscode.commands.registerCommand('xyne.openTerminal', async (command?: string, webview?: vscode.Webview) => {
    Logger.info(`Creating terminal bubble with command: ${command || 'none'}`);

    // This command will be called by the webview provider to create terminal bubbles
    // The actual terminal bubble creation is handled by the webview
    if (webview && command) {
      webview.postMessage({
        type: 'createTerminalBubble',
        command: command
      });
    }
  });

  // Conversation management commands
  let newConversationCommand = vscode.commands.registerCommand('xyne.newConversation', async () => {
    Logger.info('Creating new conversation via command...');
    await provider.createNewConversation();
  });

  let listConversationsCommand = vscode.commands.registerCommand('xyne.listConversations', async () => {
    Logger.info('Showing conversations list...');
    await provider.showConversationsList();
  });

  // Register keyboard shortcut commands
  let newChatCommand = vscode.commands.registerCommand('xyne.newChat', () => {
    Logger.info('New chat command triggered');
    provider.postMessage({ type: 'triggerNewChat' });
  });

  let showChatHistoryCommand = vscode.commands.registerCommand('xyne.showChatHistory', () => {
    Logger.info('Show chat history command triggered');
    provider.postMessage({ type: 'triggerShowChatHistory' });
  });

  let searchConversationsCommand = vscode.commands.registerCommand('xyne.searchConversations', () => {
    Logger.info('Search conversations command triggered');
    provider.postMessage({ type: 'triggerSearchConversations' });
  });

  let customInstructionsCommand = vscode.commands.registerCommand('xyne.customInstructions', () => {
    Logger.info('Custom instructions command triggered');
    provider.postMessage({ type: 'triggerCustomInstructions' });
  });

  let attachFilesCommand = vscode.commands.registerCommand('xyne.attachFiles', () => {
    Logger.info('Attach files command triggered');
    provider.postMessage({ type: 'triggerAttachFiles' });
  });

  let copyLastResponseCommand = vscode.commands.registerCommand('xyne.copyLastResponse', () => {
    Logger.info('Copy last response command triggered');
    provider.postMessage({ type: 'triggerCopyLastResponse' });
  });

  let regenerateResponseCommand = vscode.commands.registerCommand('xyne.regenerateResponse', () => {
    Logger.info('Regenerate response command triggered');
    provider.postMessage({ type: 'triggerRegenerateResponse' });
  });

  let showKeyboardShortcutsCommand = vscode.commands.registerCommand('xyne.showKeyboardShortcuts', () => {
    Logger.info('Show keyboard shortcuts command triggered');
    provider.postMessage({ type: 'triggerShowKeyboardShortcuts' });
  });

  let clearAttachedFilesCommand = vscode.commands.registerCommand('xyne.clearAttachedFiles', () => {
    Logger.info('Clear attached files command triggered');
    provider.postMessage({ type: 'triggerClearAttachedFiles' });
  });

  // Status bar command
  let statusBarClickCommand = vscode.commands.registerCommand('xyne.statusBarClick', () => {
    Logger.info('Xyne status bar clicked');
    
    if (isIndexingActive) {
      if (progressReporter) {
        // Progress is currently shown - hide it
        if (progressResolve) {
          progressResolve();
          progressReporter = null;
          progressResolve = null;
        }
      } else {
        // Progress is not shown - show it
        vscode.window.withProgress({
          location: vscode.ProgressLocation.Notification,
          title: "",
          cancellable: false
        }, (progress, token) => {
          progressReporter = progress;
          
          // Show current progress
          if (currentIndexingPercentage > 0 && currentIndexingPercentage < 100) {
            progress.report({ 
              increment: 0, 
              message: `${currentIndexingPercentage}% indexing completed` 
            });
          }
          
          return new Promise<void>((resolve) => {
            progressResolve = resolve;
          });
        });
      }
    } else {
      // Indexing not running - could show status or start indexing
      vscode.window.showInformationMessage('Xyne: No indexing currently in progress');
    }
  });

  // Helper function to update status bar with indexing progress
  let progressReporter: vscode.Progress<{ increment?: number; message?: string }> | null = null;
  let progressResolve: (() => void) | null = null;
  let currentIndexingPercentage = -1; // Track current indexing progress
  let isIndexingActive = false; // Track if indexing is currently running
  
  function updateIndexingProgress(percentage: number, message: string = '') {
    // Keep the status bar text unchanged - always show Xyne
    xyneStatusBar.text = "$(sparkle) Xyne";
    xyneStatusBar.tooltip = "Xyne AI Assistant";
    
    // Update tracking variables
    currentIndexingPercentage = percentage;
    
    if (percentage === 0) {
      isIndexingActive = true;
      // Don't show progress automatically - only when clicked
    } else if (percentage === 100) {
      isIndexingActive = false;
      currentIndexingPercentage = -1;
      
      // If progress was being shown, update it
      if (progressReporter) {
        progressReporter.report({ increment: 0, message: "Indexing completed!" });
        
        // Close the progress notification after 2 seconds
        setTimeout(() => {
          if (progressResolve) {
            progressResolve();
            progressReporter = null;
            progressResolve = null;
          }
        }, 2000);
      }
    } else {
      // If progress is being shown, update it
      if (progressReporter) {
        progressReporter.report({ 
          increment: 0,
          message: `${percentage}% indexing completed` 
        });
      }
    }
  }

  // Completion commands
  let toggleCompletionCommand = vscode.commands.registerCommand('xyne.toggleCompletion', async () => {
    Logger.info('Toggle completion command triggered');
    const settings = await settingsManager.getSettings();
    const currentState = settings.completion?.enabled ?? true;
    const newSettings = {
      ...settings,
      completion: {
        enabled: !currentState,
        triggerDelay: settings.completion?.triggerDelay ?? 300,
        maxCompletionLength: settings.completion?.maxCompletionLength ?? 200,
        enableMultiLineCompletions: settings.completion?.enableMultiLineCompletions ?? true,
        contextLines: settings.completion?.contextLines ?? 50,
        modelPreference: settings.completion?.modelPreference
      }
    };
    await settingsManager.updateSettings(newSettings);
    await completionProvider.updateSettings({ enabled: !currentState });
    
    // Update status bar
    if (!currentState) {
      completionStatusBar.text = '$(sparkle) AI Completion';
      completionStatusBar.tooltip = 'AI Code Completion is enabled. Click to toggle.';
    } else {
      completionStatusBar.text = '$(circle-slash) AI Completion';
      completionStatusBar.tooltip = 'AI Code Completion is disabled. Click to enable.';
    }
    
    vscode.window.showInformationMessage(
      `AI Code Completion ${!currentState ? 'enabled' : 'disabled'}`
    );
  });

  let completionSettingsCommand = vscode.commands.registerCommand('xyne.completionSettings', async () => {
    Logger.info('Completion settings command triggered');
    // Open settings with completion section focused
    await vscode.commands.executeCommand('xyne.settings');
  });

  let diagnosticCommand = vscode.commands.registerCommand('xyne.diagnosticCompletion', async () => {
    Logger.info('Diagnostic completion command triggered');
    const settings = await settingsManager.getSettings();
    const isProviderAvailable = aiService.isAvailable();
    const completionEnabled = settings.completion?.enabled ?? true;
    
    const diagnosticInfo = [
      `AI Provider Available: ${isProviderAvailable ? '✅' : '❌'}`,
      `Completion Enabled: ${completionEnabled ? '✅' : '❌'}`,
      `Selected Provider: ${settings.selectedProvider}`,
      `Selected Model: ${settings.selectedModel}`,
      `VS Code Inline Suggest: ${vscode.workspace.getConfiguration('editor').get('inlineSuggest.enabled') ? '✅' : '❌'}`,
      ``,
      `Debug: Check VS Code Output > Extension Host for detailed logs`
    ].join('\n');
    
    vscode.window.showInformationMessage('Xyne Completion Diagnostics', { detail: diagnosticInfo, modal: true });
  });

  let testCompletionCommand = vscode.commands.registerCommand('xyne.testCompletion', async () => {
    Logger.info('Test completion command triggered');
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      vscode.window.showErrorMessage('No active editor');
      return;
    }

    const document = editor.document;
    const position = editor.selection.active;
    
    Logger.info(`Testing completion at ${position.line}:${position.character} in ${document.languageId}`);
    
    // Manually trigger completion provider
    try {
      const items = await completionProvider.provideInlineCompletionItems(
        document, 
        position, 
        { triggerKind: vscode.InlineCompletionTriggerKind.Automatic, selectedCompletionInfo: undefined },
        new vscode.CancellationTokenSource().token
      );
      
      const itemCount = Array.isArray(items) ? items.length : items?.items?.length || 0;
      vscode.window.showInformationMessage(`Test completion returned ${itemCount} items`);
    } catch (error) {
      Logger.error(error, 'Error in test completion');
      vscode.window.showErrorMessage(`Test completion failed: ${error instanceof Error ? error.message : error}`);
    }
  });


  // Register inline completion provider for all languages
  Logger.info('Registering inline completion provider...');
  const completionProviderRegistration = vscode.languages.registerInlineCompletionItemProvider(
    [
      { language: 'javascript' },
      { language: 'typescript' },
      { language: 'javascriptreact' },
      { language: 'typescriptreact' },
      { language: 'python' },
      { language: 'json' },
      { pattern: '**' }
    ],
    completionProvider
  );
  Logger.info('Inline completion provider registered successfully');

  // Register the indexCodebase command
  let indexCodebaseCommand = vscode.commands.registerCommand('xyne.indexCodebase', async (options?: { isBackgroundTask?: boolean }) => {
    const folderPath = vscode.workspace.rootPath || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
    const outputChannel = vscode.window.createOutputChannel('Xyne Indexing');
    if (!options?.isBackgroundTask) {
      outputChannel.show(true);
    }

    const maxRetries = 1;

    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        outputChannel.appendLine(`\n--- Indexing Attempt ${attempt} ---`);

        const modelPath = await ensureModelDownloaded(context.extensionPath, outputChannel);
        const ingestScriptPath = path.join(context.extensionPath, 'dist', 'ingest-hybrid-script.js');
        const nodePath = process.execPath;

        outputChannel.appendLine(`Indexing folder: ${folderPath}`);
        outputChannel.appendLine(`Spawning hybrid indexing script: ${ingestScriptPath}`);

        const exitCode = await new Promise<number>((resolve, reject) => {
          // Update status bar to show indexing started
          updateIndexingProgress(0);
          
          const child = spawn(nodePath, [ingestScriptPath, folderPath, modelPath, context.extensionPath], {
            stdio: 'pipe',
            shell: false,
            cwd: context.extensionPath,
          });

          let stderr = '';
          let stdout = '';
          let hasSuccessIndicators = false;
          
          child.stdout.on('data', (data) => {
            const output = data.toString();
            stdout += output;
            outputChannel.appendLine(output);
            
            // Check for success indicators
            if (output.includes('Successfully saved') || 
                output.includes('HYBRID INDEXING COMPLETED') ||
                output.includes('Indexing completed successfully')) {
              hasSuccessIndicators = true;
            }
            
            // Parse progress messages from the output
            // Look for patterns like "PROGRESS:45" or "Processing file 10 of 50"
            const progressMatch = output.match(/PROGRESS[:\s]*(\d+)/i) || 
                                output.match(/(\d+)%/) ||
                                output.match(/Processing file (\d+) of (\d+)/);
            
            if (progressMatch) {
              let percentage: number;
              
              if (progressMatch[0].includes('Processing file') && progressMatch[2]) {
                // Calculate percentage from "Processing file X of Y"
                const current = parseInt(progressMatch[1]);
                const total = parseInt(progressMatch[2]);
                percentage = Math.round((current / total) * 100);
              } else {
                percentage = parseInt(progressMatch[1]);
              }
              
              // Extract message if present
              const messageMatch = output.match(/PROGRESS:\d+:(.+)/);
              const message = messageMatch ? messageMatch[1].trim() : '';
              
              updateIndexingProgress(percentage, message);
            }
            
            // Remove individual file processing messages - only show percentage progress
          });
          
          child.stderr.on('data', (data) => {
            const stderrData = data.toString();
            stderr += stderrData;
            outputChannel.appendLine(stderrData);
            
            // Check if this is just a LanceDB warning about nprobes (which is harmless)
            if (stderrData.includes('nprobes is not set') || stderrData.includes('WARN')) {
              // This is just a warning, not a real error
              return;
            }
          });
          
          child.on('error', reject);
          child.on('close', (code) => {
            // Check if stderr only contains harmless warnings
            const hasOnlyWarnings = stderr.includes('nprobes is not set') || 
                                  stderr.includes('WARN') ||
                                  stderr.trim() === '';
            
            if (code === 0 || (hasSuccessIndicators && hasOnlyWarnings)) {
              updateIndexingProgress(100);
              resolve(0);
            } else {
              // Reset indexing state on error
              isIndexingActive = false;
              currentIndexingPercentage = -1;
              
              // Close progress notification on error
              if (progressResolve) {
                progressResolve();
                progressReporter = null;
                progressResolve = null;
              }
              
              xyneStatusBar.text = "$(sparkle) Xyne";
              xyneStatusBar.tooltip = "Xyne AI Assistant";
              reject(new Error(stderr || `Indexing process exited with code ${code}.`));
            }
          });
        });

        if (exitCode === 0) {
          outputChannel.appendLine('Indexing completed successfully.');
          vscode.window.showInformationMessage('Codebase indexing completed!');
          return;
        }
      } catch (err) {
        const message = err instanceof Error ? err.message : String(err);
        outputChannel.appendLine(`Attempt ${attempt} failed: ${message}`);

        if (attempt > maxRetries) {
          vscode.window.showErrorMessage('Codebase indexing failed. See output for details.');
          return;
        }

        outputChannel.appendLine('Retrying...');
        const modelPath = path.join(context.extensionPath, '.models', MODEL_FILENAME);
        if (fs.existsSync(modelPath)) {
          fs.unlinkSync(modelPath);
        }
      }
    }
  });

  const getFromIndexCommand = vscode.commands.registerCommand('xyne.getFromIndex', async () => {
    const query = await vscode.window.showInputBox({
      prompt: 'Enter your search query',
      placeHolder: 'e.g., How to create a new component?',
    });

    if (!query) {
      vscode.window.showInformationMessage('Query cancelled.');
      return;
    }

    const folderPath = vscode.workspace.rootPath || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
    const outputChannel = vscode.window.createOutputChannel('Xyne Search Results');
    outputChannel.show(true);
            outputChannel.appendLine(`Searching for: "${query}" in folder: ${folderPath}`);
            outputChannel.appendLine(`🔍 Starting hybrid search (will choose best available method)...`);

        const maxRetries = 1;

        for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
          try {
            outputChannel.appendLine(`\n--- Search Attempt ${attempt} ---`);

            const { queryHybrid } = await import('./embedding/hybrid-search.js');
            // Use workspace path for repo ID generation, but extension path for database location
            const results = await queryHybrid(query, folderPath, context.extensionPath);

            // Determine search type from results
            const searchType = results.length > 0 ? results[0].searchType : 'unknown';
            const searchTypeDisplay = searchType === 'qwen' ? '🧠 Qwen (Semantic)' : 
                                    searchType === 'bm25' ? '🚀 BM25 (Text-based)' : 
                                    '❓ Unknown';
            
            outputChannel.appendLine(`\n🎯 SEARCH COMPLETED`);
            outputChannel.appendLine(`🔍 Method Used: ${searchTypeDisplay}`);
            outputChannel.appendLine(`📊 Results Found: ${results.length}`);
            outputChannel.appendLine(`📁 Search Priority: ${searchType === 'qwen' ? 'Qwen (Priority 1)' : searchType === 'bm25' ? 'BM25 (Priority 2 - Qwen not available)' : 'Unknown'}`);
            outputChannel.appendLine(`\n📄 Results:\n`);
        results.forEach((result: any, index: number) => {
          outputChannel.appendLine(`--- Result ${index + 1} ---`);
          outputChannel.appendLine(`File: ${result.filePath}`);
          outputChannel.appendLine(`Chunk: ${result.chunkIndex + 1}/${result.totalChunks}`);
          outputChannel.appendLine(`Language: ${result.language}`);
          outputChannel.appendLine(`Search Type: ${result.searchType === 'qwen' ? '🧠 Qwen (Semantic)' : result.searchType === 'bm25' ? '🚀 BM25 (Text-based)' : result.searchType}`);
          outputChannel.appendLine(`Text: ${result.text.trim()}`);
          if (result._distance !== undefined) {
            outputChannel.appendLine(`Distance: ${result._distance.toFixed(4)}`);
          }
          if (result._score !== undefined) {
            outputChannel.appendLine(`Score: ${result._score.toFixed(4)}`);
          }
          outputChannel.appendLine('');
        });
        
        outputChannel.appendLine(`\n📋 SEARCH SUMMARY`);
        outputChannel.appendLine(`🔍 Method: ${searchTypeDisplay}`);
        outputChannel.appendLine(`📊 Total Results: ${results.length}`);
        outputChannel.appendLine(`⚡ Performance: ${searchType === 'qwen' ? 'Semantic search (slower but more accurate)' : searchType === 'bm25' ? 'Text-based search (faster but less semantic)' : 'Unknown'}`);
        outputChannel.appendLine(`💡 Tip: ${searchType === 'qwen' ? 'Qwen provides semantic understanding of your query' : searchType === 'bm25' ? 'BM25 provides fast text matching - consider running Qwen indexing for semantic search' : 'Unknown search method'}`);
        
        vscode.window.showInformationMessage(`Found ${results.length} results using ${searchTypeDisplay}. See "Xyne Search Results" output.`);
        return;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err);
        outputChannel.appendLine(`Attempt ${attempt} failed: ${errorMessage}`);

        if (attempt > maxRetries) {
          vscode.window.showErrorMessage(`Search failed. See output for details.`);
          return;
        }
        outputChannel.appendLine('Retrying...');
      }
    }
  });

  // MCP Commands
  let mcpServersCommand = vscode.commands.registerCommand('xyne.mcpServers', async () => {
    Logger.info('MCP servers command triggered');
    provider.postMessage({ type: 'showMcpServers' });
  });

  let addMcpServerCommand = vscode.commands.registerCommand('xyne.addMcpServer', async () => {
    Logger.info('Add MCP server command triggered');
    provider.postMessage({ type: 'showAddMcpServer' });
  });

  // Hybrid search commands
  let checkIndexStatusCommand = vscode.commands.registerCommand('xyne.checkIndexStatus', async () => {
    const folderPath = vscode.workspace.rootPath || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
    const outputChannel = vscode.window.createOutputChannel('Xyne Index Status');
    outputChannel.show(true);

    try {
      // Add path debugging information
      const { sanitizePathToDirectoryName, DB_PATH } = await import('./embedding/lancedb-utils.js');
      const repoId = sanitizePathToDirectoryName(folderPath);
      const fs = require('fs');
      const path = require('path');
      
      outputChannel.appendLine(`Index Status for: ${folderPath}\n`);
      outputChannel.appendLine(`🔍 Database Path Information:`);
      outputChannel.appendLine(`  Workspace Path: ${folderPath}`);
      outputChannel.appendLine(`  Extension Path: ${context.extensionPath}`);
      outputChannel.appendLine(`  Sanitized Repo ID: ${repoId}`);
      
      // Check possible database locations
      const possiblePaths = [
        path.join(process.cwd(), '.embeddings', repoId),
        path.join(context.extensionPath, '.embeddings', repoId),
        path.join(folderPath, '.embeddings', repoId)
      ];
      
      outputChannel.appendLine(`  Possible Database Locations:`);
      possiblePaths.forEach((p, i) => {
        const exists = fs.existsSync(p);
        const dbPath = path.join(p, DB_PATH);
        const dbExists = fs.existsSync(dbPath);
        outputChannel.appendLine(`    ${i + 1}. ${p} (exists: ${exists ? '✅' : '❌'})`);
        if (exists) {
          outputChannel.appendLine(`       Database: ${dbPath} (exists: ${dbExists ? '✅' : '❌'})`);
          if (dbExists) {
            try {
              const tables = fs.readdirSync(dbPath);
              outputChannel.appendLine(`       Tables: ${tables.join(', ')}`);
            } catch (e) {
              outputChannel.appendLine(`       Tables: Error reading directory`);
            }
          }
        }
      });
      outputChannel.appendLine('');

      const { getIndexStatus } = await import('./embedding/hybrid-search.js');
      
      // Capture console output for debugging
      const originalConsoleLog = console.log;
      const logs: string[] = [];
      console.log = (...args: any[]) => {
        const message = args.map(arg => typeof arg === 'string' ? arg : JSON.stringify(arg)).join(' ');
        logs.push(message);
        originalConsoleLog(...args);
      };
      
      // Test console capture
      console.log('🧪 Test console capture');
      
      let status: any;
      try {
        // Use workspace path for repo ID generation, but extension path for database location
        outputChannel.appendLine(`🔍 Calling getIndexStatus with:`);
        outputChannel.appendLine(`  Folder Path: ${folderPath}`);
        outputChannel.appendLine(`  Extension Path: ${context.extensionPath}`);
        
        status = await getIndexStatus(folderPath, context.extensionPath);
        
        outputChannel.appendLine(`🔍 getIndexStatus completed successfully`);
        
        // Display captured logs
        outputChannel.appendLine(`🔍 Debug Information:`);
        logs.forEach(log => {
          // Show all logs that contain debugging information
          if (log.includes('🔍') || log.includes('📊') || log.includes('✅') || log.includes('❌') || log.includes('⏳') || log.includes('🧪')) {
            outputChannel.appendLine(`  ${log}`);
          }
        });
        outputChannel.appendLine('');
      } finally {
        // Restore original console.log
        console.log = originalConsoleLog;
      }

      outputChannel.appendLine(`📊 Index Availability:`);
      outputChannel.appendLine(`BM25 Index: ${status.bm25Available ? '✅ Available' : '❌ Not available'}`);
      if (status.bm25Stats) {
        outputChannel.appendLine(`  📄 ${status.bm25Stats.totalChunks} chunks, ${status.bm25Stats.totalFiles} files`);
      }
      outputChannel.appendLine(`Qwen Index: ${status.qwenAvailable ? '✅ Available' : '❌ Not available'}`);
      if (status.qwenStats) {
        outputChannel.appendLine(`  📄 ${status.qwenStats.totalChunks} chunks, ${status.qwenStats.totalFiles} files`);
      }

      if (status.bm25Available && status.qwenAvailable) {
        outputChannel.appendLine(`\n✅ Hybrid search is ready!`);
        outputChannel.appendLine(`🚀 BM25 provides fast text-based search`);
        outputChannel.appendLine(`🧠 Qwen provides semantic similarity search`);
      } else if (status.bm25Available) {
        outputChannel.appendLine(`\n✅ BM25 search is ready!`);
        outputChannel.appendLine(`⚠️  Qwen index is not available - semantic search will not work`);
      } else if (status.qwenAvailable) {
        outputChannel.appendLine(`\n✅ Qwen search is ready!`);
        outputChannel.appendLine(`⚠️  BM25 index is not available - fast text search will not work`);
      } else {
        outputChannel.appendLine(`\n❌ No indexes are available - search will not work`);
        outputChannel.appendLine(`💡 Run "Xyne: Index Codebase" to create indexes`);
      }

      vscode.window.showInformationMessage('Index status checked. See "Xyne Index Status" output.');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      outputChannel.appendLine(`Failed to check index status: ${errorMessage}`);
      vscode.window.showErrorMessage(`Failed to check index status: ${errorMessage}`);
    }
  });

  let searchBM25OnlyCommand = vscode.commands.registerCommand('xyne.searchBM25Only', async () => {
    const query = await vscode.window.showInputBox({
      prompt: 'Enter your search query (BM25 only)',
      placeHolder: 'e.g., How to create a new component?',
    });

    if (!query) {
      vscode.window.showInformationMessage('Query cancelled.');
      return;
    }

    const folderPath = vscode.workspace.rootPath || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
    const outputChannel = vscode.window.createOutputChannel('Xyne BM25 Search Results');
    outputChannel.show(true);
    outputChannel.appendLine(`BM25 search for: "${query}" in folder: ${folderPath}`);
    outputChannel.appendLine(`🔍 Using extension database at: ${context.extensionPath}`);

    try {
      const { HybridSearchService } = await import('./embedding/hybrid-search.js');
      // Use workspace path for repo ID generation, but extension path for database location
      const searchService = HybridSearchService.getInstance();
      const results = await searchService.query(query, folderPath, context.extensionPath, { forceBM25: true });

      outputChannel.appendLine(`\n🔍 Search Method: 🚀 BM25 (Text-based) - Forced`);
      outputChannel.appendLine(`Found ${results.length} results:\n`);
      results.forEach((result: any, index: number) => {
        outputChannel.appendLine(`--- Result ${index + 1} ---`);
        outputChannel.appendLine(`File: ${result.filePath}`);
        outputChannel.appendLine(`Chunk: ${result.chunkIndex + 1}/${result.totalChunks}`);
        outputChannel.appendLine(`Language: ${result.language}`);
        outputChannel.appendLine(`Search Type: 🚀 BM25 (Text-based)`);
        outputChannel.appendLine(`Text: ${result.text.trim()}`);
        if (result._score !== undefined) {
          outputChannel.appendLine(`Score: ${result._score.toFixed(4)}`);
        }
        outputChannel.appendLine('');
      });
      vscode.window.showInformationMessage(`Found ${results.length} results. See "Xyne BM25 Search Results" output.`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      outputChannel.appendLine(`BM25 search failed: ${errorMessage}`);
      vscode.window.showErrorMessage(`BM25 search failed: ${errorMessage}`);
    }
  });

  // Test command removed - shadow Git is now used instead of SCM

  context.subscriptions.push(
    vscode.window.registerWebviewViewProvider(XyneWebviewViewProvider.viewType, provider),
    completionProviderRegistration,
    focusCommand,
    settingsCommand,
    refreshProviderCommand,
    openTerminalCommand,
    // newConversationCommand,
    // listConversationsCommand,
    newChatCommand,
    showChatHistoryCommand,
    searchConversationsCommand,
    customInstructionsCommand,
    attachFilesCommand,
    copyLastResponseCommand,
    regenerateResponseCommand,
    showKeyboardShortcutsCommand,
    clearAttachedFilesCommand,
    statusBarClickCommand,
    toggleCompletionCommand,
    completionSettingsCommand,
  diagnosticCommand,
  xyneStatusBar,
  testCompletionCommand,
  completionStatusBar,
  indexCodebaseCommand,
  getFromIndexCommand,
  checkIndexStatusCommand,
  searchBM25OnlyCommand,
  mcpServersCommand,
  addMcpServerCommand,
  );

  // Automatically trigger indexing on startup if configured
  handleAutoIndexOnStartup();

  Logger.info('Xyne extension activated successfully');
}

function handleAutoIndexOnStartup() {
  const config = vscode.workspace.getConfiguration('xyne');
  const indexOnStartup = config.get('indexing.onStartup');

  if (indexOnStartup) {
    Logger.info('Auto-indexing on startup is enabled. Triggering codebase indexing.');
    vscode.commands.executeCommand('xyne.indexCodebase', { isBackgroundTask: true });
  } else {
    Logger.info('Auto-indexing on startup is disabled.');
  }
}

interface ChatSession {
  id: string;
  name: string;
  messages: any[];
  createdAt: Date;
  updatedAt: Date;
  provider?: string;
  model?: string;
}

// SCM State Management Interfaces
// Shadow Git manager for file change tracking
let shadowGitManager: ShadowGitManager | null = null;

class XyneWebviewViewProvider implements vscode.WebviewViewProvider {
  public static readonly viewType = 'xyne.webview'; // This ID must match the one in package.json

  private _view?: vscode.WebviewView;
  private readonly _extensionUri: vscode.Uri;
  private readonly _context: vscode.ExtensionContext; // Store context for extensionMode access
  private readonly _aiService: AIService;
  private readonly _chatManager: ChatManager;
  private readonly _mcpHub: McpHub;
  private readonly _mcpToolIntegration: McpToolIntegration;
  private _conversationHistory: AwsBedrockMessage[] = []; // Keep for AWS Bedrock compatibility
  private _currentConversationId: string | null = null;
  private _commandBuffer: string[] = [];
  private _currentCommandIndex: number = -1;
  private _isCancelled: boolean = false; // Flag to track cancellation
  private _currentSessionId: string | null = null;
  private _currentStreamingText: string = ''; // Track current streaming content
  private _currentStreamingMessageId: string | null = null; // Track current streaming message ID
  // Streaming timeout management
  private _timeoutManager: StreamingTimeoutManager;
  // Shadow Git manager will be initialized in activate()

  constructor(extensionUri: vscode.Uri, context: vscode.ExtensionContext, aiService: AIService, chatManager: ChatManager, mcpHub: McpHub, mcpToolIntegration: McpToolIntegration) {
    this._extensionUri = extensionUri;
    this._context = context;
    this._aiService = aiService;
    this._chatManager = chatManager;
    this._mcpHub = mcpHub;
    this._mcpToolIntegration = mcpToolIntegration;

    // Initialize streaming timeout manager
    this._timeoutManager = new StreamingTimeoutManager();

    // Set up chat manager event listeners
    this.setupChatManagerEvents();
  }

  // Set up event listeners for chat manager
  private setupChatManagerEvents() {
    this._chatManager.onConversationsChanged(async (conversations) => {
      Logger.info(`🔄 Conversations changed, sending ${conversations.length} to webview`);
      if (this._view) {
        this._view.webview.postMessage({
          type: 'conversationsLoaded',
          conversations: conversations
        });
      }
    });

    this._chatManager.onActiveConversationChanged((conversationId) => {
      Logger.info(`🔄 Active conversation changed to: ${conversationId}`);
      Logger.info(`📝 Current _conversationHistory before clear: ${this._conversationHistory.length} messages`);
      this._currentConversationId = conversationId;
      this.loadConversationHistory(conversationId);
    });

    this._chatManager.onMessagesChanged((data) => {
      Logger.info(`📬 Messages changed for conversation: ${data.conversationId} (current: ${this._currentConversationId})`);
      if (data.conversationId === this._currentConversationId) {
        Logger.info(`📝 Syncing conversation history: ${data.messages.length} messages`);
        this.syncConversationHistory(data.messages);

        // Send messages to webview
        if (this._view) {
          Logger.info(`📤 Sending ${data.messages.length} messages to webview for conversation ${data.conversationId}`);
          this._view.webview.postMessage({
            type: 'messagesChanged',
            messages: data.messages,
            conversationId: data.conversationId
          });
        }
      } else {
        Logger.info(`⚠️ Ignoring messages for different conversation: ${data.conversationId} vs ${this._currentConversationId}`);
      }
    });

    // Auto-compact / Summarization event listeners
    this._chatManager.onSummarizationProgress((data) => {
      Logger.info(`🔄 Summarization progress for conversation ${data.conversationId}: ${data.progress.stage}`);
      if (this._view && data.conversationId === this._currentConversationId) {
        this._view.webview.postMessage({
          type: 'summarizationProgress',
          conversationId: data.conversationId,
          progress: data.progress
        });
      }
    });

    this._chatManager.onSummarizationComplete((data) => {
      Logger.info(`✅ Summarization completed for conversation ${data.conversationId}`);
      if (this._view && data.conversationId === this._currentConversationId) {
        this._view.webview.postMessage({
          type: 'summarizationComplete',
          conversationId: data.conversationId,
          result: data.result
        });
      }
    });

    this._chatManager.onError((error) => {
      Logger.error(error, 'ChatManager error');
      if (this._view) {
        this._view.webview.postMessage({
          type: 'summarizationFailed',
          error: error.message
        });
      }
    });
  }

  // Convert ChatMessage to AWS Bedrock Message format
  private convertToBedrockMessage(chatMessage: Message): AwsBedrockMessage {
    const content: any[] = [];

    if (chatMessage.content) {
      content.push({ text: chatMessage.content });
    }

    return {
      role: chatMessage.type === MessageType.USER ? "user" as const : "assistant" as const,
      content: content
    };
  }

  // Sync persistent chat messages to in-memory format for AWS Bedrock
  private async syncConversationHistory(messages: Message[]) {
    Logger.info(`🔄 syncConversationHistory: Converting ${messages.length} messages to Bedrock format`);
    this._conversationHistory = messages.map(msg => this.convertToBedrockMessage(msg));
    Logger.info(`📝 _conversationHistory now has ${this._conversationHistory.length} messages`);
  }

  // Load conversation history for the current conversation
  private async loadConversationHistory(conversationId: string | null) {
    Logger.info(`📚 loadConversationHistory called for conversation: ${conversationId}`);

    // Always clear history first to prevent carryover
    this._conversationHistory = [];
    this._currentSessionId = null;
    Logger.info('Conversation history cleared.');
    Logger.info(`📝 Cleared conversation history`);

    if (!conversationId) {
      Logger.info(`📝 No conversation ID provided, keeping history empty`);
      return;
    }

    try {
      const messages = await this._chatManager.getMessages(conversationId);
      Logger.info(`📚 Retrieved ${messages.length} messages from ChatManager for conversation ${conversationId}`);
      await this.syncConversationHistory(messages);
      Logger.info(`📚 Loaded ${messages.length} messages for conversation ${conversationId}`);
    } catch (error) {
      Logger.error(error, 'Failed to load conversation history');
      this._conversationHistory = [];
    }
  }

  // Helper to clear history - DON'T auto-create new conversation
  private async clearConversationHistory() {
    // Just clear the history, don't create a new conversation automatically
    this._conversationHistory = [];
    this._currentConversationId = null;
    Logger.info('Conversation history cleared, no active conversation');
    this._currentSessionId = null;
    Logger.info('Conversation history cleared.');
  }


  // Session storage methods
  private getStorageKey(): string {
    return 'xyne.chatSessions';
  }

  private async getChatSessions(): Promise<ChatSession[]> {
    try {
      const sessionsData = this._context.workspaceState.get<ChatSession[]>(this.getStorageKey(), []);
      // Convert string dates back to Date objects
      return sessionsData.map(session => ({
        ...session,
        createdAt: new Date(session.createdAt),
        updatedAt: new Date(session.updatedAt)
      }));
    } catch (error) {
      Logger.error(error, 'Failed to get chat sessions');
      return [];
    }
  }

  private async saveChatSession(session: ChatSession): Promise<void> {
    try {
      const sessions = await this.getChatSessions();
      const existingIndex = sessions.findIndex(s => s.id === session.id);
      
      if (existingIndex >= 0) {
        sessions[existingIndex] = session;
      } else {
        sessions.unshift(session); // Add new sessions to the beginning
      }

      // Keep only the last 50 sessions to prevent excessive storage
      const trimmedSessions = sessions.slice(0, 50);
      
      await this._context.workspaceState.update(this.getStorageKey(), trimmedSessions);
      Logger.info(`Chat session saved: ${session.name} (${session.id})`);
    } catch (error) {
      Logger.error(error, 'Failed to save chat session');
    }
  }

  private async deleteChatSession(sessionId: string): Promise<void> {
    try {
      const sessions = await this.getChatSessions();
      const filteredSessions = sessions.filter(s => s.id !== sessionId);
      await this._context.workspaceState.update(this.getStorageKey(), filteredSessions);
      Logger.info(`Chat session deleted: ${sessionId}`);
    } catch (error) {
      Logger.error(error, 'Failed to delete chat session');
    }
  }

  private async loadChatSession(sessionId: string): Promise<ChatSession | null> {
    try {
      const sessions = await this.getChatSessions();
      const session = sessions.find(s => s.id === sessionId);
      if (session) {
        this._currentSessionId = sessionId;
        Logger.info(`Chat session loaded: ${session.name} (${sessionId})`);
      }
      return session || null;
    } catch (error) {
      Logger.error(error, 'Failed to load chat session');
      return null;
    }
  }

  // Create VS Code diff view using built-in diff command
  private async createVSCodeDiff(filePath: string, originalContent: string, newContent: string) {
    try {
      // Create temporary files for diff
      const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
      if (!workspaceFolder) {
        return;
      }

      const tempDir = vscode.Uri.joinPath(workspaceFolder.uri, '.vscode', 'temp');
      await vscode.workspace.fs.createDirectory(tempDir);

      const originalUri = vscode.Uri.joinPath(tempDir, `${filePath.replace(/[\/\\]/g, '_')}.original`);
      const newUri = vscode.Uri.joinPath(tempDir, `${filePath.replace(/[\/\\]/g, '_')}.new`);

      // Write temporary files
      await vscode.workspace.fs.writeFile(originalUri, Buffer.from(originalContent, 'utf8'));
      await vscode.workspace.fs.writeFile(newUri, Buffer.from(newContent, 'utf8'));

      // Open diff using VS Code's built-in diff command
      await vscode.commands.executeCommand('vscode.diff', originalUri, newUri, `${filePath} (Before ↔ After)`);

      Logger.info(`📊 Created VS Code diff for ${filePath}`);
    } catch (error) {
      Logger.error(error, 'Failed to create VS Code diff');
    }
  }

  // Helper to format tool results in a human-readable way for the AI
  private formatToolResult(toolCall: ToolCall, toolResult: ToolResult): string {
    if (toolResult.error) {
      return `Error executing ${toolCall.name}: ${toolResult.error}`;
    }

    const result = toolResult.result;

    switch (toolCall.name) {
      case 'read_file':
        if (result?.content) {
          return `Successfully read file '${result.path}':\n\n\`\`\`\n${result.content}\n\`\`\`\n\n(File size: ${result.size} bytes)`;
        }
        return `Successfully read file '${result?.path}' but content was empty.`;

      case 'write_to_file':
        const diffInfo = result?.diffSummary ? ` (${result.diffSummary})` : '';
        const stagedInfo = result?.staged ? ' (staged via SCM)' : '';

        // Send diff data to webview for Monaco Editor display with accept/reject buttons
        if (result?.originalContent && result?.newContent && result?.path) {
          // Use tool call ID as change ID for shadow Git tracking
          const changeId = toolCall.id;
          
          const diffMessage = {
            type: 'showFileChangeReview',
            filePath: result.path,
            originalContent: result.originalContent,
            newContent: result.newContent,
            diffSummary: result.diffSummary || 'File modified',
            timestamp: Date.now(), // Add timestamp to associate with correct message
            staged: result?.staged || false,
            changeId: changeId // Include change ID for shadow Git tracking
          };
          Logger.info(`📤 Sending file change review message to webview: ${result.path} with change ID: ${changeId}`);
          this._view?.webview.postMessage(diffMessage);

          // PERSISTENCE FIX: Add file change review message to conversation history
          if (this._currentConversationId) {
            const fileChangeReviewMessage: Message = {
              id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              timestamp: Date.now(),
              type: MessageType.ASSISTANT,
              content: `📝 **File Change Review:** \`${result.path}\`\n\n${result.diffSummary || 'File changes tracked via shadow Git'}`,
              metadata: {
                fileChangeReview: {
                  filePath: result.path,
                  originalContent: result.originalContent,
                  newContent: result.newContent,
                  diffSummary: result.diffSummary || '',
                  language: result.language,
                  staged: result.staged,
                  changeId: changeId
                }
              }
            };
            // Insert after the tool call message if possible
            this._chatManager.getMessages(this._currentConversationId!)
              .then(messages => {
                // Find the tool call message by matching tool_executions[].toolCall.id === changeId
                const toolCallMsg = messages.find(m => Array.isArray(m.metadata?.tool_executions) && m.metadata.tool_executions.some(te => te.toolCall?.id === changeId));
                if (toolCallMsg) {
                  this._chatManager.addMessageAfter(this._currentConversationId!, toolCallMsg.id, fileChangeReviewMessage)
                    .then(async () => {
                      Logger.info(`💾 File change review message inserted after tool call: ${result.path}`);
                      // Log the full message list after insertion
                      const allMessages = await this._chatManager.getMessages(this._currentConversationId!);
                      Logger.info(`[DEBUG] Conversation messages after file change review insert: ` + JSON.stringify(allMessages.map(m => ({id: m.id, type: m.type, content: m.content, metadata: m.metadata}))));
                      this._chatManager.emitMessagesChanged(this._currentConversationId!);
                    })
                    .catch(error => {
                      Logger.error(error, `Failed to insert file change review message for: ${result.path}`);
                    });
                } else {
                  // Fallback: append to end
                  this._chatManager.addMessage(this._currentConversationId!, fileChangeReviewMessage)
                    .then(async () => {
                      Logger.info(`💾 File change review message appended to conversation: ${result.path}`);
                      // Log the full message list after insertion
                      const allMessages = await this._chatManager.getMessages(this._currentConversationId!);
                      Logger.info(`[DEBUG] Conversation messages after file change review append: ` + JSON.stringify(allMessages.map(m => ({id: m.id, type: m.type, content: m.content, metadata: m.metadata}))));
                      this._chatManager.emitMessagesChanged(this._currentConversationId!);
                    })
                    .catch(error => {
                      Logger.error(error, `Failed to persist file change review message for: ${result.path}`);
                    });
                }
              });
          }
        }

        return `Successfully staged ${result?.bytesWritten || 0} bytes to file '${result?.path}'${diffInfo}${stagedInfo}.`;

      case 'list_files':
        if (result?.files && Array.isArray(result.files)) {
          const fileCount = result.files.length;
          if (fileCount === 0) {
            return `No files found in the specified directory.`;
          }
          const fileList = result.files.map((f: any) => `- ${f.path}`).join('\n');
          return `Found ${fileCount} files:\n${fileList}`;
        }
        return `Directory listing completed but no files were returned.`;

      case 'search_files':
        if (result?.results && Array.isArray(result.results)) {
          const totalMatches = result.totalMatches || 0;
          if (totalMatches === 0) {
            return `No matches found for pattern '${result.pattern}'.`;
          }

          let searchSummary = `Found ${totalMatches} matches for pattern '${result.pattern}':`;

          result.results.forEach((fileResult: any) => {
            searchSummary += `\n\nFile: ${fileResult.file}`;
            fileResult.matches.forEach((match: any) => {
              searchSummary += `\n  Line ${match.line}: ${match.text}`;
            });
          });

          if (result.truncated) {
            searchSummary += `\n\n(Results truncated - showing first ${totalMatches} matches)`;
          }

          return searchSummary;
        }
        return `Search completed for pattern '${toolCall.parameters.pattern}' but no results were returned.`;

      case 'multi_edit':
        const multiEditDiffInfo = result?.diffSummary ? ` (${result.diffSummary})` : '';
        const multiEditStagedInfo = result?.staged ? ' (staged via SCM)' : '';

        // Send diff data to webview if available with accept/reject buttons
        if (result?.originalContent && result?.newContent && result?.path) {
          const changeId = toolCall.id; // Use tool call ID as change ID
          const diffMessage = {
            type: 'showFileChangeReview',
            filePath: result.path,
            originalContent: result.originalContent,
            newContent: result.newContent,
            diffSummary: result.diffSummary || 'Multiple edits applied',
            timestamp: Date.now(), // Add timestamp to associate with correct message
            staged: result?.staged || false,
            changeId: changeId // Include change ID for tracking
          };
          Logger.info(`📤 Sending file change review message to webview: ${result.path} with change ID: ${changeId}`);
          this._view?.webview.postMessage(diffMessage);

          // PERSISTENCE FIX: Add file change review message to conversation history
          if (this._currentConversationId) {
            const fileChangeReviewMessage: Message = {
              id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              timestamp: Date.now(),
              type: MessageType.ASSISTANT,
              content: `📝 **File Change Review:** \`${result.path}\`\n\n${result.diffSummary || 'Multiple edits applied'}`,
              metadata: {
                fileChangeReview: {
                  filePath: result.path,
                  originalContent: result.originalContent,
                  newContent: result.newContent,
                  diffSummary: result.diffSummary || 'Multiple edits applied',
                  language: undefined, // Could be determined from file extension
                  staged: result?.staged || false,
                  changeId: changeId
                }
              }
            };
            // Insert after the tool call message if possible
            this._chatManager.getMessages(this._currentConversationId!)
              .then(messages => {
                // Find the tool call message by matching tool_executions[].toolCall.id === changeId
                const toolCallMsg = messages.find(m => Array.isArray(m.metadata?.tool_executions) && m.metadata.tool_executions.some(te => te.toolCall?.id === changeId));
                if (toolCallMsg) {
                  this._chatManager.addMessageAfter(this._currentConversationId!, toolCallMsg.id, fileChangeReviewMessage)
                    .then(async () => {
                      Logger.info(`💾 File change review message inserted after tool call: ${result.path}`);
                      // Log the full message list after insertion
                      const allMessages = await this._chatManager.getMessages(this._currentConversationId!);
                      Logger.info(`[DEBUG] Conversation messages after file change review insert: ` + JSON.stringify(allMessages.map(m => ({id: m.id, type: m.type, content: m.content, metadata: m.metadata}))));
                      this._chatManager.emitMessagesChanged(this._currentConversationId!);
                    })
                    .catch(error => {
                      Logger.error(error, `Failed to insert file change review message for: ${result.path}`);
                    });
                } else {
                  // Fallback: append to end
                  this._chatManager.addMessage(this._currentConversationId!, fileChangeReviewMessage)
                    .then(async () => {
                      Logger.info(`💾 File change review message appended to conversation: ${result.path}`);
                      // Log the full message list after insertion
                      const allMessages = await this._chatManager.getMessages(this._currentConversationId!);
                      Logger.info(`[DEBUG] Conversation messages after file change review append: ` + JSON.stringify(allMessages.map(m => ({id: m.id, type: m.type, content: m.content, metadata: m.metadata}))));
                      this._chatManager.emitMessagesChanged(this._currentConversationId!);
                    })
                    .catch(error => {
                      Logger.error(error, `Failed to persist file change review message for: ${result.path}`);
                    });
                }
              });
          }
        }

        return `Successfully applied ${result?.totalReplacements || 0} edits to file '${result?.path}'${multiEditDiffInfo}${multiEditStagedInfo}. ${result?.changesSummary || ''}`;

      default:
        // Fallback for unknown tools
        return `Tool '${toolCall.name}' executed successfully: ${JSON.stringify(result)}`;
    }
  }

  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    viewResolveContext: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken,
  ) {
    this._view = webviewView;

    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [
        vscode.Uri.joinPath(this._extensionUri, "webview-ui", "dist")
      ]
    };

    // Set context for when clause
    vscode.commands.executeCommand('setContext', 'xyne.chatViewFocused', true);
    
    // Clear context when view loses focus
    webviewView.onDidChangeVisibility(() => {
      vscode.commands.executeCommand('setContext', 'xyne.chatViewFocused', webviewView.visible);
    });

    this.setWebviewHtml(webviewView.webview);
    this.setupMessageHandling(webviewView.webview);
  }

  // Public method for sending messages to webview (used by keyboard shortcuts)
  public postMessage(message: any) {
    if (this._view) {
      this._view.webview.postMessage(message);
    }
  }

  private setupMessageHandling(webview: vscode.Webview) {
    webview.onDidReceiveMessage(async (message) => {
      try {
        switch (message.type) {
          case 'sendMessage':
            // Use sessionId from payload if provided
            if (message.sessionId) {
              this._currentConversationId = message.sessionId;
            } else if (!this._currentConversationId) {
              // If no sessionId is provided and no current conversation, create a new one
              this._currentConversationId = await this._chatManager.createConversation();
              // Optionally, notify the frontend of the new sessionId here
              if (this._view) {
                this._view.webview.postMessage({
                  type: 'newSessionStarted',
                  sessionId: this._currentConversationId
                });
              }
            }
            // Clear any existing streaming timeout when new message is sent
            this._timeoutManager.stop();
            
            await this.handleChatMessage(webview, message.content, false, message.customInstructions, message.structuredContent);
            break;
          case 'refreshProvider':
            await this._aiService.refreshProvider();
            webview.postMessage({
              type: 'providerRefreshed',
              isAvailable: this._aiService.isAvailable()
            });
            break;
          case 'validateConfiguration':
            await this.handleValidateConfiguration(webview);
            break;
          case 'getAvailableModels':
            await this.handleGetAvailableModels(webview);
            break;
          case 'changeModel':
            await this.handleChangeModel(webview, message.model);
            break;
          case 'testModelCredentials':
            await this.handleTestModelCredentials(webview, message.model);
            break;
          case 'closePanel':
            await this.handleClosePanel();
            break;
          case 'openSettings':
            await this.handleOpenSettings();
            break;
          case 'openImageInVSCode':
            await this.handleOpenImageInVSCode(message.imageUrl, message.imageName);
            break;
          case 'openTerminal':
            await this.handleOpenTerminal(message.command);
            break;
          case 'loadConversations':
            await this.handleLoadConversations(webview);
            break;
          case 'switchConversation':
            await this.handleSwitchConversation(webview, message.conversationId);
            break;
          case 'openFullHistory':
            await this.handleOpenFullHistory();
            break;
          case 'renameConversation':
            if (message.conversationId && message.newTitle) {
              await this.handleRenameConversation(webview, message.conversationId, message.newTitle);
            }
            break;
          case 'deleteConversation':
            if (message.conversationId) {
              await this.handleDeleteConversation(webview, message.conversationId);
            }
            break;
          case 'newConversation':
            // Clear current conversation - let handleChatMessage create new one when user sends first message
            this._currentConversationId = null;
            this._conversationHistory = [];

            // Clear the webview and show fresh state
            if (this._view) {
              this._view.webview.postMessage({
                type: 'conversationCreated',
                conversationId: null // null indicates fresh start
              });
            }

            Logger.info('🆕 Cleared current conversation - ready for new chat');
            break;
          case 'stopExecution':
            this.handleStopExecution(webview);
            break;
          case 'runCode':
            await this.handleRunCode(webview, message.code, message.language);
            break;
          case 'createFile':
            await this.handleCreateFile(webview, message.content, message.filename, message.language);
            break;
          case 'getChatSessions':
            await this.handleGetChatSessions(webview);
            break;
          case 'saveChatSession':
            await this.handleSaveChatSession(webview, message.session);
            break;
          case 'loadChatSession':
            await this.handleLoadChatSession(webview, message.sessionId, message.messages);
            break;
          case 'deleteChatSession':
            await this.handleDeleteChatSession(webview, message.sessionId);
            break;
          case 'startNewSession':
            await this.handleStartNewSession(webview);
            break;
          // Keyboard shortcut handlers
          case 'newChat':
            webview.postMessage({ type: 'triggerNewChat' });
            break;
          case 'showChatHistory':
            webview.postMessage({ type: 'triggerShowChatHistory' });
            break;
          case 'searchConversations':
            webview.postMessage({ type: 'triggerSearchConversations' });
            break;
          case 'customInstructions':
            webview.postMessage({ type: 'triggerCustomInstructions' });
            break;
          case 'attachFiles':
            webview.postMessage({ type: 'triggerAttachFiles' });
            break;
          case 'copyLastResponse':
            webview.postMessage({ type: 'triggerCopyLastResponse' });
            break;
          case 'regenerateResponse':
            webview.postMessage({ type: 'triggerRegenerateResponse' });
            break;
          case 'showKeyboardShortcuts':
            webview.postMessage({ type: 'triggerShowKeyboardShortcuts' });
            break;
          case 'clearAttachedFiles':
            webview.postMessage({ type: 'triggerClearAttachedFiles' });
            break;
          case 'getMcpServers':
            await this.handleGetMcpServers(webview);
            break;
          case 'toggleMcpServer':
            await this.handleToggleMcpServer(webview, message.serverName);
            break;
          case 'removeMcpServer':
            await this.handleRemoveMcpServer(webview, message.serverName);
            break;
          case 'addMcpServer':
            await this.handleAddMcpServer(webview, message.serverConfig);
            break;
          case 'updateMcpServer':
            await this.handleUpdateMcpServer(webview, message.serverConfig);
            break;
          case 'showAddMcpServer':
            // This is now handled by the AddMcpServerModal in the webview
            webview.postMessage({ type: 'showAddMcpServer' });
            break;
          case 'openMcpSettings':
            await this.handleOpenMcpSettings();
            break;
          case 'getCostSettings':
            await this.handleGetCostSettings(webview);
            break;
          case 'acceptFileChange':
            await this.handleAcceptFileChange(webview, message.filePath, message.originalContent, message.newContent, message.changeId);
            break;
          case 'rejectFileChange':
            await this.handleRejectFileChange(webview, message.filePath, message.originalContent, message.changeId, message.targetContent);
            break;
          case 'restoreCheckpoint': {
            Logger.info(`🔄 Restore checkpoint requested for messageId: ${message.messageId}`);
            Logger.info(`🔍 [EXTENSION_DEBUG] Starting restore checkpoint process for messageId: ${message.messageId}`);
            
            if (!this._currentConversationId) {
              Logger.warn('No active conversation for restoreCheckpoint');
              Logger.error(`🔍 [EXTENSION_DEBUG] _currentConversationId is null`);
              return;
            }
            
            Logger.info(`🔍 [EXTENSION_DEBUG] Current conversation ID: ${this._currentConversationId}`);
            
            const conversation = await this._chatManager.getActiveConversation();
            if (!conversation) {
              Logger.warn('No active conversation loaded for restoreCheckpoint');
              Logger.error(`🔍 [EXTENSION_DEBUG] No conversation returned from getActiveConversation`);
              return;
            }
            
            Logger.info(`🔍 [EXTENSION_DEBUG] Conversation loaded, message count: ${conversation.messages.length}`);
            Logger.info(`🔍 [EXTENSION_DEBUG] Message IDs: ${conversation.messages.map(m => m.id).join(', ')}`);
            
            const msgIdx = conversation.messages.findIndex(m => String(m.id) === String(message.messageId));
            if (msgIdx === -1) {
              Logger.warn('Message id not found in conversation for restoreCheckpoint');
              Logger.error(`🔍 [EXTENSION_DEBUG] Message ID ${message.messageId} not found in conversation`);
              Logger.info(`🔍 [EXTENSION_DEBUG] Available message IDs: ${conversation.messages.map(m => m.id).join(', ')}`);
              webview.postMessage({ type: 'checkpointRestoreFailed', messageId: message.messageId, error: 'Checkpoint message not found. It may have already been restored or removed.' });
              return;
            }
            
            Logger.info(`🔍 [EXTENSION_DEBUG] Found message at index: ${msgIdx}`);
            const checkpointId = conversation.messages[msgIdx].id;
            const checkpointContent = conversation.messages[msgIdx].content;
            Logger.info(`🔍 [EXTENSION_DEBUG] Checkpoint ID: ${checkpointId}`);
            Logger.info(`🔍 [EXTENSION_DEBUG] Checkpoint content length: ${checkpointContent.length}`);
            
            // Truncate conversation to just before the checkpoint message (exclusive) - remove the checkpoint message
            Logger.info(`🔍 [EXTENSION_DEBUG] Truncating conversation to index: ${msgIdx} (exclusive)`);
            if (msgIdx === 0) {
              // If checkpoint is the first message, clear all messages
              await this._chatManager.truncateConversationToIndex(-1);
              Logger.info(`🔍 [EXTENSION_DEBUG] Checkpoint is first message, cleared all messages`);
            } else {
              // Otherwise truncate to just before the checkpoint message
              await this._chatManager.truncateConversationToIndex(msgIdx - 1);
              Logger.info(`🔍 [EXTENSION_DEBUG] Truncated to just before checkpoint message`);
            }
            Logger.info(`🔍 [EXTENSION_DEBUG] Conversation truncated successfully`);
            
            // Restore workspace files using checkpoint-level file states
            if (shadowGitManagerFactory) {
              Logger.info(`🔍 [EXTENSION_DEBUG] ShadowGitManagerFactory is available`);
              const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
              if (workspaceRoot) {
                Logger.info(`🔍 [EXTENSION_DEBUG] Workspace root: ${workspaceRoot}`);
                Logger.info(`🔍 [EXTENSION_DEBUG] Getting shadow Git manager for conversation: ${this._currentConversationId}`);
                const manager = await shadowGitManagerFactory.getManager(this._currentConversationId);
                Logger.info(`🔍 [EXTENSION_DEBUG] Shadow Git manager obtained, calling restoreToCheckpoint...`);
                await manager.restoreToCheckpoint(checkpointId, workspaceRoot);
                Logger.info('✅ Workspace restored to checkpoint');
              } else {
                Logger.warn('No workspace root found for restoreCheckpoint');
                Logger.error(`🔍 [EXTENSION_DEBUG] No workspace root found`);
              }
            } else {
              Logger.warn('ShadowGitManagerFactory not initialized for restoreCheckpoint');
              Logger.error(`🔍 [EXTENSION_DEBUG] ShadowGitManagerFactory is null`);
            }
            
            // Notify webview to refresh chat view and set input
            Logger.info(`🔍 [EXTENSION_DEBUG] Sending checkpointRestored message to webview`);
            webview.postMessage({ type: 'checkpointRestored', messageId: message.messageId, content: checkpointContent });
            
            // Emit messagesChanged with the updated messages
            Logger.info(`🔍 [EXTENSION_DEBUG] Getting updated conversation and emitting messagesChanged`);
            const updatedConversation = await this._chatManager.getActiveConversation();
            if (updatedConversation) {
              Logger.info(`🔍 [EXTENSION_DEBUG] Updated conversation message count: ${updatedConversation.messages.length}`);
              this._chatManager.emitMessagesChanged(updatedConversation.id);
              Logger.info(`🔍 [EXTENSION_DEBUG] messagesChanged event emitted`);
            } else {
              Logger.warn(`🔍 [EXTENSION_DEBUG] No updated conversation found after truncate`);
            }
            
            Logger.info(`🔍 [EXTENSION_DEBUG] Restore checkpoint process completed successfully`);
            break;
          }
          case 'openMermaidInEditor':
            await this.handleOpenMermaidInEditor(message.content, message.filename);
            break;
          case 'continueAfterTimeout':
            if (this._currentConversationId) {
              this._timeoutManager.resume();
              Logger.info('Resumed streaming after timeout');
            }
            break;
          case 'updateSettings':
            await this.handleUpdateSettings(webview, message.settings);
            break;
          default:
            Logger.warn(`Unknown message type: ${message.type} Error message: ${message.message}`);
        }
      } catch (error) {
        Logger.error(error, `Error handling webview message: ${message.type}`);
        webview.postMessage({
          type: 'messageError',
          error: error instanceof Error ? error.message : 'An unknown error occurred'
        });
      }
    });
  }

  private async handleChatMessage(webview: vscode.Webview, content: string, isFollowUpFromTool = false, customInstructions?: string, structuredContent?: any[]) {
    try {
      Logger.info(`📨 Received chat message: "${content.substring(0, 100)}..." (isFollowUp: ${isFollowUpFromTool})`);

      // Convert HTML content to plain text
      const plainText = content.replace(/<[^>]*>/g, '').trim();

      if (!plainText) {
        Logger.warn('⚠️ Empty message received, ignoring');
        return;
      }

      Logger.info(`�� Processing message: "${plainText.substring(0, 50)}..."`);

      // Ensure we have an active conversation
      if (!this._currentConversationId) {
        this._currentConversationId = await this._chatManager.createConversation();
        Logger.info(`🆕 Created new conversation: ${this._currentConversationId}`);
        
        // Update the tool executor with the conversation-specific shadow Git manager
        await this._aiService.updateToolExecutorForConversation(this._currentConversationId);
        Logger.info(`🔄 Updated tool executor for new conversation: ${this._currentConversationId}`);
      }

      // Persist user message (if not a follow-up from tool)
      if (!isFollowUpFromTool) {
        const userMessage: Message = {
          id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
          type: MessageType.USER,
          content: plainText,
          metadata: {
            attachedFiles: structuredContent && Array.isArray(structuredContent) 
              ? JSON.stringify(structuredContent.filter(block => block.image).map((block, index) => ({
                  name: `image_${Date.now()}_${index}.${block.image.format}`,
                  type: `image/${block.image.format}`,
                  content: `data:image/${block.image.format};base64,${block.image.source.bytes}`,
                  size: Math.round(block.image.source.bytes.length * 0.75), // Approximate size from base64
                  path: `image_${Date.now()}_${index}.${block.image.format}`
                })))
              : undefined
          }
        };

        // --- Start checkpoint for this user message ---
        if (shadowGitManagerFactory) {
          const manager = await shadowGitManagerFactory.getManager(this._currentConversationId);
          manager.startCheckpoint(userMessage.id);
          Logger.info(`🔄 Started checkpoint for user message: ${userMessage.id}`);
        }
        // --- End checkpoint start logic ---

        await this._chatManager.addMessage(this._currentConversationId, userMessage);

        // Also add to in-memory history for AWS Bedrock
        // Build content array with text and images if available
        const contentBlocks: any[] = [{ text: plainText }];
        
        // Add image blocks if structured content contains images
        if (structuredContent && Array.isArray(structuredContent)) {
          structuredContent.forEach((block) => {
            if (block.image) {
              // Convert base64 string to Uint8Array for AWS Bedrock
              const base64Data = block.image.source.bytes;
              const binaryString = atob(base64Data);
              const bytes = new Uint8Array(binaryString.length);
              for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
              }
              
              contentBlocks.push({
                image: {
                  format: block.image.format,
                  source: {
                    bytes: bytes
                  }
                }
              });
            }
          });
        }
        
        this._conversationHistory.push({
          role: "user" as const,
          content: contentBlocks,
        });
      }
      
      // Reset cancellation flag and send message start event
      this._isCancelled = false;
      webview.postMessage({
        type: 'messageStart',
        messageId: Date.now().toString()
      });
      webview.postMessage({
        type: 'streamingStart'
      });

      if (!this._aiService.isAvailable()) {
        Logger.warn('⚠️ AI service not available, sending fallback response');
        const errorText = "I'm not currently connected to an AI provider. Please check your settings.";

        webview.postMessage({
          type: 'messageChunk',
          text: errorText,
          isComplete: true,
        });

        // Persist error message
        const errorMessage: Message = {
          id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
          type: MessageType.ERROR,
          content: errorText,
          metadata: {}
        };

        await this._chatManager.addMessage(this._currentConversationId, errorMessage);

        this._conversationHistory.push({
          role: "assistant" as const,
          content: [{text: errorText}]
        });
        return;
      }

      Logger.info(`🚀 Starting AI conversation stream with history length: ${this._conversationHistory.length}`);

      let finalAssistantResponseText = '';
      let finalCost: number | undefined;
      let finalMetadata: any;

      // Create streaming message ID for tracking
      this._currentStreamingMessageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      this._currentStreamingText = '';
      // Start streaming timeout
      const timeoutSettings = this._chatManager.getStreamingTimeoutSettings();
      this._timeoutManager.start(timeoutSettings, {
        onTimeout: async (timeoutInfo) => {
          if (this._currentConversationId) {
            await this._chatManager.addMessage(this._currentConversationId, timeoutInfo.message);
          }
        }
      });

      for await (const chunk of this._aiService.converseStream(this._conversationHistory, {}, webview, customInstructions)) {
        // Check for cancellation
        if (this._isCancelled) {
          Logger.info('🛑 Execution cancelled by user - saving partial response');
          
          // Save partial response immediately if there's content
          if (this._currentStreamingText) {
            const partialAssistantMessage: Message = {
              id: this._currentStreamingMessageId,
              timestamp: Date.now(),
              type: MessageType.ASSISTANT,
              content: this._currentStreamingText,
              metadata: {
                isInterrupted: true,
                interruptedText: this._currentStreamingText,
                wasStreaming: true,
                interruptedAt: Date.now()
              }
            };

            await this._chatManager.addMessage(this._currentConversationId!, partialAssistantMessage);
            Logger.info(`💾 Saved partial streaming response: "${this._currentStreamingText.substring(0, 50)}..."`);
          }

          Logger.info('🛑 Execution cancelled by user');
          this._timeoutManager.stop();
          webview.postMessage({
            type: 'messageError',
            error: 'Execution cancelled by user'
          });
          return;
        }

        // Check for streaming pause
        if (this._timeoutManager.isPausedState()) {
          Logger.info('⏸️ Streaming paused due to timeout');
          break;
        }
        
        if (chunk.text) {
          // Accumulate the streamed text
          finalAssistantResponseText += chunk.text;
          this._currentStreamingText += chunk.text;

          // Stream text chunk to webview
          webview.postMessage({
            type: 'messageChunk',
            text: chunk.text,
            cost: chunk.cost,
          });
          Logger.info(`💬 Streaming chunk: "${chunk.text.substring(0, 50)}..."`);
        }
        if (chunk.isComplete) {
          // Don't overwrite the accumulated text, just get metadata
          finalCost = chunk.cost;
          finalMetadata = chunk.metadata;
          Logger.info(`🔍 Final metadata received: ${finalMetadata}`);
          break;
        }
      }

      // Clear streaming tracking
      this._currentStreamingText = '';
      this._currentStreamingMessageId = null;
      // Clear streaming timeout when streaming completes
      this._timeoutManager.stop();

      Logger.info(`✅ LLM turn completed. Final text: "${finalAssistantResponseText.substring(0,50)}..."`);

      // Always persist assistant message, even if empty (for tool-only responses)
      const assistantMessage: Message = {
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
        type: MessageType.ASSISTANT,
        content: finalAssistantResponseText || '', // Allow empty content
        metadata: {
          model: finalMetadata?.model,
          tokens: finalMetadata?.inputTokens + finalMetadata?.outputTokens,
          cost: finalCost,
          responseTime: finalMetadata?.responseTime,
          tool_executions: finalMetadata?.tool_executions
        }
      };

      await this._chatManager.addMessage(this._currentConversationId, assistantMessage);
      Logger.info(`💾 Stored assistant message: "${finalAssistantResponseText.substring(0, 50)}..."`);

      const rawLLMResponse = finalMetadata?.raw_llm_response || finalAssistantResponseText;
      if (rawLLMResponse) {
        const parsedToolCallsFromRaw = ToolParser.parseToolCalls(rawLLMResponse);
        const assistantContentBlocks: any[] = [];

        const conversationalTextPart = ToolParser.stripToolCallsFromText(rawLLMResponse);
        if (conversationalTextPart.trim()) {
          assistantContentBlocks.push({ text: conversationalTextPart.trim() });
        }

        if (parsedToolCallsFromRaw.length > 0) {
          parsedToolCallsFromRaw.forEach(tc => {
            assistantContentBlocks.push({
              toolUse: {
                toolUseId: tc.id,
                name: tc.name,
                input: tc.parameters,
              }
            } as any);
          });
        }

        if (assistantContentBlocks.length > 0) {
          this._conversationHistory.push({
            role: "assistant" as const,
            content: assistantContentBlocks,
          });
        } else if (rawLLMResponse.trim()) {
            this._conversationHistory.push({
                role: "assistant" as const,
                content: [{ text: rawLLMResponse.trim() }],
            });
        }
      }

      webview.postMessage({
        type: 'streamingEnd'
      });

      // Send completion message to UI
      webview.postMessage({
        type: 'messageComplete',
        fullText: finalAssistantResponseText,
        cost: finalCost,
        metadata: {
          model: finalMetadata?.model,
          tokens: finalMetadata?.inputTokens + finalMetadata?.outputTokens,
          cost: finalCost,
          responseTime: finalMetadata?.responseTime,
          tool_executions: finalMetadata?.tool_executions
        }
      });

      const toolExecutions = finalMetadata?.tool_executions as { toolCall: ToolCall, toolResult: ToolResult }[] | undefined;
      Logger.info(`🔍 Tool executions check: ${toolExecutions ? toolExecutions.length : 'null/undefined'} executions found`);

      if (toolExecutions && toolExecutions.length > 0) {
        Logger.info(`🛠️ Tools were executed. Preparing to send results back to LLM.`);
        Logger.info(`🔍 First tool execution: ${JSON.stringify(toolExecutions[0], null, 2)}`);

        // Check if any tools failed with errors that should stop the loop
        const hasUnrecoverableErrors = toolExecutions.some(exec =>
          exec.toolResult.error && (
            exec.toolResult.error.includes('not found in file') ||
            exec.toolResult.error.includes('TRUNCATED_RESPONSE') ||
            exec.toolResult.error.includes('File not found') ||
            exec.toolResult.error.includes('Permission denied')
          )
        );

        if (hasUnrecoverableErrors) {
          Logger.warn('🚫 Stopping tool result processing due to unrecoverable errors');
          // Just show the error messages to user, don't feed back to AI
          const errorMessages = toolExecutions
            .filter(exec => exec.toolResult.error)
            .map(exec => `❌ ${exec.toolCall.name}: ${exec.toolResult.error}`)
            .join('\n');

          webview.postMessage({
            type: 'messageChunk',
            text: `\n\n${errorMessages}`,
            isComplete: true
          });
          return;
        }

        // Format tool results as simple text for Azure OpenAI compatibility
        const toolResultsText = toolExecutions.map(exec => {
          Logger.info(`🔧 Tool result raw data for ${exec.toolCall.name}: ${JSON.stringify(exec.toolResult, null, 2)}`);
          const formattedResult = this.formatToolResult(exec.toolCall, exec.toolResult);
          Logger.info(`🔧 Formatted tool result for ${exec.toolCall.name}: ${formattedResult.substring(0, 200)}...`);
          return formattedResult;
        }).join('\n\n');

        if (toolResultsText.length > 0) {
            this._conversationHistory.push({
                role: "user" as const,
                content: [{ text: toolResultsText }],
            });

            // Send processing state with context
            webview.postMessage({
                type: 'toolProcessing',
                isProcessing: true,
                context: this.getProcessingContext(toolExecutions)
            });

            await this.handleChatMessage(webview, toolResultsText, true);

            // Clear processing state
            webview.postMessage({
                type: 'toolProcessing',
                isProcessing: false
            });
        }
      }

      // --- Commit checkpoint after all processing is complete ---
      if (shadowGitManagerFactory && !isFollowUpFromTool) {
        // Find the user message that started this checkpoint
        const conversation = await this._chatManager.getActiveConversation();
        if (conversation) {
          const userMessages = conversation.messages.filter(m => m.type === MessageType.USER);
          const lastUserMessage = userMessages[userMessages.length - 1];
          if (lastUserMessage) {
            const manager = await shadowGitManagerFactory.getManager(this._currentConversationId);
            const commitHash = await manager.commitCheckpoint(lastUserMessage.id);
            if (commitHash) {
              await this._chatManager.updateMessageMetadata(lastUserMessage.id, { checkpointCommit: commitHash });
              Logger.info(`✅ Committed checkpoint for user message: ${lastUserMessage.id} with hash: ${commitHash}`);
            } else {
              // Even if no commit was made (no changes), we should still mark this as a checkpoint
              // by getting the latest commit hash or creating an initial commit
              const latestCommit = await manager.getLatestCommitHash();
              if (latestCommit) {
                await this._chatManager.updateMessageMetadata(lastUserMessage.id, { checkpointCommit: latestCommit });
                Logger.info(`✅ Updated checkpoint for user message: ${lastUserMessage.id} with existing hash: ${latestCommit}`);
              } else {
                // Create an initial commit if none exists
                const initialCommit = await manager.commitCheckpoint(lastUserMessage.id);
                if (initialCommit) {
                  await this._chatManager.updateMessageMetadata(lastUserMessage.id, { checkpointCommit: initialCommit });
                  Logger.info(`✅ Created initial checkpoint for user message: ${lastUserMessage.id} with hash: ${initialCommit}`);
                }
              }
            }
          }
        }
      }
      // --- End checkpoint commit logic ---

    } catch (error) {
      Logger.error(error, '💥 Error handling chat message');
      webview.postMessage({
        type: 'messageError',
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      });
    }
  }

  private async handleValidateConfiguration(webview: vscode.Webview) {
    try {
      const settingsManager = new SettingsManager(this._context);
      const settings = await settingsManager.getSettings();

      const selectedModel = settings.selectedModel;
      const selectedProvider = settings.selectedProvider;

      if (!ModelRegistry.isModelValid(selectedModel as Models)) {
        webview.postMessage({
          type: 'validationResult',
          isValid: false,
          error: `Unknown model: ${selectedModel}`
        });
        return;
      }

      const requiredProvider = ModelRegistry.getProvider(selectedModel as Models);

      if (selectedProvider !== requiredProvider) {
        webview.postMessage({
          type: 'validationResult',
          isValid: false,
          error: `Model ${selectedModel} is not compatible with provider ${selectedProvider}. Model requires ${requiredProvider} provider.`
        });
        return;
      }

      const modelService = new ModelConfigurationService();
      const provider = settings.providers[selectedProvider as AIProviders];
      const providerStatus = modelService.getProviderStatus(selectedProvider as AIProviders, provider);
      
      const status = providerStatus.status;
      const message = providerStatus.message;

      webview.postMessage({
        type: 'validationResult',
        isValid: true,
        currentModel: selectedModel,
        currentProvider: selectedProvider,
        status: status,
        statusMessage: message
      });

    } catch (error) {
      Logger.error(error, 'Error validating configuration');
      webview.postMessage({
        type: 'validationResult',
        isValid: false,
        error: error instanceof Error ? error.message : 'Configuration validation failed'
      });
    }
  }

  private async handleGetAvailableModels(webview: vscode.Webview) {
    try {
      const settingsManager = new SettingsManager(this._context);
      const settings = await settingsManager.getSettings();
      const modelService = new ModelConfigurationService();

      const availability = await modelService.getAvailableModels(
        settings.providers,
        settings.selectedModel as Models
      );

      const modelsWithMetadata = availability.availableModels.map(model => ({
        id: model,
        displayName: ModelRegistry.getDisplayName(model),
        provider: ModelRegistry.getProvider(model),
        family: ModelRegistry.getModelMetadata(model)?.family,
        isDefault: ModelRegistry.getModelMetadata(model)?.isDefault
      }));

      webview.postMessage({
        type: 'availableModels',
        models: modelsWithMetadata,
        currentModel: availability.currentModel
      });

    } catch (error) {
      Logger.error(error, 'Error getting available models');
      webview.postMessage({
        type: 'availableModels',
        models: [],
        currentModel: null
      });
    }
  }

  private async handleChangeModel(webview: vscode.Webview, newModel: Models) {
    try {
      const settingsManager = new SettingsManager(this._context);
      const settings = await settingsManager.getSettings();
      const modelService = new ModelConfigurationService();

      const availability = await modelService.getAvailableModels(settings.providers, newModel);
      if (!availability.availableModels.includes(newModel)) {
        webview.postMessage({
          type: 'modelChangeResult',
          success: false,
          error: `Model ${newModel} is not available with current provider configuration`
        });
        return;
      }

      // Update the selected model
      settings.selectedModel = newModel;

      const requiredProvider = ModelRegistry.getProvider(newModel);
      if (requiredProvider && requiredProvider !== settings.selectedProvider) {
        const providerStatus = modelService.getProviderStatus(requiredProvider, settings.providers[requiredProvider]);
        if (providerStatus.status !== 'configured') {
          webview.postMessage({
            type: 'modelChangeResult',
            success: false,
            error: `Model ${newModel} requires ${requiredProvider} provider, but it is not configured`
          });
          return;
        }
        
        settings.selectedProvider = requiredProvider;
      }

      // Save the updated settings
      await settingsManager.updateSettings(settings);

      // Refresh the AI service with the new model
      await this._aiService.refreshProvider();

      webview.postMessage({
        type: 'modelChanged',
        model: newModel,
        provider: settings.selectedProvider,
        isAvailable: this._aiService.isAvailable()
      });

      Logger.info(`Model changed to: ${newModel}`);
    } catch (error) {
      Logger.error(error, 'Error changing model');
      webview.postMessage({
        type: 'messageError',
        error: error instanceof Error ? error.message : 'Failed to change model'
      });
    }
  }

  private async handleTestModelCredentials(webview: vscode.Webview, model: Models) {
    try {
      const settingsManager = new SettingsManager(this._context);
      const settings = await settingsManager.getSettings();
      const modelService = new ModelConfigurationService();

      if (!ModelRegistry.isModelValid(model)) {
        webview.postMessage({
          type: 'modelCredentialsTestResult',
          success: false,
          error: `Invalid model: ${model}`
        });
        return;
      }

      const requiredProvider = ModelRegistry.getProvider(model);
      if (!requiredProvider) {
        webview.postMessage({
          type: 'modelCredentialsTestResult',
          success: false,
          error: `No provider found for model: ${model}`
        });
        return;
      }

      const providerConfig = settings.providers[requiredProvider];
      const providerStatus = modelService.getProviderStatus(requiredProvider, providerConfig);
      
      if (providerStatus.status !== 'configured') {
        webview.postMessage({
          type: 'modelCredentialsTestResult',
          success: false,
          error: `Provider ${requiredProvider} is not configured`
        });
        return;
      }

      try {
        const testResult = await this._aiService.testCredentials(model, requiredProvider as AIProviders);
        
        if (testResult.success) {
          webview.postMessage({
            type: 'modelCredentialsTestResult',
            success: true,
            model: model,
            provider: requiredProvider
          });
          Logger.info(`Credentials test passed for ${model} with ${requiredProvider}`);
        } else {
          webview.postMessage({
            type: 'modelCredentialsTestResult',
            success: false,
            error: testResult.error || 'Credential test failed'
          });
        }
      } catch (testError) {
        Logger.error(testError, `Credential test failed for ${model}`);
        webview.postMessage({
          type: 'modelCredentialsTestResult',
          success: false,
          error: testError instanceof Error ? testError.message : 'Credential test failed'
        });
      }

    } catch (error) {
      Logger.error(error, 'Error testing model credentials');
      webview.postMessage({
        type: 'modelCredentialsTestResult',
        success: false,
        error: error instanceof Error ? error.message : 'Failed to test credentials'
      });
    }
  }

  private async handleClosePanel() {
    try {
      await vscode.commands.executeCommand('workbench.action.toggleSidebarVisibility');

      Logger.info('Chat panel focus moved away');
    } catch (error) {
      Logger.error(error, 'Error handling close panel');
      // Fallback: try to hide the view
      try {
        if (this._view) {
          this._view.show(false);
        }
      } catch (fallbackError) {
        Logger.error(fallbackError, 'Fallback close also failed');
      }
    }
  }

  private async handleOpenSettings() {
    try {
      // Execute the settings command which will open the settings webview
      await vscode.commands.executeCommand('xyne.settings');
      Logger.info('Settings command executed');
    } catch (error) {
      Logger.error(error, 'Error opening settings');
      // Send error back to webview if needed
      if (this._view) {
        this._view.webview.postMessage({
          type: 'messageError',
          error: 'Failed to open settings'
        });
      }
    }
  }

  private async handleOpenTerminal(command?: string) {
    try {
      Logger.info(`Creating terminal bubble with command: ${command || 'none'}`);
      if (!this._currentConversationId) {
        this._currentConversationId = await this._chatManager.createConversation();
      }
      // Create a terminal message and store it in the backend
      const terminalMessage: Message = {
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
        type: MessageType.TERMINAL,
        content: command || '',
        metadata: {},
        sessionId: `term_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
      await this._chatManager.addMessage(this._currentConversationId, terminalMessage);
      const messages = await this._chatManager.getMessages(this._currentConversationId);
      Logger.info('[DEBUG] Conversation messages after terminal append: ' + JSON.stringify(messages.map(m => ({id: m.id, type: m.type, content: m.content, sessionId: m.sessionId}))));
      // The emitMessagesChanged will send the new message to the frontend
    } catch (error) {
      Logger.error(error, 'Failed to create terminal bubble');
    }
  }

  // Conversation management methods
  async createNewConversation() {
    try {
      Logger.info('🆕 Creating new conversation...');

      // Clear current state first
      this._currentConversationId = null;
      this._conversationHistory = [];

      // Focus the chat view
      if (this._view) {
        this._view.show(true);
        this._view.webview.postMessage({
          type: 'conversationCreated',
          conversationId: null // null indicates fresh start, conversation will be created on first message
        });
      }

      Logger.info(`✅ Ready for new conversation - will be created on first message`);
    } catch (error) {
      Logger.error(error, 'Failed to prepare new conversation');
      vscode.window.showErrorMessage('Failed to prepare new conversation');
    }
  }

  async showConversationsList() {
    try {
      Logger.info('📋 Showing conversations list...');
      const conversations = await this._chatManager.getConversations();

      if (conversations.length === 0) {
        vscode.window.showInformationMessage('No conversations found. Start a new conversation!');
        return;
      }

      // Create quick pick items
      const items = conversations.map(conv => ({
        label: conv.title,
        description: `${conv.messageCount} messages - ${new Date(conv.updatedAt).toLocaleDateString()}`,
        detail: conv.preview,
        conversationId: conv.id
      }));

      const selected = await vscode.window.showQuickPick(items, {
        placeHolder: 'Select a conversation to open',
        matchOnDescription: true,
        matchOnDetail: true
      });

      if (selected) {
        Logger.info(`🔄 Switching to conversation: ${selected.conversationId}`);
        await this._chatManager.switchConversation(selected.conversationId);

        // Update the tool executor with the conversation-specific shadow Git manager
        await this._aiService.updateToolExecutorForConversation(selected.conversationId);
        Logger.info(`🔄 Updated tool executor for selected conversation: ${selected.conversationId}`);

        // Focus the chat view
        if (this._view) {
          this._view.show(true);
        }
      }
    } catch (error) {
      Logger.error(error, 'Failed to show conversations list');
      vscode.window.showErrorMessage('Failed to load conversations');
    }}
  private async handleStopExecution(webview: vscode.Webview) {
    try {
      Logger.info('🛑 Stop execution requested by user');
      this._isCancelled = true;
      
      // Auto-save current conversation state before stopping
      await this._chatManager.autoSaveCurrentConversation();
      
      // Mark current conversation as interrupted if there's an active one
      if (this._currentConversationId) {
        const partialResponse = this.getCurrentStreamingText();
        const lastMessageId = this.getLastMessageId();
        
        await this._chatManager.markConversationAsInterrupted(
          'user_stop',
          partialResponse,
          lastMessageId
        );
        
        Logger.info(`📱 Marked conversation ${this._currentConversationId} as interrupted by user`);
      }
      
      // Send confirmation to webview
      webview.postMessage({
        type: 'executionStopped'
      });
      
      // Send conversation interruption notification
      webview.postMessage({
        type: 'conversationInterrupted',
        reason: 'user_stop',
        conversationId: this._currentConversationId,
        canResume: true
      });
      
    } catch (error) {
      Logger.error(error, 'Error handling stop execution');
    }
  }

  private getProcessingContext(toolExecutions: { toolCall: ToolCall, toolResult: ToolResult }[]): string {
    if (!toolExecutions || toolExecutions.length === 0) {
      return 'default';
    }

    const toolNames = toolExecutions.map(exec => exec.toolCall.name);
    
    // Determine the context based on tool types
    if (toolNames.some(name => ['write_file', 'multi_edit'].includes(name))) {
      return 'writing';
    } else if (toolNames.some(name => ['read_file', 'search_files', 'hybrid_search'].includes(name))) {
      return 'reading';
    } else if (toolNames.some(name => ['list_files'].includes(name))) {
      return 'exploring';
    } else {
      return 'analyzing';
    }
  }

  private async handleRunCode(webview: vscode.Webview, code: string, language: string) {
    try {
      Logger.info(`🏃 Running ${language} code: "${code.substring(0, 50)}..."`);
      
      // Create a new terminal and run the code
      const terminal = vscode.window.createTerminal(`Run ${language.toUpperCase()}`);
      
      // Show the terminal
      terminal.show();
      
      // Send the appropriate command based on language
      switch (language.toLowerCase()) {
        case 'javascript':
        case 'js':
          terminal.sendText(`node -e "${code.replace(/"/g, '\\"')}"`);
          break;
        case 'typescript':
        case 'ts':
          terminal.sendText(`npx ts-node -e "${code.replace(/"/g, '\\"')}"`);
          break;
        case 'python':
        case 'py':
          terminal.sendText(`python3 -c "${code.replace(/"/g, '\\"')}"`);
          break;
        case 'bash':
        case 'sh':
        case 'shell':
          terminal.sendText(code);
          break;
        default:
          // For other languages, just output the code
          terminal.sendText(`echo "Code to run:"; cat << 'EOF'\n${code}\nEOF`);
      }
      
      // Send success response to webview
      webview.postMessage({
        type: 'codeExecuted',
        language: language
      });
      
    } catch (error) {
      Logger.error(error, 'Error running code');
      webview.postMessage({
        type: 'messageError',
        error: error instanceof Error ? error.message : 'Failed to run code'
      });
    }
  }

  private async handleCreateFile(webview: vscode.Webview, content: string, filename: string, language?: string) {
    try {
      Logger.info(`📄 Creating file: ${filename}`);
      
      // Get the workspace folder
      const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
      if (!workspaceFolder) {
        throw new Error('No workspace folder found');
      }
      
      // Create the file URI
      const fileUri = vscode.Uri.joinPath(workspaceFolder.uri, filename);
      
      // Write the file
      await vscode.workspace.fs.writeFile(fileUri, Buffer.from(content, 'utf8'));
      
      // Open the file in the editor
      const document = await vscode.workspace.openTextDocument(fileUri);
      await vscode.window.showTextDocument(document);
      
      // Send success response to webview
      webview.postMessage({
        type: 'fileCreated',
        filename: filename,
        path: fileUri.fsPath
      });
      
      Logger.info(`✅ File created successfully: ${fileUri.fsPath}`);
      
    } catch (error) {
      Logger.error(error, 'Error creating file');
      webview.postMessage({
        type: 'messageError',
        error: error instanceof Error ? error.message : 'Failed to create file'
      });
    }
  }

  // Chat session handlers
  private async handleGetChatSessions(webview: vscode.Webview) {
    try {
      const sessions = await this.getChatSessions();
      webview.postMessage({
        type: 'chatSessionsLoaded',
        sessions: sessions
      });
      Logger.info(`Sent ${sessions.length} chat sessions to webview`);
    } catch (error) {
      Logger.error(error, 'Error getting chat sessions');
      webview.postMessage({
        type: 'chatSessionsLoaded',
        sessions: []
      });
    }
  }

  private async handleSaveChatSession(webview: vscode.Webview, session: ChatSession) {
    try {
      await this.saveChatSession(session);
      this._currentSessionId = session.id;
      
      webview.postMessage({
        type: 'chatSessionSaved',
        success: true,
        sessionId: session.id
      });
    } catch (error) {
      Logger.error(error, 'Error saving chat session');
      webview.postMessage({
        type: 'chatSessionSaved',
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save session'
      });
    }
  }

  private async handleLoadChatSession(webview: vscode.Webview, sessionId: string, messages: any[]) {
    try {
      const session = await this.loadChatSession(sessionId);
      if (session) {
        this._currentConversationId = sessionId;
        // Convert UI messages to conversation history format
        this._conversationHistory = this.convertUIMessagesToConversationHistory(messages);
        
        webview.postMessage({
          type: 'chatSessionLoaded',
          success: true,
          session: session
        });
        Logger.info(`Loaded session ${sessionId} with ${this._conversationHistory.length} conversation entries`);
      } else {
        webview.postMessage({
          type: 'chatSessionLoaded',
          success: false,
          error: 'Session not found'
        });
      }
    } catch (error) {
      Logger.error(error, 'Error loading chat session');
      webview.postMessage({
        type: 'chatSessionLoaded',
        success: false,
        error: error instanceof Error ? error.message : 'Failed to load session'
      });
    }
  }

  private async handleDeleteChatSession(webview: vscode.Webview, sessionId: string) {
    try {
      await this.deleteChatSession(sessionId);
      
      webview.postMessage({
        type: 'chatSessionDeleted',
        success: true,
        sessionId: sessionId
      });
    } catch (error) {
      Logger.error(error, 'Error deleting chat session');
      webview.postMessage({
        type: 'chatSessionDeleted',
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete session'
      });
    }
  }

  private async handleStartNewSession(webview: vscode.Webview) {
    try {
      this._currentConversationId = await this._chatManager.createConversation();
      this._conversationHistory = [];
      if (this._view) {
        this._view.webview.postMessage({
          type: 'newSessionStarted',
          sessionId: this._currentConversationId
        });
      }
      Logger.info('Started new chat session');
    } catch (error) {
      Logger.error(error, 'Error starting new session');
      if (this._view) {
        this._view.webview.postMessage({
          type: 'newSessionStarted',
          success: false,
          error: error instanceof Error ? error.message : 'Failed to start new session'
        });
      }
    }
  }

  // Helper to convert UI messages to conversation history format
  private convertUIMessagesToConversationHistory(uiMessages: any[]): AwsBedrockMessage[] {
    const conversationHistory: AwsBedrockMessage[] = [];
    
    for (const msg of uiMessages) {
      if (msg.sender === 'user') {
        conversationHistory.push({
          role: "user" as const,
          content: [{ text: msg.content }]
        });
      } else if (msg.sender === 'bot') {
        conversationHistory.push({
          role: "assistant" as const,
          content: [{ text: msg.content }]
        });
      }
    }
    
    return conversationHistory;
  }

  public async notifySettingsChanged() {
    if (this._view?.webview) {
      this._view.webview.postMessage({
        type: 'settingsChanged'
      });
      
      // Also send updated cost settings
      await this.handleGetCostSettings(this._view.webview);
    }
  }

  private injectWorkspacePath(htmlContent: string): string {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    const workspacePath = workspaceFolders && workspaceFolders.length > 0
      ? workspaceFolders[0].uri.fsPath
      : process.env.HOME || '';
    const workspacePathScript = `\n<script>window.__vscodeWorkspacePath = ${JSON.stringify(workspacePath)};<\/script>\n`;
    return htmlContent.replace('<head>', `<head>\n${workspacePathScript}`);
  }

  private async setWebviewHtml(webview: vscode.Webview) {
    const isDevelopment = this._context.extensionMode === vscode.ExtensionMode.Development;
    const webviewDevServerUrl = 'http://localhost:5173';

    Logger.info(`Getting chat webview content - Development mode: ${isDevelopment}`);

    if (isDevelopment) {
      Logger.info('Using development mode - fetching content from Vite dev server');

      try {
        // Fetch the HTML content from Vite dev server
        const response = await fetch(`${webviewDevServerUrl}?page=chat`);
        Logger.info(`Fetch response status: ${response.status}, ok: ${response.ok}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch from Vite dev server: ${response.status} ${response.statusText}`);
        }

        let htmlContent = await response.text();

        // Inject workspace path
        htmlContent = this.injectWorkspacePath(htmlContent);

        // Convert relative URLs to absolute URLs pointing to the Vite dev server
        htmlContent = htmlContent
          .replace(/src="\/([^"]+)"/g, `src="${webviewDevServerUrl}/$1"`)
          .replace(/href="\/([^"]+)"/g, `href="${webviewDevServerUrl}/$1"`)
          .replace(/url\(\/([^)]+)\)/g, `url(${webviewDevServerUrl}/$1)`)
          .replace(/from "\/([^"]+)"/g, `from "${webviewDevServerUrl}/$1"`)
          .replace(/import "\/([^"]+)"/g, `import "${webviewDevServerUrl}/$1"`);

        // Update CSP to allow the webview resources
        const cspMetaTag = `
          <meta http-equiv="Content-Security-Policy" content="
            default-src 'none';
            style-src 'unsafe-inline' ${webview.cspSource} ${webviewDevServerUrl};
            script-src 'unsafe-eval' 'unsafe-inline' ${webview.cspSource} ${webviewDevServerUrl};
            connect-src ${webview.cspSource} ${webviewDevServerUrl} ws://localhost:5173 ws://localhost:8080;
            img-src ${webview.cspSource} ${webviewDevServerUrl} data:;
            font-src ${webview.cspSource} ${webviewDevServerUrl};
          ">`;

        // Replace or add CSP
        if (htmlContent.includes('<meta http-equiv="Content-Security-Policy"')) {
          htmlContent = htmlContent.replace(/<meta http-equiv="Content-Security-Policy"[^>]*>/i, cspMetaTag);
        } else if (htmlContent.includes('<head>')) {
          htmlContent = htmlContent.replace('<head>', `<head>\n    ${cspMetaTag}`);
        }

        // Add chat page indicator and VS Code API setup
        const bodyReplacement = `<body data-page="chat">
          <script>
            console.log('Chat webview loaded in development mode');

            // Set up global page context for React app
            window.__WEBVIEW_PAGE__ = 'chat';

            // Set up VS Code API once and make it globally available
            (function() {
              try {
                if (window.acquireVsCodeApi && !window.__vscodeApi) {
                  window.__vscodeApi = window.acquireVsCodeApi();
                  console.log('VS Code API acquired and stored globally');
                }
              } catch (error) {
                console.error('Error setting up VS Code API:', error);
              }
            })();

            // Error handling
            window.addEventListener('error', function(event) {
              console.error('Webview error:', event);
              if (window.__vscodeApi) {
                window.__vscodeApi.postMessage({type: 'error', message: event.message});
              }
            });

            // Signal that webview is ready after DOM is loaded
            window.addEventListener('DOMContentLoaded', function() {
              setTimeout(() => {
                if (window.__vscodeApi) {
                  console.log('Sending webviewReady signal...');
                  window.__vscodeApi.postMessage({type: 'webviewReady'});
                }
              }, 100);
            });

            // Fallback ready signal after load event
            window.addEventListener('load', function() {
              setTimeout(() => {
                if (window.__vscodeApi) {
                  console.log('Sending webviewReady signal (load fallback)...');
                  window.__vscodeApi.postMessage({type: 'webviewReady'});
                }
              }, 50);
            });
          </script>`;

        // Remove any existing vscode variable declarations to prevent conflicts
        htmlContent = htmlContent.replace(/(?:const|let|var)\s+vscode\s*=\s*[^;]+;?/g, '');
        htmlContent = htmlContent.replace(/window\.vscode\s*=\s*[^;]+;?/g, '');

        htmlContent = htmlContent.replace('<body>', bodyReplacement);

        Logger.info('Development HTML content prepared successfully');
        webview.html = htmlContent;

      } catch (error) {
        Logger.error(error, 'Failed to fetch from Vite dev server');
        vscode.window.showErrorMessage(`Failed to load chat webview: ${error}`);
      }
    } else {
      Logger.info('Using production mode - loading pre-built assets');
      // Production mode: Load pre-built static assets
      const builtIndexPath = vscode.Uri.joinPath(this._extensionUri, 'webview-ui', 'dist', 'index.html');
      try {
        Logger.info(`Reading built index.html from: ${builtIndexPath.fsPath}`);
        const htmlContentUint8Array = await vscode.workspace.fs.readFile(builtIndexPath);
        let htmlString = new TextDecoder().decode(htmlContentUint8Array);
        Logger.info('Successfully read built HTML content');

        // Inject workspace path
        htmlString = this.injectWorkspacePath(htmlString);

        const mainJsUri = webview.asWebviewUri(
          vscode.Uri.joinPath(this._extensionUri, 'webview-ui', 'dist', 'assets', 'main.js')
        );
        const indexCssUri = webview.asWebviewUri(
          vscode.Uri.joinPath(this._extensionUri, 'webview-ui', 'dist', 'assets', 'index.css')
        );

        htmlString = htmlString.replace('src="/assets/main.js"', `src="${mainJsUri}"`);
        htmlString = htmlString.replace('href="/assets/index.css"', `href="${indexCssUri}"`);

        const cspMetaTag = `
          <meta http-equiv="Content-Security-Policy" content="
            default-src 'none';
            style-src ${webview.cspSource} 'unsafe-inline';
            script-src ${webview.cspSource};
            img-src ${webview.cspSource} data:;
            font-src ${webview.cspSource};
          ">`;

        if (htmlString.includes("<head>")) {
            htmlString = htmlString.replace("<head>", `<head>\n    ${cspMetaTag}`);
        } else {
            htmlString = cspMetaTag + htmlString;
        }

        // Add chat page indicator
        htmlString = htmlString.replace('<body>', `<body data-page="chat">
          <script>
            console.log('Chat webview loaded in production mode');
            if (window.acquireVsCodeApi) {
              const vscode = acquireVsCodeApi();
              window.__vscodeApi = vscode;
            }
            window.addEventListener('error', function(event) {
              console.error('Webview error:', event);
              if (window.__vscodeApi) {
                window.__vscodeApi.postMessage({type: 'error', message: event.message});
              }
            });

            // Signal that webview is ready after DOM is loaded
            window.addEventListener('DOMContentLoaded', function() {
              setTimeout(() => {
                if (window.__vscodeApi) {
                  console.log('Sending webviewReady signal...');
                  window.__vscodeApi.postMessage({type: 'webviewReady'});
                }
              }, 100);
            });

            // Fallback ready signal after load event
            window.addEventListener('load', function() {
              setTimeout(() => {
                if (window.__vscodeApi) {
                  console.log('Sending webviewReady signal (load fallback)...');
                  window.__vscodeApi.postMessage({type: 'webviewReady'});
                }
              }, 50);
            });
          </script>`);

        Logger.info('Production HTML content prepared successfully');
        webview.html = htmlString;
      } catch (error) {
        Logger.error(error, "Error loading webview HTML for chat");
        vscode.window.showErrorMessage(`Failed to load webview content: ${error}`);
        webview.html = `<html><body><h1>Error</h1><p>Could not load webview content. Details: ${error}</p></body></html>`;
      }
    }
  }

  // History management methods
  private async handleLoadConversations(webview: vscode.Webview) {
    try {
      const conversations = await this._chatManager.getConversations();
      webview.postMessage({
        type: 'conversationsLoaded',
        conversations: conversations
      });
      Logger.info(`📋 Loaded ${conversations.length} conversations for UI`);
    } catch (error) {
      Logger.error(error, 'Failed to load conversations');
      webview.postMessage({
        type: 'messageError',
        error: 'Failed to load conversation history'
      });
    }
  }

  private async handleSwitchConversation(webview: vscode.Webview, conversationId: string) {
    try {
      Logger.info(`🔄 handleSwitchConversation called for: ${conversationId}`);
      Logger.info(`📝 Current _conversationHistory before switch: ${this._conversationHistory.length} messages`);

      // Only call switchConversation - let the event system handle the rest
      await this._chatManager.switchConversation(conversationId);

      // Update the tool executor with the conversation-specific shadow Git manager
      await this._aiService.updateToolExecutorForConversation(conversationId);
      Logger.info(`🔄 Updated tool executor for switched conversation: ${conversationId}`);

      // Send simple acknowledgment to UI (the actual messages will come via events)
      webview.postMessage({
        type: 'conversationSwitched',
        conversationId: conversationId
      });

      Logger.info(`🔄 Switched to conversation: ${conversationId}`);
    } catch (error) {
      Logger.error(error, 'Failed to switch conversation');
      webview.postMessage({
        type: 'messageError',
        error: 'Failed to switch conversation'
      });
    }
  }

  private async handleOpenFullHistory() {
    try {
      // Use the existing command to open the full conversations list
      await vscode.commands.executeCommand('xyne.listConversations');
      Logger.info('📋 Opened full conversation history');
    } catch (error) {
      Logger.error(error, 'Failed to open full history');
    }
  }

  private async handleRenameConversation(webview: vscode.Webview, conversationId: string, newTitle?: string) {
    if (!newTitle) {
      Logger.warn(`Rename conversation request for ${conversationId} received without a new title.`);
      // Optionally, you could prompt for a title here if the webview doesn't.
      // For now, we'll just log and return.
      return;
    }
    Logger.info(`Renaming conversation ${conversationId} to "${newTitle}"`);
    try {
      await this._chatManager.renameConversation(conversationId, newTitle);
      Logger.info(`Conversation ${conversationId} renamed successfully.`);
    } catch (error) {
      Logger.error(error, `Failed to rename conversation ${conversationId}`);
      // You might want to send an error message back to the webview
      webview.postMessage({
        type: 'error',
        message: `Failed to rename conversation: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }
  }

  private async handleDeleteConversation(webview: vscode.Webview, conversationId: string) {
    try {
      Logger.info(`🗑️ Deleting conversation: ${conversationId}`);

      // Get conversation title for confirmation
      const conversations = await this._chatManager.getConversations();
      const conversation = conversations.find(c => c.id === conversationId);
      const title = conversation?.title || 'Untitled Conversation';

      // Show confirmation dialog
      const confirmDelete = await vscode.window.showWarningMessage(
        `Are you sure you want to delete "${title}"?`,
        { modal: true },
        'Delete',
        'Cancel'
      );

      if (confirmDelete === 'Delete') {
        await this._chatManager.deleteConversation(conversationId);

        // If this was the current conversation, clear the chat view
        if (this._currentConversationId === conversationId) {
          this._currentConversationId = null;
          this._conversationHistory = [];

          // Clear messages in UI and reset input position
          webview.postMessage({
            type: 'conversationDeleted',
            deletedConversationId: conversationId
          });
        }

        Logger.info(`✅ Conversation deleted: "${title}"`);
        vscode.window.showInformationMessage(`Conversation "${title}" deleted`);
      }
    } catch (error) {
      Logger.error(error, 'Failed to delete conversation');
      webview.postMessage({
        type: 'messageError',
        error: 'Failed to delete conversation'
      });
      vscode.window.showErrorMessage('Failed to delete conversation');
    }
  }

  private async handleOpenImageInVSCode(imageUrl: string, imageName: string) {
    try {
      Logger.info(`Opening image in VS Code: ${imageName}`);
      
      // Extract base64 data from data URL
      const base64Match = imageUrl.match(/^data:image\/[^;]+;base64,(.+)$/);
      if (!base64Match) {
        throw new Error('Invalid image data URL format');
      }
      
      const base64Data = base64Match[1];
      const imageBuffer = Buffer.from(base64Data, 'base64');
      
      // Create a temporary file
      const fs = require('fs');
      const path = require('path');
      const os = require('os');
      
      // Get file extension from image name or default to png
      const ext = path.extname(imageName) || '.png';
      const tempFileName = `temp_image_${Date.now()}${ext}`;
      const tempFilePath = path.join(os.tmpdir(), tempFileName);
      
      // Write the image buffer to the temporary file
      fs.writeFileSync(tempFilePath, imageBuffer);
      
      // Open the image file using VS Code's default handler
      const imageUri = vscode.Uri.file(tempFilePath);
      await vscode.commands.executeCommand('vscode.open', imageUri);
      
      Logger.info(`✅ Image opened in VS Code: ${tempFilePath}`);
      
    } catch (error) {
      Logger.error(error, 'Failed to open image in VS Code');
      vscode.window.showErrorMessage(`Failed to open image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // MCP Handler Methods
  private async handleGetMcpServers(webview: vscode.Webview) {
    try {
      const servers = this._mcpHub.getServers();
      webview.postMessage({
        type: 'mcpServers',
        servers: servers
      });
      Logger.info(`Sent ${servers.length} MCP servers to webview`);
    } catch (error) {
      Logger.error(error, 'Error getting MCP servers');
      webview.postMessage({
        type: 'messageError',
        error: 'Failed to get MCP servers'
      });
    }
  }

  private async handleToggleMcpServer(webview: vscode.Webview, serverName: string) {
    try {
      if (!serverName) {
        throw new Error('Server name is required');
      }
      
      await this._mcpHub.toggleServer(serverName);
      
      // Send updated servers list
      const servers = this._mcpHub.getServers();
      webview.postMessage({
        type: 'mcpServers',
        servers: servers
      });
      
      Logger.info(`Toggled MCP server: ${serverName}`);
    } catch (error) {
      Logger.error(error, `Error toggling MCP server: ${serverName}`);
      webview.postMessage({
        type: 'messageError',
        error: `Failed to toggle MCP server: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }
  }

  private async handleRemoveMcpServer(webview: vscode.Webview, serverName: string) {
    try {
      if (!serverName) {
        throw new Error('Server name is required');
      }
      
      await this._mcpHub.removeServer(serverName);
      
      // Send updated servers list
      const servers = this._mcpHub.getServers();
      webview.postMessage({
        type: 'mcpServers',
        servers: servers
      });
      
      Logger.info(`Removed MCP server: ${serverName}`);
    } catch (error) {
      Logger.error(error, `Error removing MCP server: ${serverName}`);
      webview.postMessage({
        type: 'messageError',
        error: `Failed to remove MCP server: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }
  }

  private async handleAddMcpServer(webview: vscode.Webview, serverConfig: any) {
    try {
      if (!serverConfig || !serverConfig.name) {
        throw new Error('Valid server configuration is required');
      }
      
      await this._mcpHub.addServer(serverConfig);
      
      // Send updated servers list
      const servers = this._mcpHub.getServers();
      webview.postMessage({
        type: 'mcpServers',
        servers: servers
      });
      
      Logger.info(`Added MCP server: ${serverConfig.name}`);
    } catch (error) {
      Logger.error(error, `Error adding MCP server: ${serverConfig?.name || 'unknown'}`);
      webview.postMessage({
        type: 'messageError',
        error: `Failed to add MCP server: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }
  }

  private async handleUpdateMcpServer(webview: vscode.Webview, serverConfig: any) {
    try {
      if (!serverConfig || !serverConfig.name) {
        throw new Error('Valid server configuration is required');
      }
      
      // Remove the old server and add the updated one
      await this._mcpHub.removeServer(serverConfig.name);
      await this._mcpHub.addServer(serverConfig);
      
      // Send updated servers list
      const servers = this._mcpHub.getServers();
      webview.postMessage({
        type: 'mcpServers',
        servers: servers
      });
      
      Logger.info(`Updated MCP server: ${serverConfig.name}`);
    } catch (error) {
      Logger.error(error, `Error updating MCP server: ${serverConfig?.name || 'unknown'}`);
      webview.postMessage({
        type: 'messageError',
        error: `Failed to update MCP server: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }
  }

  private async handleShowAddMcpServer(webview: vscode.Webview) {
    try {
      // For now, we'll use VS Code's input boxes to collect server information
      // In the future, this could be a dedicated webview form
      
      const serverName = await vscode.window.showInputBox({
        prompt: 'Enter MCP server name',
        placeHolder: 'e.g., filesystem, git, database',
        validateInput: (value) => {
          if (!value || value.trim().length === 0) {
            return 'Server name is required';
          }
          // Check if server already exists
          const existingServers = this._mcpHub.getServers();
          if (existingServers.some(s => s.name === value.trim())) {
            return 'Server with this name already exists';
          }
          return null;
        }
      });

      if (!serverName) {
        return; // User cancelled
      }

      const serverType = await vscode.window.showQuickPick([
        { label: 'stdio', description: 'Local process communication (recommended)' },
        { label: 'http', description: 'HTTP-based server' },
        { label: 'sse', description: 'Server-Sent Events' }
      ], {
        placeHolder: 'Select server connection type'
      });

      if (!serverType) {
        return; // User cancelled
      }

      let serverConfig: any = {
        name: serverName.trim(),
        type: serverType.label
      };

      if (serverType.label === 'stdio') {
        const command = await vscode.window.showInputBox({
          prompt: 'Enter command to run the MCP server',
          placeHolder: 'e.g., npx, node, python',
          validateInput: (value) => {
            if (!value || value.trim().length === 0) {
              return 'Command is required for stdio servers';
            }
            return null;
          }
        });

        if (!command) {
          return; // User cancelled
        }

        const argsInput = await vscode.window.showInputBox({
          prompt: 'Enter command arguments (space-separated)',
          placeHolder: 'e.g., -y @modelcontextprotocol/server-filesystem /path/to/directory'
        });

        serverConfig.command = command.trim();
        if (argsInput && argsInput.trim()) {
          serverConfig.args = argsInput.trim().split(/\s+/);
        }
      } else {
        const url = await vscode.window.showInputBox({
          prompt: 'Enter server URL',
          placeHolder: 'e.g., https://api.example.com/mcp',
          validateInput: (value) => {
            if (!value || value.trim().length === 0) {
              return 'URL is required for HTTP/SSE servers';
            }
            try {
              new URL(value);
              return null;
            } catch {
              return 'Please enter a valid URL';
            }
          }
        });

        if (!url) {
          return; // User cancelled
        }

        serverConfig.url = url.trim();
      }

      // Add the server
      await this._mcpHub.addServer(serverConfig);
      
      // Send updated servers list
      const servers = this._mcpHub.getServers();
      webview.postMessage({
        type: 'mcpServers',
        servers: servers
      });
      
      // Show success message
      vscode.window.showInformationMessage(`MCP server "${serverName}" added successfully!`);
      
      Logger.info(`Added MCP server via UI: ${serverName}`);
    } catch (error) {
      Logger.error(error, 'Error in handleShowAddMcpServer');
      vscode.window.showErrorMessage(`Failed to add MCP server: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleOpenMcpSettings() {
    try {
      Logger.info('Opening MCP settings...');
      // Open VS Code settings with MCP configuration focus
      await vscode.commands.executeCommand('workbench.action.openSettings', 'xyne.mcpServers');
    } catch (error) {
      Logger.error(error, 'Error opening MCP settings');
      vscode.window.showErrorMessage(`Failed to open MCP settings: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async handleGetCostSettings(webview: vscode.Webview) {
    try {
      Logger.info('Getting cost display settings...');
      const settingsManager = new SettingsManager(this._context);
      const settings = await settingsManager.getSettings();
      
      webview.postMessage({
        type: 'costSettingsLoaded',
        settings: {
          showAggregateCost: settings.showAggregateCost !== undefined ? settings.showAggregateCost : true,
          showCostPerMessage: settings.showCostPerMessage !== undefined ? settings.showCostPerMessage : false
        }
      });
      
      Logger.info('Cost settings sent to webview');
    } catch (error) {
      Logger.error(error, 'Error getting cost settings');
      webview.postMessage({
        type: 'messageError',
        error: 'Failed to get cost settings'
      });
    }
  }

  /**
   * Handle accepting a file change - mark as accepted in SCM state
   */
  private async handleAcceptFileChange(webview: vscode.Webview, filePath: string, originalContent: string, newContent: string, changeId?: string) {
    try {
      Logger.info(`✅ User accepting file change for: ${filePath} with changeId: ${changeId}`);
  
      
      if (!changeId) {
        Logger.error(`❌ No changeId provided for file: ${filePath}`);
        vscode.window.showErrorMessage(`No change ID provided for file: ${filePath}`);
        return;
      }
      
      if (!shadowGitManagerFactory || !this._currentConversationId) {
        Logger.error(`❌ Shadow Git manager factory not initialized or no active conversation`);
        vscode.window.showErrorMessage('Shadow Git manager not initialized or no active conversation');
        return;
      }
      
      // Use conversation-specific shadow Git manager to accept the change
      const manager = await shadowGitManagerFactory.getManager(this._currentConversationId);
      await manager.acceptChange(changeId);
      Logger.info(`✅ Shadow Git change accepted: ${changeId}`);
      
      
      // Send confirmation to webview
      webview.postMessage({
        type: 'fileChangeAccepted',
        filePath: filePath,
        changeId: changeId,
        message: `File change accepted: ${filePath}`
      });
      
      Logger.info(`✅ File change accepted and confirmed to webview: ${filePath}`);
      
      
      // --- PERSIST REVIEW STATUS IN CONVERSATION HISTORY ---
      if (this._currentConversationId && changeId) {
        const messages = await this._chatManager.getMessages(this._currentConversationId);
        let updated = false;
        for (const msg of messages) {
          if (
            msg.metadata &&
            msg.metadata.fileChangeReview &&
            msg.metadata.fileChangeReview.changeId === changeId
          ) {
            msg.metadata.fileChangeReview.reviewStatus = 'accepted';
            updated = true;
          }
        }
        if (updated) {
          const conversation = await this._chatManager.storage.loadConversation(this._currentConversationId);
          if (conversation) {
            conversation.messages = messages;
            await this._chatManager.storage.saveConversation(conversation);
            this._chatManager.emitMessagesChanged(this._currentConversationId);
          }
        }
      }
      // --- END PERSISTENCE PATCH ---
    } catch (error) {
      Logger.error(error, `Failed to accept file change for: ${filePath}`);
      console.error(`[FILE REVIEW] [${changeId}] Failed to accept file change:`, error);
      webview.postMessage({
        type: 'messageError',
        error: `Failed to accept file change: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }
  }

  /**
   * Handle rejecting a file change - revert file to original content using shadow Git
   */
  private async handleRejectFileChange(webview: vscode.Webview, filePath: string, originalContent: string, changeId?: string, targetContent?: string) {
    try {
      Logger.info(`❌ User rejecting file change for: ${filePath} with changeId: ${changeId}`);
      Logger.info(`📝 Target content for rejection: ${targetContent ? 'provided' : 'using change original'}`);
  
      
      if (!changeId) {
        Logger.error(`❌ No changeId provided for file: ${filePath}`);
        vscode.window.showErrorMessage(`No change ID provided for file: ${filePath}`);
        return;
      }
      
      if (!shadowGitManagerFactory || !this._currentConversationId) {
        Logger.error(`❌ Shadow Git manager factory not initialized or no active conversation`);
        vscode.window.showErrorMessage('Shadow Git manager not initialized or no active conversation');
        return;
      }
      
      // Use conversation-specific shadow Git manager to reject the change with target content
      const manager = await shadowGitManagerFactory.getManager(this._currentConversationId);
      await manager.rejectChange(changeId, targetContent);
      Logger.info(`❌ Shadow Git change rejected: ${changeId}`);
      
      
      // Send confirmation to webview
      webview.postMessage({
        type: 'fileChangeRejected',
        filePath: filePath,
        changeId: changeId,
        message: `File change rejected: ${filePath}`
      });
      
      Logger.info(`❌ File change rejected and confirmed to webview: ${filePath}`);
      
      
      // --- PERSIST REVIEW STATUS IN CONVERSATION HISTORY ---
      if (this._currentConversationId && changeId) {
        const messages = await this._chatManager.getMessages(this._currentConversationId);
        let updated = false;
        for (const msg of messages) {
          if (
            msg.metadata &&
            msg.metadata.fileChangeReview &&
            msg.metadata.fileChangeReview.changeId === changeId
          ) {
            msg.metadata.fileChangeReview.reviewStatus = 'rejected';
            updated = true;
          }
        }
        if (updated) {
          const conversation = await this._chatManager.storage.loadConversation(this._currentConversationId);
          if (conversation) {
            conversation.messages = messages;
            await this._chatManager.storage.saveConversation(conversation);
            this._chatManager.emitMessagesChanged(this._currentConversationId);
          }
        }
      }
      // --- END PERSISTENCE PATCH ---
    } catch (error) {
      Logger.error(error, `Failed to reject file change for: ${filePath}`);
      console.error(`[FILE REVIEW] [${changeId}] Failed to reject file change:`, error);
      webview.postMessage({
        type: 'messageError',
        error: `Failed to reject file change: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }
  }

  /**
   * Handle opening Mermaid diagram in VS Code editor
   */
  private async handleOpenMermaidInEditor(content: string, filename: string) {
    try {
      Logger.info(`📄 Opening Mermaid diagram in editor: ${filename}`);
      
      // Create a new untitled document with Mermaid content
      const document = await vscode.workspace.openTextDocument({
        content: content,
        language: 'mermaid'
      });
      
      // Open the document in the editor
      await vscode.window.showTextDocument(document, {
        preview: false,
        viewColumn: vscode.ViewColumn.One
      });
      
      Logger.info(`✅ Successfully opened Mermaid diagram in editor`);
      
    } catch (error) {
      Logger.error(error, `Failed to open Mermaid diagram in editor: ${filename}`);
      vscode.window.showErrorMessage(`Failed to open Mermaid diagram: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Shadow Git file change tracking methods

  // Public method to register file changes from tool executor
  public async registerFileChangeFromCallback(filePath: string, originalContent: string, newContent: string, toolCallId: string): Promise<void> {
    try {
      // This method is now handled by the shadow Git manager in the tool executor
      // The shadow Git manager automatically tracks changes when files are written
      Logger.info(`📝 File change callback received: ${toolCallId} for ${filePath} (handled by shadow Git)`);
    } catch (error) {
      Logger.error(error, `Failed to process file change callback for ${filePath}`);
    }
  }

  // Public method to set AI service after initialization
  public setAIService(aiService: AIService): void {
    (this as any)._aiService = aiService;
    Logger.info('✅ AI service set in provider');
  }

  public getCurrentConversationId(): string | null {
    return this._currentConversationId;
  }

  // Helper methods for interrupted conversation tracking
  private getCurrentStreamingText(): string {
    // Return any current streaming text that was being generated
    // This could be tracked during streaming if needed
    return '';
  }

  private getLastMessageId(): string | undefined {
    // Return the ID of the last message in the current conversation
    if (this._conversationHistory.length > 0) {
      const lastMessage = this._conversationHistory[this._conversationHistory.length - 1];
      return lastMessage.id || undefined;
    }
    return undefined;
  }
  // Streaming timeout management methods
  public getTimeoutManager(): StreamingTimeoutManager {
    return this._timeoutManager;
  }

  private async handleUpdateSettings(webview: vscode.Webview, settings: any): Promise<void> {
    try {
      Logger.info('📝 Updating settings from webview');
      
      const settingsManager = new SettingsManager(this._context);
      await settingsManager.updateSettings(settings);
      
      // Update ChatManager settings if they changed
      if (settings.autoCompact) {
        this._chatManager.updateAutoCompactSettings(settings.autoCompact);
        Logger.info('Auto-compact settings updated in ChatManager');
      }
      
      if (settings.streamingTimeout) {
        this._chatManager.updateStreamingTimeoutSettings({
          enabled: settings.streamingTimeout.enabled,
          timeoutMs: (settings.streamingTimeout.timeoutMinutes || 2) * 60000, // Convert minutes to milliseconds
        });
        Logger.info('Streaming timeout settings updated in ChatManager');
      }
      
      // Refresh the AI service with new settings
      await this._aiService.refreshProvider();
      
      // Send confirmation back to webview
      webview.postMessage({
        type: 'settingsUpdated'
      });
      
      Logger.info('✅ Settings updated successfully');
      
    } catch (error) {
      Logger.error(error, 'Failed to update settings');
      webview.postMessage({
        type: 'error',
        message: `Failed to update settings: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }}
}

// Store test server reference for cleanup
let globalTestServer: any = null;

export function deactivate() {
  // Clean up test server if it exists
  if (globalTestServer) {
    globalTestServer.stop().catch((error: any) => {
      Logger.error(error, 'Error stopping test server during deactivation');
    });
  }
  
  // Clean up MCP connections
  Logger.info('Deactivating Xyne extension...');
}

import * as path from 'path';
import * as fs from 'fs';
import { connect, Index } from '@lancedb/lancedb';
import { QwenEmbeddingFunction, getAllFiles, splitTextByTokens, detectLanguage, DB_PATH, TABLE_NAME, sanitizePathToDirectoryName, EMBEDDING_CONFIG, checkGPUCapabilities } from './lancedb-utils';
import { UniversalChunker } from './universal-chunker';
import ignore from 'ignore';

export async function ingestCodebase(
  folderToEmbed: string, 
  modelPath: string, 
  extensionPath: string, 
  options?: { 
    fastMode?: boolean;
  }
) {
  const totalStartTime = process.hrtime();
  const fastMode = options?.fastMode ?? false;
  folderToEmbed = path.resolve(folderToEmbed);
  
  // Check GPU capabilities first
  checkGPUCapabilities();
  
  console.log(`🚀 Ingesting files into LanceDB (Universal Chunker, ${fastMode ? 'FAST MODE' : 'NORMAL MODE'})  `, folderToEmbed);
  console.log(`📊 Configuration: BATCH_SIZE=${EMBEDDING_CONFIG.BATCH_SIZE}, FILE_BATCH_SIZE=${EMBEDDING_CONFIG.FILE_BATCH_SIZE}, EMBEDDING_BATCH_SIZE=${EMBEDDING_CONFIG.EMBEDDING_BATCH_SIZE}`);

  const gitignorePath = path.join(folderToEmbed, '.gitignore');
  const ig = ignore();
  ig.add('.git');
  if (fs.existsSync(gitignorePath)) {
    const gitignoreContent = fs.readFileSync(gitignorePath, 'utf-8');
    ig.add(gitignoreContent);
    console.log('📁 Loaded .gitignore rules.');
  }

  const repoId = sanitizePathToDirectoryName(folderToEmbed);
  const embeddingsDir = path.join(extensionPath, '.embeddings', repoId);
  if (!fs.existsSync(embeddingsDir)) {
    fs.mkdirSync(embeddingsDir, { recursive: true });
  }
  const dbPath = path.join(embeddingsDir, DB_PATH);
  console.log('🗄️  DB PATH ', dbPath);

  const func = new QwenEmbeddingFunction({ modelPath });
  await func.init();

  // Initialize the universal chunker
  const chunker = new UniversalChunker(extensionPath);
  await chunker.initialize();

  const db = await connect(dbPath);

  let table;
  try {
    table = await db.openTable(TABLE_NAME);
    console.log(`📋 Opened existing table '${TABLE_NAME}'.`);
  } catch (e) {
    try {
      const dummy = [{
        text: "dummy",
        file: "dummy.txt",
        filePath: "/dummy.txt",
        language: "Text",
        chunkIndex: 0,
        totalChunks: 1,
        vector: new Array(1024).fill(0),
      }];
      table = await db.createTable(TABLE_NAME, dummy, { mode: "create" });
      await table.delete("file = 'dummy.txt'");
      console.log(`📋 Table '${TABLE_NAME}' created.`);
    } catch (createError) {
      // If table creation fails (e.g., already exists), try to open it again
      console.log(`⚠️  Table creation failed, trying to open existing table: ${createError instanceof Error ? createError.message : String(createError)}`);
      table = await db.openTable(TABLE_NAME);
      console.log(`📋 Opened existing table '${TABLE_NAME}'.`);
    }
  }

  await table.createIndex("text", { config: Index.fts() });

  const allFiles = await getAllFiles(folderToEmbed, ig, folderToEmbed);
  console.log(`📁 Found ${allFiles.length} files in ${folderToEmbed} (after .gitignore filter)`);

  const existingRecords = await table.query().select(["filePath", "chunkIndex", "totalChunks"]).toArray();
  
  // Group records by filePath to check for complete embeddings
  const fileCompleteness = new Map<string, { chunks: Set<number>, totalChunks: number }>();
  
  existingRecords.forEach(record => {
    const { filePath, chunkIndex, totalChunks } = record;
    if (!fileCompleteness.has(filePath)) {
      fileCompleteness.set(filePath, { chunks: new Set(), totalChunks });
    }
    fileCompleteness.get(filePath)!.chunks.add(chunkIndex);
  });
  
  // Check which files are completely embedded
  const completelyEmbeddedFiles = new Set<string>();
  fileCompleteness.forEach((info, filePath) => {
    const hasAllChunks = info.chunks.size === info.totalChunks;
    if (hasAllChunks) {
      completelyEmbeddedFiles.add(filePath);
    }
  });
  
  console.log(`📊 Found ${completelyEmbeddedFiles.size} completely embedded files out of ${fileCompleteness.size} files with partial embeddings`);
  
  // Debug: Log some examples of existing paths
  if (completelyEmbeddedFiles.size > 0) {
    console.log(`✅ Found ${completelyEmbeddedFiles.size} completely embedded files in database`);
    const samplePaths = Array.from(completelyEmbeddedFiles).slice(0, 3);
    console.log('📝 Sample completely embedded files:', samplePaths);
  }
  
  const newFiles = allFiles.filter(filePath => !completelyEmbeddedFiles.has(filePath));
  console.log(`🆕 Found ${newFiles.length} new files to embed out of ${allFiles.length} total files`);

  // Clean up only files with incomplete embeddings (partial chunks)
  const filesWithPartialEmbeddings = Array.from(fileCompleteness.keys()).filter(filePath => {
    const info = fileCompleteness.get(filePath)!;
    return info.chunks.size > 0 && info.chunks.size < info.totalChunks;
  });
  
  if (filesWithPartialEmbeddings.length > 0) {
    console.log(`🧹 Cleaning up ${filesWithPartialEmbeddings.length} files with incomplete embeddings...`);
    for (const filePath of filesWithPartialEmbeddings) {
      try {
        // Use proper SQL syntax with double quotes for the column name
        await table.delete(`"filePath" = '${filePath.replace(/'/g, "''")}'`);
        console.log(`  ✅ Cleaned incomplete chunks for: ${path.basename(filePath)}`);
      } catch (error) {
        console.warn(`⚠️  Failed to clean up incomplete embeddings for ${filePath}:`, error);
      }
    }
    console.log('✅ Cleanup completed');
  } else {
    console.log('✅ No files with incomplete embeddings found');
  }

  // Debug: Log some examples of new file paths (actual new files, not just first few from allFiles)
  if (newFiles.length > 0) {
    console.log('📝 Sample new file paths:', newFiles.slice(0, 3));
  }

  if (newFiles.length === 0) {
    console.log("🎉 All files are already embedded!");
    // Don't dispose the model here - it's needed for vector index creation
    // The model will be disposed at the end of the function
    return;
  }

  const BATCH_SIZE = fastMode ? Math.min(EMBEDDING_CONFIG.FILE_BATCH_SIZE * 2, 20) : EMBEDDING_CONFIG.FILE_BATCH_SIZE;
  let processedFiles = 0;
  let totalChunks = 0;
  let totalEmbeddedChunks = 0;
  let totalTokens = 0;
  let totalFileSize = 0;

  console.log(`\n🔄 Starting file processing with batch size: ${BATCH_SIZE}`);
  console.log(`📈 Progress tracking enabled - will show detailed metrics per batch\n`);

  for (let i = 0; i < newFiles.length; i += BATCH_SIZE) {
    const batchStartTime = process.hrtime();
    const batchFiles = newFiles.slice(i, i + BATCH_SIZE);
    const batchNumber = Math.floor(i / BATCH_SIZE) + 1;
    const totalBatches = Math.ceil(newFiles.length / BATCH_SIZE);
    const progressPercent = ((i / newFiles.length) * 100).toFixed(1);
    
    console.log(`\n📦 Processing batch ${batchNumber}/${totalBatches} (${batchFiles.length} files) - Progress: ${processedFiles}/${newFiles.length} files (${progressPercent}%)`);
    console.log(`⏱️  Batch start time: ${new Date().toLocaleTimeString()}`);

    const batchPromises = batchFiles.map(async (filePath) => {
      if (fs.statSync(filePath).isDirectory()) return null;
      try {
        const fileContent = fs.readFileSync(filePath, 'utf-8');
        const fileName = path.basename(filePath);
        const fileSize = fs.statSync(filePath).size;
        totalFileSize += fileSize;
        
        const result = await chunker.chunkFile(filePath, fileContent);
        processedFiles++;
        totalChunks += result.chunks.length;
        
        // Calculate tokens for this file
        const fileTokens = result.chunks.reduce((sum, chunk) => {
          return sum + (chunk.split(/\s+/).length || 0); // Simple token estimation
        }, 0);
        totalTokens += fileTokens;
        
        console.log(`  📄 ${fileName}: ${result.chunks.length} chunks, ~${fileTokens} tokens, ${(fileSize / 1024).toFixed(1)}KB`);
        
        return result.chunks.map((chunk, idx) => ({
          text: chunk,
          file: fileName,
          filePath,
          language: result.language,
          chunkIndex: idx,
          totalChunks: result.chunks.length,
        }));
      } catch (error) {
        console.error(`❌ Error processing file ${filePath}:`, error);
        processedFiles++;
        return null;
      }
    });

    const batchResults = await Promise.all(batchPromises);
    const validResults = batchResults.filter(result => result !== null).flat();
    
    const batchEndTime = process.hrtime(batchStartTime);
    const batchTimeMs = (batchEndTime[0] * 1000 + batchEndTime[1] / 1e6).toFixed(2);
    
    console.log(`✅ Batch ${batchNumber} completed: ${validResults.length} chunks generated in ${batchTimeMs}ms`);
    console.log(`📊 Batch stats: ${validResults.length} chunks, ~${totalTokens} tokens, ${(totalFileSize / 1024 / 1024).toFixed(2)}MB total`);
    console.log(`⏱️  Batch end time: ${new Date().toLocaleTimeString()}`);

    if (validResults.length > 0) {
      const texts = validResults.map(record => record.text);
      
      // Process embeddings in batches and save each batch immediately
      const batchSize = EMBEDDING_CONFIG.EMBEDDING_BATCH_SIZE;
      let totalProcessed = 0;
      
      console.log(`\n🧠 Starting embedding processing for ${texts.length} texts...`);
      
      for (let j = 0; j < texts.length; j += batchSize) {
        const embeddingBatchStartTime = process.hrtime();
        const textBatch = texts.slice(j, j + batchSize);
        const resultBatch = validResults.slice(j, j + batchSize);
        
        console.log(`🧠 Processing embedding batch ${Math.floor(j / batchSize) + 1}/${Math.ceil(texts.length / batchSize)} (${textBatch.length} texts)`);
        
        try {
          const embeddings = await func.computeSourceEmbeddings(textBatch);
          
          if (!embeddings || !Array.isArray(embeddings)) {
            console.error('❌ Invalid embeddings array received:', embeddings);
            continue;
          }
          
          if (embeddings.length !== resultBatch.length) {
            console.error(`❌ Embeddings array length (${embeddings.length}) doesn't match resultBatch length (${resultBatch.length})`);
            continue;
          }
          
          const recordsForDb = resultBatch.map((record, idx) => {
            const embedding = embeddings[idx];
            if (!embedding || !Array.isArray(embedding)) {
              console.warn(`⚠️  Invalid embedding at index ${idx}, using zero vector as fallback`);
              return {
                text: record.text,
                file: record.file,
                filePath: record.filePath,
                language: record.language,
                chunkIndex: record.chunkIndex,
                totalChunks: record.totalChunks,
                vector: new Array(1024).fill(0),
              };
            }
            if (embedding.length !== 1024) {
              console.warn(`⚠️  Embedding at index ${idx} has wrong length (${embedding.length}), using zero vector as fallback`);
              return {
                text: record.text,
                file: record.file,
                filePath: record.filePath,
                language: record.language,
                chunkIndex: record.chunkIndex,
                totalChunks: record.totalChunks,
                vector: new Array(1024).fill(0),
              };
            }
            return {
              text: record.text,
              file: record.file,
              filePath: record.filePath,
              language: record.language,
              chunkIndex: record.chunkIndex,
              totalChunks: record.totalChunks,
              vector: embedding,
            };
          });
          
          console.log(`💾 Saving ${recordsForDb.length} records to database...`);
          await table.add(recordsForDb);
          console.log(`✅ Successfully saved ${recordsForDb.length} records to database`);
          totalProcessed += recordsForDb.length;
          totalEmbeddedChunks += recordsForDb.length;
          
          const embeddingBatchEndTime = process.hrtime(embeddingBatchStartTime);
          const embeddingBatchTimeMs = (embeddingBatchEndTime[0] * 1000 + embeddingBatchEndTime[1] / 1e6).toFixed(2);
          
          console.log(`📊 Embedding batch stats: ${recordsForDb.length} embeddings in ${embeddingBatchTimeMs}ms`);
          console.log(`📈 Progress: ${totalEmbeddedChunks} chunks embedded and saved to database`);
          
        } catch (error) {
          console.error('❌ Failed to process embedding batch:', error);
          throw error;
        }
      }
    }
  }

  await func.dispose();
  const totalEndTime = process.hrtime(totalStartTime);
  const totalTimeInS = (totalEndTime[0] + totalEndTime[1] / 1e9).toFixed(2);
  
  console.log(`\n🎉 INGESTION COMPLETED!`);
  console.log(`📊 FINAL STATISTICS:`);
  console.log(`  ⏱️  Total time: ${totalTimeInS}s`);
  console.log(`  📁 Files processed: ${processedFiles}`);
  console.log(`  📄 Total chunks: ${totalChunks}`);
  console.log(`  🧠 Total embeddings: ${totalEmbeddedChunks}`);
  console.log(`  📝 Total tokens: ~${totalTokens.toLocaleString()}`);
  console.log(`  💾 Total file size: ${(totalFileSize / 1024 / 1024).toFixed(2)}MB`);
  console.log(`  ⚡ Average speed: ${(totalChunks / parseFloat(totalTimeInS)).toFixed(1)} chunks/second`);
  console.log(`  🧠 Embedding speed: ${(totalEmbeddedChunks / parseFloat(totalTimeInS)).toFixed(1)} embeddings/second`);
} 
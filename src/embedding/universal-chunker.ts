import * as path from 'path';
import Parser from 'web-tree-sitter';
import { QwenEmbeddingFunction, EMBEDDING_CONFIG } from './lancedb-utils';
import { rustQuery } from '../tools/parsers/queries/rust';
import { typescriptQuery } from '../tools/parsers/queries/typescript';
import { javascriptQuery } from '../tools/parsers/queries/javascript';
import { haskellQuery } from '../tools/parsers/queries/haskell';
import { purescriptQuery } from '../tools/parsers/queries/purescript';
import { pythonQuery } from '../tools/parsers/queries/python';

export interface CodeBlock {
  name: string;
  type: 'function' | 'struct' | 'enum' | 'trait' | 'impl' | 'module' | 'constant' | 'static' | 'macro';
  startLine: number;
  endLine: number;
  text: string;
  tokenCount: number;
}

export interface Chunk {
  text: string;
  blocks: CodeBlock[];
  totalTokens: number;
}

const QUERIES: Record<string, string> = {
  'rust': rustQuery,
  'typescript': typescriptQuery,
  'javascript': javascriptQuery,
  'python': pythonQuery,
  'haskell': haskellQuery,
  'purescript': purescriptQuery,
};

const WASM_PATHS: Record<string, string> = {
  'rust': 'node_modules/tree-sitter-wasms-complete/out/tree-sitter-rust.wasm',
  'typescript': 'node_modules/tree-sitter-wasms-complete/out/tree-sitter-typescript.wasm',
  'javascript': 'node_modules/tree-sitter-wasms-complete/out/tree-sitter-javascript.wasm',
  'python': 'node_modules/tree-sitter-wasms-complete/out/tree-sitter-python.wasm',
  'haskell': 'node_modules/tree-sitter-wasms-complete/out/tree-sitter-haskell.wasm',
  'purescript': 'node_modules/tree-sitter-wasms-complete/out/tree-sitter-purescript.wasm',
};

export class UniversalChunker {
  private parsers: Record<string, { parser: Parser; query: Parser.Query }> = {};
  private isInitialized = false;
  private extensionPath: string;

  constructor(extensionPath: string) {
    this.extensionPath = extensionPath;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await Parser.init();
    
      this.isInitialized = true;
    } catch (error) {
      console.warn('Failed to initialize Tree-sitter, will use token-based splitting:', error instanceof Error ? error.message : String(error));
      this.isInitialized = false;
    }
  }

  async chunkFile(filePath: string, fileContent: string): Promise<{
    chunks: string[];
    language: string;
    tokenCount: number;
  }> {
    const fileName = path.basename(filePath);
    const ext = path.extname(fileName).toLowerCase();
    const language = this.getLanguageFromExt(ext) || 'text';
    const contentToProcess = fileContent || '';
    const tokenCount = QwenEmbeddingFunction.model.tokenize(contentToProcess).length;
    
  
    
    const initialChunks = await this.splitTextByStructures(
      contentToProcess, 
      filePath, 
      EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK, 
      QwenEmbeddingFunction.model
    );
    
    // Post-process chunks to ensure none exceed max token limit
    const finalChunks: string[] = [];
    for (const chunk of initialChunks) {
      const chunkTokenCount = QwenEmbeddingFunction.model.tokenize(chunk).length;
      
      if (chunkTokenCount <= EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK) {
        // Chunk is within limit, keep as is
        finalChunks.push(chunk);
      } else {
      
        
        // Try to split using Tree-sitter first
        try {
          const subChunks = await this.splitTextByStructures(
            chunk,
            filePath,
            EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK,
            QwenEmbeddingFunction.model
          );
          
          // Check if Tree-sitter splitting helped
          const stillTooLarge = subChunks.some(subChunk => 
            QwenEmbeddingFunction.model.tokenize(subChunk).length > EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK
          );
          
          if (!stillTooLarge) {
          
            finalChunks.push(...subChunks);
          } else {
          
            
            // Try to extract smaller Tree-sitter blocks from the large chunk
            const ext = path.extname(filePath).toLowerCase().slice(1);
            const chunkLanguage = this.getLanguageFromExt(ext);
            
            if (chunkLanguage && this.isInitialized) {
              try {
                const parser = await this.getParser(chunkLanguage);
                if (parser) {
                  const blocks = await this.extractCodeBlocks(chunk, filePath, chunkLanguage, parser, QwenEmbeddingFunction.model);
                  
                  if (blocks.length > 1) {
                    // We found multiple blocks, try to create chunks from them
                    const blockChunks = this.createChunksFromBlocks(blocks, EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK, QwenEmbeddingFunction.model);
                    const blockChunkTexts = blockChunks.map(c => c.text);
                    
                    // Check if this helped
                    const blockChunksStillTooLarge = blockChunkTexts.some(blockChunk => 
                      QwenEmbeddingFunction.model.tokenize(blockChunk).length > EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK
                    );
                    
                    if (!blockChunksStillTooLarge) {
                    
                      finalChunks.push(...blockChunkTexts);
                    } else {
                    
                      const tokenChunks = await this.splitTextByTokens(chunk, EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK, QwenEmbeddingFunction.model);
                      finalChunks.push(...tokenChunks);
                    }
                  } else {
                  
                    const tokenChunks = await this.splitTextByTokens(chunk, EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK, QwenEmbeddingFunction.model);
                    finalChunks.push(...tokenChunks);
                  }
                } else {
                
                  const tokenChunks = await this.splitTextByTokens(chunk, EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK, QwenEmbeddingFunction.model);
                  finalChunks.push(...tokenChunks);
                }
              } catch (blockError) {
              
                const tokenChunks = await this.splitTextByTokens(chunk, EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK, QwenEmbeddingFunction.model);
                finalChunks.push(...tokenChunks);
              }
            } else {
            
              const tokenChunks = await this.splitTextByTokens(chunk, EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK, QwenEmbeddingFunction.model);
              finalChunks.push(...tokenChunks);
            }
          }
        } catch (error) {
        
          
          // Try to extract smaller Tree-sitter blocks from the large chunk
          const ext = path.extname(filePath).toLowerCase().slice(1);
          const chunkLanguage = this.getLanguageFromExt(ext);
          
          if (chunkLanguage && this.isInitialized) {
            try {
              const parser = await this.getParser(chunkLanguage);
              if (parser) {
                const blocks = await this.extractCodeBlocks(chunk, filePath, chunkLanguage, parser, QwenEmbeddingFunction.model);
                
                if (blocks.length > 1) {
                  // We found multiple blocks, try to create chunks from them
                  const blockChunks = this.createChunksFromBlocks(blocks, EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK, QwenEmbeddingFunction.model);
                  const blockChunkTexts = blockChunks.map(c => c.text);
                  
                  // Check if this helped
                  const blockChunksStillTooLarge = blockChunkTexts.some(blockChunk => 
                    QwenEmbeddingFunction.model.tokenize(blockChunk).length > EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK
                  );
                  
                  if (!blockChunksStillTooLarge) {
                  
                    finalChunks.push(...blockChunkTexts);
                  } else {
                  
                    const tokenChunks = await this.splitTextByTokens(chunk, EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK, QwenEmbeddingFunction.model);
                    finalChunks.push(...tokenChunks);
                  }
                } else {
                
                  const tokenChunks = await this.splitTextByTokens(chunk, EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK, QwenEmbeddingFunction.model);
                  finalChunks.push(...tokenChunks);
                }
              } else {
              
                const tokenChunks = await this.splitTextByTokens(chunk, EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK, QwenEmbeddingFunction.model);
                finalChunks.push(...tokenChunks);
              }
            } catch (blockError) {
            
              const tokenChunks = await this.splitTextByTokens(chunk, EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK, QwenEmbeddingFunction.model);
              finalChunks.push(...tokenChunks);
            }
          } else {
          
            const tokenChunks = await this.splitTextByTokens(chunk, EMBEDDING_CONFIG.MAX_TOKENS_PER_CHUNK, QwenEmbeddingFunction.model);
            finalChunks.push(...tokenChunks);
          }
        }
      }
    }
    
  
    
    return {
      chunks: finalChunks,
      language,
      tokenCount
    };
  }

  private getLanguageFromExt(ext: string): string | undefined {
    // Handle both with and without leading dot
    const normalizedExt = ext.startsWith('.') ? ext : `.${ext}`;
    switch (normalizedExt) {
      case '.ts': case '.tsx': return 'typescript';
      case '.js': case '.jsx': return 'javascript';
      case '.rs': return 'rust';
      case '.py': return 'python';
      case '.hs': return 'haskell';
      case '.purs': return 'purescript';
      default: return undefined;
    }
  }

  private async getParser(language: string): Promise<{ parser: Parser; query: Parser.Query } | null> {
    if (this.parsers[language]) {
      return this.parsers[language];
    }

    try {
      const wasmPath = WASM_PATHS[language];
      if (!wasmPath) {
        console.warn(`No WASM path found for language: ${language}`);
        return null;
      }

      // Resolve WASM path relative to extension path
      const resolvedWasmPath = path.resolve(this.extensionPath, wasmPath);
    
      
      // Check if file exists
      const fs = require('fs');
      if (!fs.existsSync(resolvedWasmPath)) {
        console.error(`WASM file does not exist: ${resolvedWasmPath}`);
        return null;
      }
    

      const languageModule = await Parser.Language.load(resolvedWasmPath);
    
      
      const parser = new Parser();
      parser.setLanguage(languageModule);
    
      
      const queryString = QUERIES[language];
      if (!queryString) {
        console.warn(`No query found for language: ${language}`);
        return null;
      }

      const query = languageModule.query(queryString);
    
      
      this.parsers[language] = { parser, query };
    
      return this.parsers[language];
    } catch (error) {
      console.error(`Failed to load parser for ${language}:`, error instanceof Error ? error.message : String(error));
      console.error(`Full error:`, error);
      return null;
    }
  }

  private async extractCodeBlocks(
    text: string, 
    filePath: string, 
    language: string, 
    parser: { parser: Parser; query: Parser.Query },
    model: any
  ): Promise<CodeBlock[]> {
    const tree = parser.parser.parse(text);
    const captures = parser.query.captures(tree.rootNode);
    const lines = text.split('\n');

  
  

    const blocks: CodeBlock[] = [];

    for (const capture of captures) {
      const { node, name } = capture;
      
      // Only process definition captures, skip name-only captures
      if (name.includes('definition') && !name.includes('name.definition')) {
        const startLine = node.startPosition.row;
        const endLine = node.endPosition.row;
        const blockText = lines.slice(startLine, endLine + 1).join('\n');
        
        // Get the name of the block
        let blockName = 'unknown';
        if (name.includes('function')) {
          blockName = node.text || 'function';
        } else if (name.includes('struct')) {
          blockName = node.text || 'struct';
        } else if (name.includes('enum')) {
          blockName = node.text || 'enum';
        } else if (name.includes('trait')) {
          blockName = node.text || 'trait';
        } else if (name.includes('impl')) {
          blockName = node.text || 'impl';
        } else if (name.includes('module')) {
          blockName = node.text || 'module';
        } else if (name.includes('constant')) {
          blockName = node.text || 'constant';
        } else if (name.includes('static')) {
          blockName = node.text || 'static';
        } else if (name.includes('macro')) {
          blockName = node.text || 'macro';
        } else if (name.includes('class')) {
          blockName = node.text || 'class';
        } else if (name.includes('interface')) {
          blockName = node.text || 'interface';
        } else if (name.includes('type')) {
          blockName = node.text || 'type';
        } else if (name.includes('variable')) {
          blockName = node.text || 'variable';
        }

        // Determine block type
        let blockType: CodeBlock['type'] = 'function';
        if (name.includes('struct')) {
          blockType = 'struct';
        } else if (name.includes('enum')) {
          blockType = 'enum';
        } else if (name.includes('trait')) {
          blockType = 'trait';
        } else if (name.includes('impl')) {
          blockType = 'impl';
        } else if (name.includes('module')) {
          blockType = 'module';
        } else if (name.includes('constant')) {
          blockType = 'constant';
        } else if (name.includes('static')) {
          blockType = 'static';
        } else if (name.includes('macro')) {
          blockType = 'macro';
        }

        // Use real token count
        const tokenCount = model.tokenize(blockText).length;

      
      

        blocks.push({
          name: blockName,
          type: blockType,
          startLine,
          endLine,
          text: blockText,
          tokenCount
        });
      }
    }

    // Sort blocks by start line
    blocks.sort((a, b) => a.startLine - b.startLine);
    
  
    
    return blocks;
  }

  private createChunksFromBlocks(blocks: CodeBlock[], maxTokens: number, model: any): Chunk[] {
    const chunks: Chunk[] = [];
    let currentChunk: Chunk = {
      text: '',
      blocks: [],
      totalTokens: 0
    };

    for (const block of blocks) {
      // If adding this block would exceed maxTokens, start a new chunk
      if (currentChunk.totalTokens + block.tokenCount > maxTokens && currentChunk.blocks.length > 0) {
        // If the block itself is too large, we need to split it
        if (block.tokenCount > maxTokens) {
          // Save current chunk if it has content
          if (currentChunk.blocks.length > 0) {
            chunks.push(currentChunk);
            currentChunk = { text: '', blocks: [], totalTokens: 0 };
          }
          
          // Split the large block
          const splitChunks = this.splitLargeBlock(block, maxTokens, model);
          chunks.push(...splitChunks);
        } else {
          // Save current chunk and start a new one
          chunks.push(currentChunk);
          currentChunk = {
            text: block.text,
            blocks: [block],
            totalTokens: block.tokenCount
          };
        }
      } else {
        // Add block to current chunk
        if (currentChunk.text) {
          currentChunk.text += '\n\n' + block.text;
        } else {
          currentChunk.text = block.text;
        }
        currentChunk.blocks.push(block);
        currentChunk.totalTokens += block.tokenCount;
      }
    }

    // Add the last chunk if it has content
    if (currentChunk.blocks.length > 0) {
      chunks.push(currentChunk);
    }

    return chunks;
  }

  private splitLargeBlock(block: CodeBlock, maxTokens: number, model: any): Chunk[] {
    const chunks: Chunk[] = [];
    const tokens = model.tokenize(block.text);
    
    let currentChunk = '';
    let currentTokens = 0;

    for (let i = 0; i < tokens.length; i++) {
      const token = tokens[i];
      const tokenText = model.detokenize([token]);
      
      if (currentTokens + 1 > maxTokens) {
        // Save current chunk
        if (currentChunk) {
          chunks.push({
            text: currentChunk,
            blocks: [{
              ...block,
              text: currentChunk,
              tokenCount: currentTokens
            }],
            totalTokens: currentTokens
          });
        }
        
        // Start new chunk
        currentChunk = tokenText;
        currentTokens = 1;
      } else {
        currentChunk += tokenText;
        currentTokens++;
      }
    }

    // Add the last chunk
    if (currentChunk) {
      chunks.push({
        text: currentChunk,
        blocks: [{
          ...block,
          text: currentChunk,
          tokenCount: currentTokens
        }],
        totalTokens: currentTokens
      });
    }

    return chunks;
  }

  private async splitTextByTokens(text: string, maxTokens: number, model: any): Promise<string[]> {
    // Handle edge cases for text input
    if (!text || typeof text !== 'string') {
    
      return [""];
    }
    
    // Handle completely empty string
    if (text.trim().length === 0) {
    
      return [""];
    }
    
    const tokens = model.tokenize(text);
  
    
    const chunks: string[] = [];
    for (let i = 0; i < tokens.length; i += maxTokens) {
      const tokenChunk = tokens.slice(i, i + maxTokens);
      const chunkText = model.detokenize(tokenChunk);
      chunks.push(chunkText);
    }
    
    if (chunks.length === 0) {
      chunks.push(""); // Ensure at least one chunk is returned
    }
    
  
    return chunks;
  }

  async splitTextByStructures(
    text: string,
    filePath: string,
    maxTokens: number,
    model: any
  ): Promise<string[]> {
    const ext = path.extname(filePath).toLowerCase().slice(1);
    const language = this.getLanguageFromExt(ext);
    
    if (!language || !this.isInitialized) {
    
      return this.splitTextByTokens(text, maxTokens, model);
    }

    try {
      const parser = await this.getParser(language);
      if (!parser) {
      
        return this.splitTextByTokens(text, maxTokens, model);
      }

      const blocks = await this.extractCodeBlocks(text, filePath, language, parser, model);
      const chunks = this.createChunksFromBlocks(blocks, maxTokens, model);
      return chunks.map(chunk => chunk.text);
    } catch (error) {
      console.warn(`Failed to parse ${filePath} with Tree-sitter, falling back to token-based splitting:`, error instanceof Error ? error.message : String(error));
      return this.splitTextByTokens(text, maxTokens, model);
    }
  }
} 
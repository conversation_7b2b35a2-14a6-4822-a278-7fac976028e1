import * as path from 'path';
import { connect } from '@lancedb/lancedb';
import {
  DB_PATH,
  BM25_TABLE_NAME,
  sanitizePathToDirectoryName,
  getRetrievalLimit,
} from './lancedb-utils';

export interface BM25QueryResult {
  text: string;
  filePath: string;
  language: string;
  _score?: number;
}

export class BM25QueryService {
  private static instance: BM25QueryService;

  private constructor() {}

  public static getInstance(): BM25QueryService {
    if (!BM25QueryService.instance) {
      BM25QueryService.instance = new BM25QueryService();
    }
    return BM25QueryService.instance;
  }

  private async getDatabaseConnection(folderPath: string, extensionPath: string): Promise<any> {
    const repoId = sanitizePathToDirectoryName(folderPath);

    // Try to find the correct embeddings directory
    const fs = require('fs');
    const path = require('path');
    
    // Check multiple possible locations for embeddings
    const possiblePaths = [
      // Development path (if running from source)
      path.join(process.cwd(), '.embeddings', repoId),
      // Extension path (production)
      path.join(extensionPath, '.embeddings', repoId),
      // Fallback: workspace root
      path.join(folderPath, '.embeddings', repoId)
    ];
    
    let embeddingsDir = null;
    for (const possiblePath of possiblePaths) {
      if (fs.existsSync(possiblePath)) {
        embeddingsDir = possiblePath;
        break;
      }
    }
    
    if (!embeddingsDir) {
      // If no existing directory found, use the extension path as default
      embeddingsDir = path.join(extensionPath, '.embeddings', repoId);
    }

    const dbPath = path.join(embeddingsDir, DB_PATH);

    try {
      if (!fs.existsSync(dbPath)) {
        throw new Error(`Database not found at ${dbPath}. Please run indexing first.`);
      }

      const db = await connect(dbPath);
      const table = await db.openTable(BM25_TABLE_NAME);
      
      return table;
    } catch (error) {
      if (error instanceof Error && error.message.includes('Database not found')) {
        throw error;
      }
      throw new Error(`Failed to connect to BM25 database at ${dbPath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async query(
    query: string, 
    folderPath: string, 
    extensionPath: string, 
    options: { limit?: number } = {}
  ): Promise<BM25QueryResult[]> {
    const bm25StartTime = process.hrtime();
    
    try {
      if (!query || typeof query !== 'string' || query.trim().length === 0) {
        throw new Error('Query must be a non-empty string');
      }

      const table = await this.getDatabaseConnection(folderPath, extensionPath);
      const limit = options.limit || getRetrievalLimit(5);

      console.log(`   🔍 BM25: Executing full-text search for "${query}" (limit: ${limit})`);

      // Use full-text search for BM25 - one entry per file
      const queryStartTime = process.hrtime();
      const results = await table.query()
        .fullTextSearch(query)
        .limit(limit)
        .select(['text', 'filePath', 'language'])
        .toArray();
      const queryEndTime = process.hrtime(queryStartTime);
      const queryDuration = (queryEndTime[0] * 1000 + queryEndTime[1] / 1000000).toFixed(2);

      console.log(`   ✅ BM25: Found ${results.length} files in ${queryDuration}ms`);

      // Sort by score and limit to requested number of files
      const finalResults = results
        .sort((a, b) => (b._score || 0) - (a._score || 0))
        .slice(0, limit);

      console.log(`   📄 BM25: Returning ${finalResults.length} files`);

      // Log some result details for debugging
      if (finalResults.length > 0) {
        console.log(`   📄 BM25: Sample results:`);
        finalResults.slice(0, 2).forEach((result: any, i: number) => {
          const fileName = result.filePath ? result.filePath.split('/').pop() : 'unknown';
          const score = result._score ? result._score.toFixed(4) : 'N/A';
          console.log(`      ${i + 1}. ${fileName} (${result.language}) - Score: ${score}`);
        });
      }

      return finalResults;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`   ❌ BM25 query failed: ${errorMessage}`);
      
      if (errorMessage.includes('Database not found')) {
        throw new Error(`BM25 database not found. Please run "Xyne: Index Codebase" first to create the search index.`);
      }
      
      throw new Error(`BM25 query failed: ${errorMessage}`);
    } finally {
      const bm25EndTime = process.hrtime(bm25StartTime);
      const totalDuration = (bm25EndTime[0] * 1000 + bm25EndTime[1] / 1000000).toFixed(2);
      console.log(`   ⏱️  BM25: Total time including setup: ${totalDuration}ms`);
    }
  }

  public async isIndexAvailable(folderPath: string, extensionPath: string): Promise<boolean> {
    try {
      await this.getDatabaseConnection(folderPath, extensionPath);
      return true;
    } catch (error) {
      return false;
    }
  }

  public async getIndexStats(folderPath: string, extensionPath: string): Promise<{ totalChunks: number; totalFiles: number } | null> {
    try {
      const table = await this.getDatabaseConnection(folderPath, extensionPath);
      const allRecords = await table.query().select(['filePath']).toArray();
      
      const uniqueFiles = new Set(allRecords.map((r: any) => r.filePath));
      
      return {
        totalChunks: allRecords.length, // Now represents total files since no chunking
        totalFiles: uniqueFiles.size
      };
    } catch (error) {
      return null;
    }
  }
}

// Backward compatibility function
export async function queryBM25(query: string, folderPath: string, extensionPath: string): Promise<BM25QueryResult[]> {
  const queryService = BM25QueryService.getInstance();
  return queryService.query(query, folderPath, extensionPath);
} 
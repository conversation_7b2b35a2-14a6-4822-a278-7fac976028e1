import * as path from 'path';
import * as fs from 'fs';
import { connect, Index } from '@lancedb/lancedb';
import { getAllFiles, splitTextByTokens, detectLanguage, DB_PATH, sanitizePathToDirectoryName, EMBEDDING_CONFIG } from './lancedb-utils';
import ignore from 'ignore';

export const BM25_TABLE_NAME = 'bm25_index';

export async function ingestCodebaseBM25(
  folderToEmbed: string, 
  extensionPath: string, 
  options?: { 
    fastMode?: boolean;
  }
) {
  const totalStartTime = process.hrtime();
  const fastMode = options?.fastMode ?? false;
  folderToEmbed = path.resolve(folderToEmbed);
  
  console.log(`🚀 Ingesting files into LanceDB BM25 (Universal Chunker, ${fastMode ? 'FAST MODE' : 'NORMAL MODE'})  `, folderToEmbed);
  console.log(`📊 Configuration: BATCH_SIZE=${EMBEDDING_CONFIG.BATCH_SIZE}, FILE_BATCH_SIZE=${EMBEDDING_CONFIG.FILE_BATCH_SIZE}`);

  const gitignorePath = path.join(folderToEmbed, '.gitignore');
  const ig = ignore();
  ig.add('.git');
  if (fs.existsSync(gitignorePath)) {
    const gitignoreContent = fs.readFileSync(gitignorePath, 'utf-8');
    ig.add(gitignoreContent);
    console.log('📁 Loaded .gitignore rules.');
  }

  const repoId = sanitizePathToDirectoryName(folderToEmbed);
  const embeddingsDir = path.join(extensionPath, '.embeddings', repoId);
  if (!fs.existsSync(embeddingsDir)) {
    fs.mkdirSync(embeddingsDir, { recursive: true });
  }
  const dbPath = path.join(embeddingsDir, DB_PATH);
  console.log('🗄️  DB PATH ', dbPath);

  // No chunking for BM25 - one entry per file
  const processFile = (text: string): string => {
    // Clean and normalize text for better search
    return text
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  };

  const db = await connect(dbPath);

  let table;
  try {
    table = await db.openTable(BM25_TABLE_NAME);
    console.log(`📋 Opened existing BM25 table '${BM25_TABLE_NAME}'.`);
  } catch (e) {
    try {
      const dummy = [{
        text: "dummy",
        file: "dummy.txt",
        filePath: "/dummy.txt",
        language: "Text",
        // No chunking - one entry per file
      }];
      table = await db.createTable(BM25_TABLE_NAME, dummy, { mode: "create" });
      await table.delete("file = 'dummy.txt'");
      console.log(`📋 BM25 table '${BM25_TABLE_NAME}' created.`);
    } catch (createError) {
      // If table creation fails (e.g., already exists), try to open it again
      console.log(`⚠️  BM25 table creation failed, trying to open existing table: ${createError instanceof Error ? createError.message : String(createError)}`);
      table = await db.openTable(BM25_TABLE_NAME);
      console.log(`📋 Opened existing BM25 table '${BM25_TABLE_NAME}'.`);
    }
  }

  // Note: We'll create the full-text search index AFTER adding all data
  console.log(`📋 Full-text search index will be created after data ingestion`);

  const allFiles = await getAllFiles(folderToEmbed, ig, folderToEmbed);
  console.log(`📁 Found ${allFiles.length} files in ${folderToEmbed} (after .gitignore filter)`);

  const existingRecords = await table.query().select(["filePath"]).toArray();
  
  // Check which files are already indexed (no chunking - one entry per file)
  const indexedFiles = new Set<string>();
  existingRecords.forEach(record => {
    indexedFiles.add(record.filePath);
  });
  
  console.log(`📊 Found ${indexedFiles.size} already indexed files`);
  
  const newFiles = allFiles.filter(filePath => !indexedFiles.has(filePath));
  console.log(`🆕 Found ${newFiles.length} new files to index out of ${allFiles.length} total files`);

  if (newFiles.length === 0) {
    console.log("🎉 All files are already indexed!");
    return;
  }

  const BATCH_SIZE = fastMode ? Math.min(EMBEDDING_CONFIG.FILE_BATCH_SIZE * 2, 20) : EMBEDDING_CONFIG.FILE_BATCH_SIZE;
  let processedFiles = 0;
  let totalChunks = 0;
  let totalFileSize = 0;

  console.log(`\n🔄 Starting file processing with batch size: ${BATCH_SIZE}`);
  console.log(`📈 Progress tracking enabled - will show detailed metrics per batch\n`);

  for (let i = 0; i < newFiles.length; i += BATCH_SIZE) {
    const batchStartTime = process.hrtime();
    const batchFiles = newFiles.slice(i, i + BATCH_SIZE);
    const batchNumber = Math.floor(i / BATCH_SIZE) + 1;
    const totalBatches = Math.ceil(newFiles.length / BATCH_SIZE);
    const progressPercent = ((i / newFiles.length) * 100).toFixed(1);
    
    console.log(`\n📦 Processing batch ${batchNumber}/${totalBatches} (${batchFiles.length} files) - Progress: ${processedFiles}/${newFiles.length} files (${progressPercent}%)`);
    console.log(`⏱️  Batch start time: ${new Date().toLocaleTimeString()}`);

    const batchPromises = batchFiles.map(async (filePath) => {
      if (fs.statSync(filePath).isDirectory()) return null;
      try {
        const fileContent = fs.readFileSync(filePath, 'utf-8');
        const fileName = path.basename(filePath);
        const fileSize = fs.statSync(filePath).size;
        totalFileSize += fileSize;
        
        const processedText = processFile(fileContent);
        processedFiles++;
        totalChunks += 1; // One entry per file
        
        console.log(`  📄 ${fileName}: 1 entry, ${(fileSize / 1024).toFixed(1)}KB`);
        
        return [{
          text: processedText,
          file: fileName,
          filePath,
          language: detectLanguage(fileName),
          // No chunking - one entry per file
        }];
      } catch (error) {
        console.error(`❌ Error processing file ${filePath}:`, error);
        processedFiles++;
        return null;
      }
    });

    const batchResults = await Promise.all(batchPromises);
    const validResults = batchResults.filter(result => result !== null).flat();
    
    if (validResults.length > 0) {
      try {
        await table.add(validResults);
        console.log(`  ✅ Added ${validResults.length} chunks to BM25 index`);
      } catch (error) {
        console.error(`❌ Error adding batch to BM25 index:`, error);
      }
    }

    const batchEndTime = process.hrtime(batchStartTime);
    const batchDuration = (batchEndTime[0] * 1000 + batchEndTime[1] / 1000000).toFixed(2);
    console.log(`  ⏱️  Batch ${batchNumber} completed in ${batchDuration}ms`);
  }

  const totalEndTime = process.hrtime(totalStartTime);
  const totalDuration = (totalEndTime[0] * 1000 + totalEndTime[1] / 1000000).toFixed(2);
  
  console.log(`\n🎉 BM25 Indexing completed!`);
  console.log(`📊 Summary:`);
  console.log(`  📁 Files processed: ${processedFiles}`);
  console.log(`  📄 Total chunks: ${totalChunks}`);
  console.log(`  💾 Total file size: ${(totalFileSize / 1024 / 1024).toFixed(2)}MB`);
  console.log(`  ⏱️  Total time: ${totalDuration}ms`);
  console.log(`  🚀 Average: ${processedFiles > 0 ? (Number(totalDuration) / processedFiles).toFixed(2) : '0'}ms per file`);
  
  // Create full-text search index AFTER all data has been added
  console.log(`\n🔍 Creating full-text search index...`);
  const indexStartTime = process.hrtime();
  try {
    await table.createIndex("text", { config: Index.fts() });
    const indexEndTime = process.hrtime(indexStartTime);
    const indexDuration = (indexEndTime[0] * 1000 + indexEndTime[1] / 1000000).toFixed(2);
    console.log(`✅ Full-text search index created successfully in ${indexDuration}ms`);
  } catch (error) {
    console.error(`❌ Failed to create full-text search index:`, error);
    throw new Error(`BM25 indexing failed: Could not create full-text search index - ${error instanceof Error ? error.message : String(error)}`);
  }
  
  console.log(`\n✅ BM25 index is ready for fast text-based search!`);
} 
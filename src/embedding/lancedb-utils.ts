import * as path from 'path';
import * as fs from 'fs';
import { EmbeddingFunction } from '@lancedb/lancedb/embedding';
import { Float32 } from 'apache-arrow';
import { register } from '@lancedb/lancedb/embedding';

const modelDir = path.join(process.cwd(), '.models');
export const MODEL_PATH = path.join(modelDir, 'Qwen3-Embedding-0.6B-Q8_0.gguf');

// Performance configuration
export const EMBEDDING_CONFIG = {
  BATCH_SIZE: 128,                   // Increased for GPU: Number of texts to process in parallel
  FILE_BATCH_SIZE: 30,               // Increased for GPU: Number of files to process in parallel
  MAX_TOKENS_PER_CHUNK: 3000,        // Max tokens per text chunk
  CONTEXT_SIZE: 16384,              // Increased context size for GPU efficiency
  EMBEDDING_BATCH_SIZE: 100,         // Increased for GPU: Number of texts to embed in sequential batches
} as const;
// Working CPU configurations
// export const EMBEDDING_CONFIG = {
//   BATCH_SIZE: 64,                    // Increased for GPU: Number of texts to process in parallel
//   FILE_BATCH_SIZE: 20,               // Increased for GPU: Number of files to process in parallel
//   MAX_TOKENS_PER_CHUNK: 200,        // Max tokens per text chunk
//   CONTEXT_SIZE: 8192,               // Model context size (also used as batch size)
//   EMBEDDING_BATCH_SIZE: 50,          // Increased for GPU: Number of texts to embed in sequential batches
// } 
const ALLOWED_EXTENSIONS = new Set([
  // Documents
  '.md', '.txt', '.json', '.yaml', '.yml', '.xml', '.html', '.css', '.csv', '.tsv',
  // Code
  '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.kt', '.gradle', '.c', '.cpp', '.h', '.hpp',
  '.cs', '.go', '.rs', '.rb', '.php', '.sh', '.bash', '.sql', '.swift', '.m', '.mm',
  '.r', '.pl', '.pm', '.lua', '.scala', '.dart', '.ex', '.exs', '.erl', '.hrl',
  '.vb', '.vbs', '.fs', '.fsi', '.clj', '.cljs', '.cljc', '.edn', '.hs',
  // Configs
  '.toml', '.ini', '.cabal'
]);

const ALLOWED_FILENAMES = new Set([
  'Dockerfile',
  'Makefile',
  'Jenkinsfile'
]);

export { ALLOWED_EXTENSIONS, ALLOWED_FILENAMES };

export function sanitizeForQwen(text: string): string {
  // Handle edge cases for text input
  if (!text || typeof text !== 'string') {
    return '[empty content]';
  }
  
  return text
    // Remove HTML tags
    .replace(/<[^>]*>/g, '')
    // Remove Markdown image/link syntax
    .replace(/!\[[^\]]*\]\([^\)]+\)/g, '')
    .replace(/\[[^\]]*\]\([^\)]+\)/g, '')
    // Remove raw URLs
    .replace(/https?:\/\/[^\s)]+/g, '')
    // Replace non-breaking spaces, en-dashes, em-dashes
    .replace(/\u00A0|\u2013|\u2014/g, ' ')
    // Replace curly quotes with straight quotes
    .replace(/[""]/g, '"').replace(/['']/g, "'")
    // Remove emojis and non-ASCII (optional: keep if model supports)
    .replace(/[^\x00-\x7F]/g, '')
    // Normalize whitespace
    .replace(/\s+/g, ' ')
    .replace(/⚠️/g, '')    
    .replace(/<your_char>/g, '') 
    .trim();
}

/**
 * Converts a full file path into a safe string that can be used as a directory name.
 * Replaces path separators with underscores and removes characters that are invalid in folder names.
 * e.g., /Users/<USER>/project -> Users_dev_project
 * e.g., C:\Users\<USER>\project -> C_Users_dev_project
 */
export function sanitizePathToDirectoryName(repoPath: string): string {
  let sanitized = repoPath.replace(/\\/g, '_').replace(/\//g, '_');
  if (sanitized.startsWith('_')) {
    sanitized = sanitized.substring(1);
  }
  sanitized = sanitized.replace(/:/g, ''); // For Windows drive letters
  return sanitized;
}

// System check for GPU capabilities
export function checkGPUCapabilities(): void {
  const isAppleSilicon = process.platform === 'darwin' && process.arch === 'arm64';
  const isLinux = process.platform === 'linux';
  const isWindows = process.platform === 'win32';
  const isX64 = process.arch === 'x64';
  const isARM64 = process.arch === 'arm64';
  
  console.log('\n🔍 GPU Capability Check:');
  console.log(`  Platform: ${process.platform}`);
  console.log(`  Architecture: ${process.arch}`);
  console.log(`  Apple Silicon: ${isAppleSilicon ? '✅ Yes' : '❌ No'}`);
  
  if (isAppleSilicon) {
    console.log(`  Metal Support: ✅ Available`);
    console.log(`  GPU Acceleration: ✅ Will be attempted`);
    console.log(`  GPU Type: Apple Silicon (Metal)`);
  } else if (isLinux && isX64) {
    console.log(`  CUDA Support: ✅ Available (NVIDIA GPUs)`);
    console.log(`  Vulkan Support: ✅ Available (AMD/Intel GPUs)`);
    console.log(`  GPU Acceleration: ✅ Will be attempted`);
    console.log(`  GPU Type: NVIDIA CUDA / AMD Vulkan / Intel Vulkan`);
  } else if (isWindows && isX64) {
    console.log(`  CUDA Support: ✅ Available (NVIDIA GPUs)`);
    console.log(`  Vulkan Support: ✅ Available (AMD/Intel GPUs)`);
    console.log(`  GPU Acceleration: ✅ Will be attempted`);
    console.log(`  GPU Type: NVIDIA CUDA / AMD Vulkan / Intel Vulkan`);
  } else {
    console.log(`  GPU Support: ❌ Limited or not available`);
    console.log(`  GPU Acceleration: ❌ CPU-only mode will be used`);
  }
  
  // Check Node.js version for compatibility
  const nodeVersion = process.version;
  console.log(`  Node.js Version: ${nodeVersion}`);
  
  console.log(''); // Empty line for readability
}

// Enhanced GPU detection and configuration
export function getGPUConfig() {
  const isAppleSilicon = process.platform === 'darwin' && process.arch === 'arm64';
  const isLinux = process.platform === 'linux';
  const isWindows = process.platform === 'win32';
  const isX64 = process.arch === 'x64';
  
  if (isAppleSilicon) {
    return {
      type: 'metal',
      gpuLayers: -1, // All layers on GPU
      metal: true,
      metalDevice: 0,
      batchSize: EMBEDDING_CONFIG.CONTEXT_SIZE * 4, // Larger batches for GPU
      contextSize: EMBEDDING_CONFIG.CONTEXT_SIZE * 2,
    };
  } else if ((isLinux || isWindows) && isX64) {
    // Try CUDA first, then Vulkan
    return {
      type: 'cuda',
      gpuLayers: -1, // All layers on GPU
      cuda: true,
      cudaDevice: 0,
      batchSize: EMBEDDING_CONFIG.CONTEXT_SIZE * 4,
      contextSize: EMBEDDING_CONFIG.CONTEXT_SIZE * 2,
      fallbackToVulkan: true,
    };
  } else {
    return {
      type: 'cpu',
      gpuLayers: 0,
      batchSize: EMBEDDING_CONFIG.EMBEDDING_BATCH_SIZE,
      contextSize: EMBEDDING_CONFIG.CONTEXT_SIZE,
    };
  }
}

export async function getModel(modelPath: string): Promise<any> {
    const gpuConfig = getGPUConfig();
    
    try {
        const { getLlama } = await import('node-llama-cpp');
        const llama = await getLlama();
        
        console.log(`🔧 System detected: ${process.platform} ${process.arch}`);
        console.log(`🚀 Attempting to load model with ${gpuConfig.type.toUpperCase()} acceleration`);
        
        const modelConfig: any = {
            modelPath: modelPath,
            gpuLayers: gpuConfig.gpuLayers,
        };
        
        // Add GPU-specific configurations
        if (gpuConfig.type === 'metal') {
            modelConfig.metal = true;
            modelConfig.metalDevice = gpuConfig.metalDevice;
        } else if (gpuConfig.type === 'cuda') {
            modelConfig.cuda = true;
            modelConfig.cudaDevice = gpuConfig.cudaDevice;
        }
        
        const model = await llama.loadModel(modelConfig);
        
        console.log(`✅ Model loaded successfully with ${gpuConfig.type.toUpperCase()} acceleration`);
        console.log(`🔍 Model loaded with gpuLayers: ${gpuConfig.gpuLayers}`);
        
        return model;
    } catch (error) {
        console.error('❌ Failed to load model with GPU acceleration, falling back to CPU:', error instanceof Error ? error.message : String(error));
        
        // Try Vulkan fallback for CUDA failures
        if (gpuConfig.type === 'cuda' && gpuConfig.fallbackToVulkan) {
            try {
                console.log('🔄 Attempting Vulkan fallback...');
                const { getLlama } = await import('node-llama-cpp');
                const llama = await getLlama();
                const model = await llama.loadModel({
                    modelPath: modelPath,
                    gpuLayers: -1,
                    // Note: Vulkan support may not be available in all node-llama-cpp versions
                    // Using GPU layers only for now
                });
                console.log('✅ Model loaded successfully with GPU acceleration (fallback mode)');
                return model;
            } catch (vulkanError) {
                console.error('❌ GPU fallback failed:', vulkanError instanceof Error ? vulkanError.message : String(vulkanError));
            }
        }
        
        // Fallback to CPU if all GPU attempts fail
        try {
            console.log('🔄 Attempting CPU fallback...');
            const { getLlama } = await import('node-llama-cpp');
            const llama = await getLlama();
            const model = await llama.loadModel({
                modelPath: modelPath,
                gpuLayers: 0, // Force CPU mode
            });
            console.log('✅ Model loaded successfully with CPU fallback mode');
            return model;
        } catch (fallbackError) {
            console.error('❌ Failed to load model with CPU fallback:', fallbackError instanceof Error ? fallbackError.message : String(fallbackError));
            if (fs.existsSync(modelPath)) {
                console.log('🗑️  Deleting corrupted model file...');
                fs.unlinkSync(modelPath);
            }
            throw new Error('Failed to load model. Please try again.');
        }
    }
}

@register("QwenEmbeddingFunction")
export class QwenEmbeddingFunction extends EmbeddingFunction<string> {
  private modelPath: string;
  public static model: any = null;
  public static context: any = null;
  private embeddingCounter: number = 0;
  private maxEmbeddingsBeforeReset: number = 200; // Reset every 50 embeddings (testing more frequent resets for performance)
  private consecutiveFailures: number = 0;
  private maxConsecutiveFailures: number = 10; // Increased threshold since batch size is now correct

  constructor(options?: { modelPath?: string }) {
    super();
    this.modelPath = options?.modelPath || MODEL_PATH;
  }

  async init() {
    if (QwenEmbeddingFunction.context) {
      return;
    }
    console.log("Initializing QwenEmbeddingFunction model...");
    QwenEmbeddingFunction.model = await getModel(this.modelPath);
    
    // Get GPU configuration for optimal settings
    const gpuConfig = getGPUConfig();
    const contextSize = gpuConfig.contextSize;
    const batchSize = gpuConfig.batchSize;
    
    QwenEmbeddingFunction.context = await QwenEmbeddingFunction.model.createEmbeddingContext({
      contextSize: contextSize,
      batchSize: batchSize, // Optimized batch size for GPU
    });
    
    // Adjust reset threshold based on GPU usage
    this.maxEmbeddingsBeforeReset = gpuConfig.type !== 'cpu' ? 1000 : 200; // Higher threshold for GPU
    
    console.log(`QwenEmbeddingFunction model initialized with ${gpuConfig.type.toUpperCase()}-optimized settings`);
    console.log(`  Context Size: ${contextSize}`);
    console.log(`  Batch Size: ${batchSize}`);
    console.log(`  Reset Threshold: ${this.maxEmbeddingsBeforeReset} embeddings`);
  }

  ndims() {
    return 1024;
  }
  embeddingDataType() {
    return new Float32();
  }
  protected getSensitiveKeys(): string[] {
    return [];
  }
  async computeQueryEmbeddings(data: string): Promise<number[]> {
    if (!QwenEmbeddingFunction.context) {
      throw new Error("Embedding function not initialized. Call init() first.");
    }
    
    // Handle edge cases for text input
    if (!data || typeof data !== 'string') {
      data = '[empty content]';
    }
    
    const startTime = process.hrtime();
    const tokenCount = QwenEmbeddingFunction.model.tokenize(data).length;
    
    const embedding = await QwenEmbeddingFunction.context.getEmbeddingFor(data);
    const endTime = process.hrtime(startTime);
    const timeMs = (endTime[0] * 1000 + endTime[1] / 1e6).toFixed(2);
    
    console.log(`⚡ Query embedding completed in ${timeMs}ms (${data.length} chars, ${tokenCount} tokens)`);
    
    return Array.from(embedding.vector);
  }
  async computeSourceEmbeddings(data: string[], onBatchComplete?: (batchResults: number[][], batchIndex: number) => Promise<void>): Promise<number[][]> {
    if (!QwenEmbeddingFunction.context) {
      throw new Error("Embedding function not initialized. Call init() first.");
    }
    
    // Clean all texts first
    const cleanTexts = data.map(text => sanitizeForQwen(text));
    
    // Get GPU configuration to decide processing strategy
    const gpuConfig = getGPUConfig();
    
    // Use parallel processing for GPU, sequential for CPU
    if (gpuConfig.type !== 'cpu') {
      return await this.processEmbeddingsParallel(cleanTexts, onBatchComplete);
    } else {
      return await this.processEmbeddingsSequentially(cleanTexts, onBatchComplete);
    }
  }

  private async processEmbeddingsSequentially(texts: string[], onBatchComplete?: (batchResults: number[][], batchIndex: number) => Promise<void>): Promise<number[][]> {
    const results: number[][] = [];
    const batchSize = EMBEDDING_CONFIG.EMBEDDING_BATCH_SIZE;
    
    console.log(`🔄 Processing ${texts.length} texts sequentially in batches of ${batchSize}`);
    
    // Calculate total tokens for logging
    const totalTokens = texts.reduce((sum, text) => {
      return sum + (text.split(/\s+/).length || 0);
    }, 0);
    console.log(`📝 Total estimated tokens: ~${totalTokens.toLocaleString()}`);
    
    // Process texts in batches sequentially to avoid thread-safety issues
    for (let i = 0; i < texts.length; i += batchSize) {
      const batchStartTime = process.hrtime();
      const batch = texts.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      const totalBatches = Math.ceil(texts.length / batchSize);
      
      console.log(`\n🧠 Processing embedding batch ${batchNumber}/${totalBatches} (${batch.length} texts)`);
      console.log(`⏱️  Batch start time: ${new Date().toLocaleTimeString()}`);
      
      // Process this batch sequentially but efficiently
      const batchResults = await this.processBatchSequentially(batch);
      results.push(...batchResults);
      
      const batchEndTime = process.hrtime(batchStartTime);
      const batchTimeMs = (batchEndTime[0] * 1000 + batchEndTime[1] / 1e6).toFixed(2);
      
      console.log(`✅ Completed embedding batch ${batchNumber}/${totalBatches} in ${batchTimeMs}ms`);
      console.log(`📊 Batch performance: ${(batch.length / parseFloat(batchTimeMs) * 1000).toFixed(1)} embeddings/second`);
      console.log(`⏱️  Batch end time: ${new Date().toLocaleTimeString()}`);
      
      // Call the callback if provided, allowing immediate database save
      if (onBatchComplete) {
        console.log(`💾 Saving batch ${batchNumber} to database...`);
        await onBatchComplete(batchResults, batchNumber - 1);
        console.log(`✓ Batch ${batchNumber} saved to database`);
      }
    }
    
    console.log(`\n🎉 Sequential processing completed: ${results.length} embeddings generated`);
    return results;
  }

  private async processBatchSequentially(texts: string[]): Promise<number[][]> {
    const results: number[][] = [];
    const failedTexts = new Set<number>(); // Track which texts have failed
    
    console.log(`🔄 Starting to process ${texts.length} texts sequentially...`);
    
    for (let i = 0; i < texts.length; i++) {
      const text = texts[i];
      const startTime = process.hrtime();
      
      // Add a small delay between embeddings to prevent overwhelming the model
      if (i > 0) {
        await new Promise(resolve => setTimeout(resolve, 10)); // 10ms delay
      }
      
      try {
        // Validate and clean text (no truncation)
        const processedText = this.validateAndTruncateText(text);
        
        // Check if we need to reset the context
        if (this.embeddingCounter >= this.maxEmbeddingsBeforeReset) {
          console.log(`🔄 Performing scheduled context reset after ${this.embeddingCounter} embeddings...`);
          await this.resetEmbeddingContext();
          this.embeddingCounter = 0;
        }
        
        const embedding = await this.getEmbeddingWithRetry(processedText, i + 1, texts.length);
        this.embeddingCounter++;
        this.consecutiveFailures = 0; // Reset failure counter on success
        
        const endTime = process.hrtime(startTime);
        const timeMs = (endTime[0] * 1000 + endTime[1] / 1e6).toFixed(2);
        
        // Log progress for every embedding
        const tokenCount = QwenEmbeddingFunction.model.tokenize(processedText).length;
        console.log(`  ✅ Embedding ${i + 1}/${texts.length} completed in ${timeMs}ms (${tokenCount} tokens, counter: ${this.embeddingCounter})`);
        
        results.push(Array.from(embedding.vector));
        
      } catch (error) {
        console.error(`❌ Failed to process embedding ${i + 1}/${texts.length}:`, error instanceof Error ? error.message : String(error));
        failedTexts.add(i);
        this.consecutiveFailures++;
        
        // If we have too many consecutive failures, perform a full reset
        if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
          console.error(`❌ Too many consecutive failures (${this.consecutiveFailures}), performing full model reset...`);
          try {
            await this.resetFullModel();
            this.consecutiveFailures = 0;
          } catch (resetError) {
            console.error('❌ Full model reset failed, attempting complete system restart...');
            try {
              await this.restartEmbeddingSystem();
              this.consecutiveFailures = 0;
            } catch (restartError) {
              console.error('❌ Complete system restart failed:', restartError);
              throw new Error('Unable to recover from embedding system corruption');
            }
          }
          
          // If we still have too many failed texts, skip the rest of this batch
          if (failedTexts.size > texts.length * 0.3) { // If more than 30% failed
            console.warn(`⚠️  Too many failed texts in batch (${failedTexts.size}/${texts.length}), skipping remaining texts`);
            for (let j = i + 1; j < texts.length; j++) {
              results.push(new Array(1024).fill(0));
            }
            break;
          }
        }
        
        // Add a zero vector as fallback to maintain array structure
        results.push(new Array(1024).fill(0));
      }
    }
    
    const successRate = ((texts.length - failedTexts.size) / texts.length * 100).toFixed(1);
    console.log(`📊 Completed processing ${texts.length} texts. Success: ${texts.length - failedTexts.size}, Failed: ${failedTexts.size} (${successRate}% success rate)`);
    return results;
  }

  private validateAndTruncateText(text: string): string {
    // Handle edge cases for text input
    if (!text || typeof text !== 'string') {
      return '[empty content]';
    }
    
    // Basic text cleaning only - no truncation
    let processedText = text
      // Remove null bytes and control characters
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      // Remove excessive whitespace
      .replace(/\s+/g, ' ')
      // Remove excessive newlines
      .replace(/\n{3,}/g, '\n\n')
      // Remove excessive spaces at the beginning and end
      .trim();
    
    // If the text is too short after processing, add some padding
    if (processedText.length < 10) {
      processedText = processedText + ' [empty content]';
    }
    
    return processedText;
  }

  private async getEmbeddingWithRetry(text: string, index: number, total: number, maxRetries: number = 3): Promise<any> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await QwenEmbeddingFunction.context.getEmbeddingFor(text);
        
        // Validate the result
        if (!result || !result.vector || !Array.isArray(result.vector)) {
          throw new Error('Invalid embedding result structure');
        }
        
        return result;
      } catch (error: any) {
        console.warn(`Embedding attempt ${attempt}/${maxRetries} failed for text ${index}/${total}:`, error.message);
        
        // Check for any embedding-related errors and reset context immediately
        if (error.message && (
          error.message.includes('invalid embeddings id') || 
          error.message.includes('Failed to get embeddings') ||
          error.message.includes('batch.logits') ||
          error.message.includes('out of range')
        )) {
          console.error('Embedding context error detected, resetting context immediately...');
          await this.resetEmbeddingContext();
          
          // If this was the first attempt, try again immediately
          if (attempt === 1) {
            try {
              const retryResult = await QwenEmbeddingFunction.context.getEmbeddingFor(text);
              if (!retryResult || !retryResult.vector || !Array.isArray(retryResult.vector)) {
                throw new Error('Invalid embedding result structure after retry');
              }
              return retryResult;
            } catch (retryError: any) {
              console.error('Embedding still failed after context reset:', retryError.message);
            }
          }
        }
        
        if (attempt === maxRetries) {
          // Log the problematic text for debugging (first 100 chars)
          const textPreview = text.length > 100 ? text.substring(0, 100) + '...' : text;
          const tokenCount = QwenEmbeddingFunction.model.tokenize(text).length;
          console.error(`Text that failed embedding (${tokenCount} tokens): "${textPreview}"`);
          throw error;
        }
        
        // Wait before retrying (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    // This should never be reached, but just in case
    throw new Error('All embedding attempts failed');
  }

  private async resetEmbeddingContext(): Promise<void> {
    const startTime = process.hrtime();
    try {
      console.log('Resetting embedding context...');
      if (QwenEmbeddingFunction.context) {
        await QwenEmbeddingFunction.context.dispose();
        QwenEmbeddingFunction.context = null;
      }
      
      // Small delay to ensure proper cleanup
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Recreate the context with the same settings
      QwenEmbeddingFunction.context = await QwenEmbeddingFunction.model.createEmbeddingContext({
        contextSize: EMBEDDING_CONFIG.CONTEXT_SIZE,
        batchSize: EMBEDDING_CONFIG.CONTEXT_SIZE,
      });
      
      // Reset the counter
      this.embeddingCounter = 0;
      
      const endTime = process.hrtime(startTime);
      const timeMs = (endTime[0] * 1000 + endTime[1] / 1e6).toFixed(2);
      console.log(`Embedding context reset successfully in ${timeMs}ms`);
    } catch (error) {
      console.error('Failed to reset embedding context, attempting full model reset:', error);
      await this.resetFullModel();
    }
  }

  private async resetFullModel(): Promise<void> {
    try {
      console.log('Performing full model reset...');
      
      // Dispose everything
      if (QwenEmbeddingFunction.context) {
        await QwenEmbeddingFunction.context.dispose();
        QwenEmbeddingFunction.context = null;
      }
      if (QwenEmbeddingFunction.model) {
        await QwenEmbeddingFunction.model.dispose();
        QwenEmbeddingFunction.model = null;
      }
      
      // Small delay to ensure proper cleanup
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Reload the entire model
      QwenEmbeddingFunction.model = await getModel(this.modelPath);
      QwenEmbeddingFunction.context = await QwenEmbeddingFunction.model.createEmbeddingContext({
        contextSize: EMBEDDING_CONFIG.CONTEXT_SIZE,
        batchSize: EMBEDDING_CONFIG.CONTEXT_SIZE,
      });
      
      // Reset the counter
      this.embeddingCounter = 0;
      
      console.log('Full model reset completed successfully');
    } catch (error) {
      console.error('Failed to reset full model:', error);
      throw new Error('Unable to recover from embedding context corruption');
    }
  }

  private async restartEmbeddingSystem(): Promise<void> {
    try {
      console.log('Performing complete embedding system restart...');
      
      // Dispose everything
      if (QwenEmbeddingFunction.context) {
        await QwenEmbeddingFunction.context.dispose();
        QwenEmbeddingFunction.context = null;
      }
      if (QwenEmbeddingFunction.model) {
        await QwenEmbeddingFunction.model.dispose();
        QwenEmbeddingFunction.model = null;
      }
      
      // Longer delay for complete restart
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Reinitialize the entire system
      await this.init();
      
      // Reset all counters
      this.embeddingCounter = 0;
      this.consecutiveFailures = 0;
      
      console.log('Complete embedding system restart completed successfully');
    } catch (error) {
      console.error('Failed to restart embedding system:', error);
      throw new Error('Unable to restart embedding system');
    }
  }

  async dispose() {
    if (QwenEmbeddingFunction.context) {
      await QwenEmbeddingFunction.context.dispose();
      QwenEmbeddingFunction.context = null;
    }
    if (QwenEmbeddingFunction.model) {
      await QwenEmbeddingFunction.model.dispose();
      QwenEmbeddingFunction.model = null;
    }
  }

  private async processEmbeddingsParallel(texts: string[], onBatchComplete?: (batchResults: number[][], batchIndex: number) => Promise<void>): Promise<number[][]> {
    const results: number[][] = [];
    const gpuConfig = getGPUConfig();
    const batchSize = gpuConfig.batchSize;
    
    console.log(`🚀 Processing ${texts.length} texts in parallel batches of ${batchSize} (GPU mode)`);
    
    // Calculate total tokens for logging
    const totalTokens = texts.reduce((sum, text) => {
      return sum + (text.split(/\s+/).length || 0);
    }, 0);
    console.log(`📝 Total estimated tokens: ~${totalTokens.toLocaleString()}`);
    
    // Process texts in parallel batches for GPU efficiency
    for (let i = 0; i < texts.length; i += batchSize) {
      const batchStartTime = process.hrtime();
      const batch = texts.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      const totalBatches = Math.ceil(texts.length / batchSize);
      
      console.log(`\n🧠 Processing parallel embedding batch ${batchNumber}/${totalBatches} (${batch.length} texts)`);
      console.log(`⏱️  Batch start time: ${new Date().toLocaleTimeString()}`);
      
      try {
        // Process batch in parallel using Promise.all
        const batchPromises = batch.map(async (text, index) => {
          const processedText = this.validateAndTruncateText(text);
          return await this.getEmbeddingWithRetry(processedText, index + 1, batch.length);
        });
        
        const batchResults = await Promise.all(batchPromises);
        const embeddings = batchResults.map(result => Array.from(result.vector) as number[]);
        results.push(...embeddings);
        
        const batchEndTime = process.hrtime(batchStartTime);
        const batchTimeMs = (batchEndTime[0] * 1000 + batchEndTime[1] / 1e6).toFixed(2);
        
        console.log(`✅ Completed parallel batch ${batchNumber}/${totalBatches} in ${batchTimeMs}ms`);
        console.log(`📊 Batch performance: ${(batch.length / parseFloat(batchTimeMs) * 1000).toFixed(1)} embeddings/second`);
        console.log(`⏱️  Batch end time: ${new Date().toLocaleTimeString()}`);
        
        // Call the callback if provided
        if (onBatchComplete) {
          console.log(`💾 Saving parallel batch ${batchNumber} to database...`);
          await onBatchComplete(embeddings, batchNumber - 1);
          console.log(`✓ Parallel batch ${batchNumber} saved to database`);
        }
        
        // Reset context periodically for GPU
        if (this.embeddingCounter >= this.maxEmbeddingsBeforeReset) {
          console.log(`🔄 Performing scheduled context reset after ${this.embeddingCounter} embeddings...`);
          await this.resetEmbeddingContext();
          this.embeddingCounter = 0;
        }
        
        this.embeddingCounter += batch.length;
        
      } catch (error) {
        console.error(`❌ Parallel batch ${batchNumber} failed, falling back to sequential processing:`, error);
        // Fallback to sequential processing for this batch
        const sequentialResults = await this.processBatchSequentially(batch);
        results.push(...sequentialResults);
      }
    }
    
    console.log(`\n🎉 Parallel processing completed: ${results.length} embeddings generated`);
    return results;
  }
}

// Recursively get all file paths in a directory
export async function getAllFiles(dir: string, ig: any, rootFolder: string): Promise<string[]> {
  const dirents = await fs.promises.readdir(dir, { withFileTypes: true });
  const files = await Promise.all(dirents.map(async (dirent) => {
    const res = path.resolve(dir, dirent.name);
    const relativePath = path.relative(rootFolder, res);

    if (ig.ignores(relativePath)) {
      return [];
    }

    if (dirent.isDirectory()) {
      return getAllFiles(res, ig, rootFolder);
    } else {
      const ext = path.extname(dirent.name);
      if (ALLOWED_EXTENSIONS.has(ext) || ALLOWED_FILENAMES.has(dirent.name)) {
        return [res];
      }
      return [];
    }
  }));
  return Array.prototype.concat(...files);
}

// Split text into chunks of maxTokens using the model's tokenizer
export async function splitTextByTokens(text: string, maxTokens: number, model: any): Promise<string[]> {
  // Handle edge cases for text input
  if (!text || typeof text !== 'string') {
    console.log('Empty or invalid text provided, returning single empty chunk');
    return [""];
  }
  
  // Handle completely empty string
  if (text.trim().length === 0) {
    console.log('Empty text provided, returning single empty chunk');
    return [""];
  }
  
  const tokens = model.tokenize(text);
  console.log(`Tokenized text into ${tokens.length} tokens (max ${maxTokens} per chunk)`);
  
  const chunks: string[] = [];
  for (let i = 0; i < tokens.length; i += maxTokens) {
    const tokenChunk = tokens.slice(i, i + maxTokens);
    const chunkText = model.detokenize(tokenChunk);
    chunks.push(chunkText);
  }
  
  if (chunks.length === 0) {
    chunks.push(""); // Ensure at least one chunk is returned
  }
  
  console.log(`Split text into ${chunks.length} chunks`);
  return chunks;
}

// Helper to detect language from file extension
export function detectLanguage(fileName: string): string {
  const ext = path.extname(fileName).toLowerCase();
  switch (ext) {
    case '.js': return 'JavaScript';
    case '.ts': return 'TypeScript';
    case '.py': return 'Python';
    case '.java': return 'Java';
    case '.json': return 'JSON';
    case '.md': return 'Markdown';
    case '.txt': return 'Text';
    case '.html': return 'HTML';
    case '.css': return 'CSS';
    case '.c': return 'C';
    case '.cpp': return 'C++';
    case '.go': return 'Go';
    case '.rs': return 'Rust';
    case '.sh': return 'Shell';
    case '.xml': return 'XML';
    default: return ext.replace('.', '').toUpperCase() || 'Unknown';
  }
}

export function getRetrievalLimit(defaultValue = 5): number {
  try {
    // Only works in VS Code extension host
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const vscode = require('vscode');
    return vscode.workspace.getConfiguration('xyne').get<number>('indexing.retrievalLimit') || defaultValue;
  } catch {
    // Fallback for Node.js scripts
    return defaultValue;
  }
}

export const DB_PATH = 'test-embeddings.lancedb';
export const TABLE_NAME = 'new-xyne-table';
export const BM25_TABLE_NAME = 'bm25_index'; 
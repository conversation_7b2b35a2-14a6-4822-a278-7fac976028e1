import { BM25QueryService, BM25QueryResult } from './query-bm25';
import { LanceDBQueryService, QueryResult } from './query-lancedb';
import { 
  getRetrievalLimit, 
  getAllFiles, 
  DB_PATH, 
  TABLE_NAME, 
  BM25_TABLE_NAME,
  sanitizePathToDirectoryName 
} from './lancedb-utils';
import * as fs from 'fs';
import * as path from 'path';
import ignore from 'ignore';
import { connect } from '@lancedb/lancedb';

export interface HybridSearchResult {
  text: string;
  filePath: string;
  language: string;
  chunkIndex: number;
  totalChunks: number;
  _distance?: number;
  _score?: number;
  searchType: 'bm25' | 'qwen' | 'hybrid';
}

export interface IndexStatus {
  bm25Available: boolean;
  qwenAvailable: boolean;
  bm25Stats?: { totalChunks: number; totalFiles: number };
  qwenStats?: { totalChunks: number; totalFiles: number };
}

export class HybridSearchService {
  private static instance: HybridSearchService;
  private bm25Service: BM25QueryService;
  private qwenService: LanceDBQueryService;
  
  // Completion flags for efficiency
  private bm25CompletionCache: Map<string, { isComplete: boolean; lastCheck: number }> = new Map();
  private qwenCompletionCache: Map<string, { isComplete: boolean; lastCheck: number }> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

  private constructor() {
    this.bm25Service = BM25QueryService.getInstance();
    this.qwenService = LanceDBQueryService.getInstance();
  }

  public static getInstance(): HybridSearchService {
    if (!HybridSearchService.instance) {
      HybridSearchService.instance = new HybridSearchService();
    }
    return HybridSearchService.instance;
  }

  private async getDatabaseConnection(folderPath: string, extensionPath: string): Promise<any> {
    const repoId = sanitizePathToDirectoryName(folderPath);

    // Try to find the correct embeddings directory
    const fs = require('fs');
    const path = require('path');
    
    // Check multiple possible locations for embeddings
    const possiblePaths = [
      // Development path (if running from source)
      path.join(process.cwd(), '.embeddings', repoId),
      // Extension path (production)
      path.join(extensionPath, '.embeddings', repoId),
      // Fallback: workspace root
      path.join(folderPath, '.embeddings', repoId)
    ];
    
    let embeddingsDir = null;
    for (const possiblePath of possiblePaths) {
      if (fs.existsSync(possiblePath)) {
        embeddingsDir = possiblePath;
        break;
      }
    }
    
    if (!embeddingsDir) {
      // If no existing directory found, use the extension path as default
      embeddingsDir = path.join(extensionPath, '.embeddings', repoId);
    }

    const dbPath = path.join(embeddingsDir, DB_PATH);
    
    console.log(`   🔍 Database connection:`);
    console.log(`     - Possible paths checked: ${possiblePaths.join(', ')}`);
    console.log(`     - Using path: ${embeddingsDir}`);
    console.log(`     - Database: ${dbPath}`);
    console.log(`     - Exists: ${fs.existsSync(dbPath)}`);

    try {
      if (!fs.existsSync(dbPath)) {
        throw new Error(`Database not found at ${dbPath}. Please run indexing first.`);
      }

      const db = await connect(dbPath);
      return db;
    } catch (error) {
      if (error instanceof Error && error.message.includes('Database not found')) {
        throw error;
      }
      throw new Error(`Failed to connect to database at ${dbPath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async checkBM25Completion(folderPath: string, extensionPath: string): Promise<boolean> {
    const cacheKey = `${folderPath}:${extensionPath}`;
    const now = Date.now();
    const cached = this.bm25CompletionCache.get(cacheKey);
    
    // Return cached result if still valid
    if (cached && (now - cached.lastCheck) < this.CACHE_DURATION) {
      return cached.isComplete;
    }

    try {
      const db = await this.getDatabaseConnection(folderPath, extensionPath);
      const table = await db.openTable(BM25_TABLE_NAME);
      
      // Get all files that should be indexed (same logic as ingestion)
      const ig = ignore();
      ig.add('.git');
      const gitignorePath = path.join(folderPath, '.gitignore');
      if (fs.existsSync(gitignorePath)) {
        const gitignoreContent = fs.readFileSync(gitignorePath, 'utf-8');
        ig.add(gitignoreContent);
      }
      
      const allFiles = await getAllFiles(folderPath, ig, folderPath);
      const existingRecords = await table.query().select(["filePath"]).toArray();
      
      // Check which files are already indexed
      const indexedFiles = new Set<string>();
      existingRecords.forEach((record: any) => {
        indexedFiles.add(record.filePath);
      });
      
      const missingFiles = allFiles.filter(filePath => !indexedFiles.has(filePath));
      const completionPercentage = allFiles.length > 0 ? (indexedFiles.size / allFiles.length) * 100 : 100;
      const isComplete = completionPercentage >= 95; // Allow 95% completion instead of 100%
      
      // Cache the result
      this.bm25CompletionCache.set(cacheKey, { isComplete, lastCheck: now });
      
      if (!isComplete) {
        console.log(`   ⏳ BM25 indexing incomplete: ${missingFiles.length} files missing out of ${allFiles.length} total files (${completionPercentage.toFixed(1)}% complete)`);
      } else {
        console.log(`   ✅ BM25 indexing complete: ${completionPercentage.toFixed(1)}% of files indexed (threshold: 95%)`);
      }
      
      return isComplete;
    } catch (error) {
      console.log(`   ❌ BM25 completion check failed: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  private async checkQwenCompletion(folderPath: string, extensionPath: string): Promise<boolean> {
    const cacheKey = `${folderPath}:${extensionPath}`;
    const now = Date.now();
    const cached = this.qwenCompletionCache.get(cacheKey);
    
    // Return cached result if still valid
    if (cached && (now - cached.lastCheck) < this.CACHE_DURATION) {
      return cached.isComplete;
    }

    try {
      const db = await this.getDatabaseConnection(folderPath, extensionPath);
      const table = await db.openTable(TABLE_NAME);
      
      // Get all files that should be indexed (same logic as ingestion)
      const ig = ignore();
      ig.add('.git');
      const gitignorePath = path.join(folderPath, '.gitignore');
      if (fs.existsSync(gitignorePath)) {
        const gitignoreContent = fs.readFileSync(gitignorePath, 'utf-8');
        ig.add(gitignoreContent);
      }
      
      const allFiles = await getAllFiles(folderPath, ig, folderPath);
      const existingRecords = await table.query().select(["filePath", "chunkIndex", "totalChunks"]).toArray();
      
      console.log(`   🔍 Qwen completion check: ${allFiles.length} files should be indexed, ${existingRecords.length} records in table`);
      
      // Group records by filePath to check for complete embeddings
      const fileCompleteness = new Map<string, { chunks: Set<number>, totalChunks: number }>();
      
      existingRecords.forEach((record: any) => {
        const { filePath, chunkIndex, totalChunks } = record;
        if (!fileCompleteness.has(filePath)) {
          fileCompleteness.set(filePath, { chunks: new Set(), totalChunks });
        }
        fileCompleteness.get(filePath)!.chunks.add(chunkIndex);
      });
      
      // Check which files are completely embedded
      const completelyEmbeddedFiles = new Set<string>();
      fileCompleteness.forEach((info, filePath) => {
        const hasAllChunks = info.chunks.size === info.totalChunks;
        if (hasAllChunks) {
          completelyEmbeddedFiles.add(filePath);
        }
      });
      
      const missingFiles = allFiles.filter(filePath => !completelyEmbeddedFiles.has(filePath));
      const completionPercentage = allFiles.length > 0 ? (completelyEmbeddedFiles.size / allFiles.length) * 100 : 100;
      const isComplete = completionPercentage >= 95; // Allow 95% completion instead of 100%
      
      console.log(`   🔍 Qwen completion check: ${completelyEmbeddedFiles.size} files complete, ${missingFiles.length} files missing (${completionPercentage.toFixed(1)}% complete)`);
      if (missingFiles.length > 0) {
        console.log(`   🔍 Sample missing files: ${missingFiles.slice(0, 3).map(f => path.basename(f)).join(', ')}`);
      }
      
      // Cache the result
      this.qwenCompletionCache.set(cacheKey, { isComplete, lastCheck: now });
      
      if (!isComplete) {
        console.log(`   ⏳ Qwen indexing incomplete: ${missingFiles.length} files missing out of ${allFiles.length} total files (${completionPercentage.toFixed(1)}% complete)`);
      } else {
        console.log(`   ✅ Qwen indexing complete: ${completionPercentage.toFixed(1)}% of files embedded (threshold: 95%)`);
      }
      
      return isComplete;
    } catch (error) {
      console.log(`   ❌ Qwen completion check failed: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  public async getIndexStatus(folderPath: string, extensionPath: string): Promise<IndexStatus> {
    try {
      const db = await this.getDatabaseConnection(folderPath, extensionPath);
      
      // Check BM25 availability and completion
      let bm25Available = false;
      let bm25Stats = undefined;
      try {
        const bm25Table = await db.openTable(BM25_TABLE_NAME);
        const bm25Records = await bm25Table.query().select(['filePath']).toArray();
        
        // Check if BM25 indexing is complete
        const bm25Complete = await this.checkBM25Completion(folderPath, extensionPath);
        if (bm25Complete && bm25Records.length > 0) {
          bm25Available = true;
          const uniqueFiles = new Set(bm25Records.map((r: any) => r.filePath));
          bm25Stats = {
            totalChunks: bm25Records.length, // Now represents total files since no chunking
            totalFiles: uniqueFiles.size
          };
          console.log(`   ✅ BM25 index complete: ${bm25Records.length} records`);
        } else if (bm25Records.length > 0) {
          console.log(`   ⏳ BM25 table exists but indexing is incomplete`);
        } else {
          console.log(`   ❌ BM25 table exists but is empty`);
        }
      } catch (error) {
        console.log(`   ❌ BM25 table check failed: ${error instanceof Error ? error.message : String(error)}`);
        // BM25 table doesn't exist
      }

      // Check Qwen availability and completion
      let qwenAvailable = false;
      let qwenStats = undefined;
      try {
        const qwenTable = await db.openTable(TABLE_NAME);
        const qwenRecords = await qwenTable.query().select(['filePath', 'chunkIndex']).toArray();
        
        console.log(`   🔍 Qwen table check: ${qwenRecords.length} records found`);
        
        // Check if Qwen indexing is complete
        const qwenComplete = await this.checkQwenCompletion(folderPath, extensionPath);
        console.log(`   🔍 Qwen completion check result: ${qwenComplete}`);
        
        if (qwenComplete && qwenRecords.length > 0) {
          qwenAvailable = true;
          const uniqueFiles = new Set(qwenRecords.map((r: any) => r.filePath));
          qwenStats = {
            totalChunks: qwenRecords.length,
            totalFiles: uniqueFiles.size
          };
          console.log(`   ✅ Qwen index complete: ${qwenRecords.length} records`);
        } else if (qwenRecords.length > 0) {
          console.log(`   ⏳ Qwen table exists but indexing is incomplete (completion check returned: ${qwenComplete})`);
        } else {
          console.log(`   ❌ Qwen table exists but is empty`);
        }
      } catch (error) {
        console.log(`   ❌ Qwen table check failed: ${error instanceof Error ? error.message : String(error)}`);
        // Qwen table doesn't exist
      }

      return {
        bm25Available,
        qwenAvailable,
        bm25Stats,
        qwenStats
      };
    } catch (error) {
      console.log(`   ❌ Database connection failed: ${error instanceof Error ? error.message : String(error)}`);
      return {
        bm25Available: false,
        qwenAvailable: false
      };
    }
  }

  public async query(
    query: string, 
    folderPath: string, 
    extensionPath: string, 
    options: { 
      limit?: number;
      preferBM25?: boolean;
      forceBM25?: boolean;
    } = {}
  ): Promise<HybridSearchResult[]> {
    const searchStartTime = process.hrtime();
    
    try {
      if (!query || typeof query !== 'string' || query.trim().length === 0) {
        throw new Error('Query must be a non-empty string');
      }

      const limit = options.limit || getRetrievalLimit(5);
      const preferBM25 = options.preferBM25 ?? true; // Default to preferring BM25 for speed
      const forceBM25 = options.forceBM25 ?? false;

      console.log(`\n🔍 HYBRID SEARCH STARTED`);
      console.log(`   Query: "${query}"`);
      console.log(`   Limit: ${limit}`);
      console.log(`   Options: preferBM25=${preferBM25}, forceBM25=${forceBM25}`);
      console.log(`   Folder: ${folderPath}`);

      // Get index status
      console.log(`\n📊 Checking index availability...`);
      const indexStatus = await this.getIndexStatus(folderPath, extensionPath);
      console.log(`   BM25 Index: ${indexStatus.bm25Available ? '✅ Available' : '❌ Not available'}`);
      if (indexStatus.bm25Stats) {
        console.log(`     📄 ${indexStatus.bm25Stats.totalChunks} chunks, ${indexStatus.bm25Stats.totalFiles} files`);
      } else if (indexStatus.bm25Available) {
        console.log(`     📄 Stats not available (table exists but empty or error reading)`);
      }
      console.log(`   Qwen Index: ${indexStatus.qwenAvailable ? '✅ Available' : '❌ Not available'}`);
      if (indexStatus.qwenStats) {
        console.log(`     📄 ${indexStatus.qwenStats.totalChunks} chunks, ${indexStatus.qwenStats.totalFiles} files`);
      } else if (indexStatus.qwenAvailable) {
        console.log(`     📄 Stats not available (table exists but empty or error reading)`);
      }
      
      console.log(`\n🔍 Search Decision Logic:`);
      console.log(`   - Qwen available: ${indexStatus.qwenAvailable} (Priority 1)`);
      console.log(`   - BM25 available: ${indexStatus.bm25Available} (Priority 2)`);
      console.log(`   - Force BM25: ${forceBM25}`);

      // If forceBM25 is true, only use BM25
      if (forceBM25) {
        console.log(`\n🚀 FORCING BM25-ONLY SEARCH`);
        if (!indexStatus.bm25Available) {
          throw new Error('BM25 index not available. Please run indexing first.');
        }
        const bm25StartTime = process.hrtime();
        const bm25Results = await this.bm25Service.query(query, folderPath, extensionPath, { limit });
        const bm25EndTime = process.hrtime(bm25StartTime);
        const bm25Duration = (bm25EndTime[0] * 1000 + bm25EndTime[1] / 1000000).toFixed(2);
        console.log(`   ✅ BM25 search completed in ${bm25Duration}ms`);
        console.log(`   📄 Found ${bm25Results.length} results`);
        console.log(`   🔍 SEARCH TYPE: BM25 (forced)`);
        return bm25Results.map(result => ({
          ...result,
          chunkIndex: 0, // BM25 no longer uses chunking - one entry per file
          totalChunks: 1, // BM25 no longer uses chunking - one entry per file
          searchType: 'bm25' as const
        }));
      }

      // Priority 1: If Qwen is available, use Qwen
      if (indexStatus.qwenAvailable) {
        console.log(`\n🧠 USING QWEN SEARCH (Qwen index available)`);
        const qwenStartTime = process.hrtime();
        const qwenResults = await this.qwenService.query(query, folderPath, extensionPath, { limit });
        const qwenEndTime = process.hrtime(qwenStartTime);
        const qwenDuration = (qwenEndTime[0] * 1000 + qwenEndTime[1] / 1000000).toFixed(2);
        console.log(`   ✅ Qwen search completed in ${qwenDuration}ms`);
        console.log(`   📄 Found ${qwenResults.length} results`);
        console.log(`   🔍 SEARCH TYPE: Qwen (priority)`);
        return qwenResults.map(result => ({
          ...result,
          searchType: 'qwen' as const
        }));
      }

      // Priority 2: If Qwen not available but BM25 is available, use BM25
      if (indexStatus.bm25Available) {
        console.log(`\n🚀 USING BM25 SEARCH (Qwen not available, BM25 fallback)`);
        try {
          const bm25StartTime = process.hrtime();
          const bm25Results = await this.bm25Service.query(query, folderPath, extensionPath, { limit });
          const bm25EndTime = process.hrtime(bm25StartTime);
          const bm25Duration = (bm25EndTime[0] * 1000 + bm25EndTime[1] / 1000000).toFixed(2);
          console.log(`   ✅ BM25 search completed in ${bm25Duration}ms`);
          console.log(`   📄 Found ${bm25Results.length} results`);
          console.log(`   🔍 SEARCH TYPE: BM25 (fallback)`);
          return bm25Results.map(result => ({
            ...result,
            chunkIndex: 0, // BM25 no longer uses chunking - one entry per file
            totalChunks: 1, // BM25 no longer uses chunking - one entry per file
            searchType: 'bm25' as const
          }));
        } catch (bm25Error) {
          console.error(`   ❌ BM25 search failed: ${bm25Error instanceof Error ? bm25Error.message : String(bm25Error)}`);
          throw bm25Error;
        }
      }

      // If neither is available
      console.log(`\n❌ NO INDEXES AVAILABLE`);
      console.log(`   Neither BM25 nor Qwen indexes are available`);
      console.log(`   💡 Run "Xyne: Index Codebase" to create indexes`);
      console.log(`   🔍 Debug info: BM25=${indexStatus.bm25Available}, Qwen=${indexStatus.qwenAvailable}`);
      throw new Error('No search index available. Please run "Xyne: Index Codebase" first to create the search index.');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`\n❌ HYBRID SEARCH FAILED: ${errorMessage}`);
      console.error(`\n🔍 Debug information:`);
      console.error(`   - Error type: ${error instanceof Error ? error.constructor.name : typeof error}`);
      console.error(`   - Error stack: ${error instanceof Error ? error.stack : 'No stack trace'}`);
      throw new Error(`Search failed: ${errorMessage}`);
    } finally {
      const searchEndTime = process.hrtime(searchStartTime);
      const totalDuration = (searchEndTime[0] * 1000 + searchEndTime[1] / 1000000).toFixed(2);
      console.log(`\n⏱️  TOTAL SEARCH TIME: ${totalDuration}ms`);
      console.log(`🔍 HYBRID SEARCH COMPLETED\n`);
    }
  }

  private combineAndDeduplicateResults(
    primaryResults: (QueryResult | BM25QueryResult)[], 
    secondaryResults: (QueryResult | BM25QueryResult)[], 
    limit: number
  ): HybridSearchResult[] {
    const seen = new Set<string>();
    const combined: HybridSearchResult[] = [];

    // Add primary results first
    for (const result of primaryResults) {
      // Handle both BM25 (no chunking) and Qwen (with chunking) results
      const chunkIndex = 'chunkIndex' in result ? result.chunkIndex : 0;
      const totalChunks = 'totalChunks' in result ? result.totalChunks : 1;
      const key = `${result.filePath}:${chunkIndex}`;
      if (!seen.has(key)) {
        seen.add(key);
        combined.push({
          ...result,
          chunkIndex,
          totalChunks,
          searchType: 'hybrid' as const
        });
      }
    }

    // Add secondary results if we haven't reached the limit
    for (const result of secondaryResults) {
      if (combined.length >= limit) break;
      
      // Handle both BM25 (no chunking) and Qwen (with chunking) results
      const chunkIndex = 'chunkIndex' in result ? result.chunkIndex : 0;
      const totalChunks = 'totalChunks' in result ? result.totalChunks : 1;
      const key = `${result.filePath}:${chunkIndex}`;
      if (!seen.has(key)) {
        seen.add(key);
        combined.push({
          ...result,
          chunkIndex,
          totalChunks,
          searchType: 'hybrid' as const
        });
      }
    }

    return combined.slice(0, limit);
  }

  public async dispose(): Promise<void> {
    try {
      await this.qwenService.dispose();
    } catch (error) {
      console.error(`Error disposing HybridSearchService: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

// Backward compatibility functions
export async function queryHybrid(query: string, folderPath: string, extensionPath: string): Promise<HybridSearchResult[]> {
  const searchService = HybridSearchService.getInstance();
  return searchService.query(query, folderPath, extensionPath);
}

export async function getIndexStatus(folderPath: string, extensionPath: string): Promise<IndexStatus> {
  const searchService = HybridSearchService.getInstance();
  return searchService.getIndexStatus(folderPath, extensionPath);
} 
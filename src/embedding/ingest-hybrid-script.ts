import { ingestCodebaseHybrid } from './ingest-hybrid';

async function main() {
  const folderPath = process.argv[2];
  const modelPath = process.argv[3];
  const extensionPath = process.argv[4];
  
  if (!folderPath) {
    console.error('Error: Folder path not provided.');
    process.exit(1);
  }
  if (!modelPath) {
    console.error('Error: Model path not provided.');
    process.exit(1);
  }
  if (!extensionPath) {
    console.error('Error: Extension path not provided.');
    process.exit(1);
  }

  try {
    console.log(`Starting hybrid ingestion for folder: ${folderPath}`);
    await ingestCodebaseHybrid(folderPath, modelPath, extensionPath);
    console.log('Hybrid ingestion complete.');
    process.exit(0);
  } catch (error) {
    console.error('Hybrid ingestion failed:', error);
    process.exit(1);
  }
}

main(); 
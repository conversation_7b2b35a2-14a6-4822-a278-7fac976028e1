import * as path from 'path';
import { ingestCodebase } from './ingest-lancedb';
import { ingestCodebaseBM25 } from './ingest-bm25';

export async function ingestCodebaseHybrid(
  folderToEmbed: string, 
  modelPath: string, 
  extensionPath: string, 
  options?: { 
    fastMode?: boolean;
    skipBM25?: boolean;
    skipQwen?: boolean;
  }
) {
  const totalStartTime = process.hrtime();
  const fastMode = options?.fastMode ?? false;
  const skipBM25 = options?.skipBM25 ?? false;
  const skipQwen = options?.skipQwen ?? false;
  folderToEmbed = path.resolve(folderToEmbed);
  
  console.log(`\n🚀 HYBRID INDEXING STARTED`);
  console.log(`   📁 Folder: ${folderToEmbed}`);
  console.log(`   ⚙️  Options: fastMode=${fastMode}, skipBM25=${skipBM25}, skipQwen=${skipQwen}`);
  console.log(`   🕐 Start time: ${new Date().toLocaleTimeString()}`);

  // Step 1: Create BM25 index (fast)
  if (!skipBM25) {
    console.log(`\n🔄 STEP 1: Creating BM25 index...`);
    console.log(`   🕐 BM25 start time: ${new Date().toLocaleTimeString()}`);
    const bm25StartTime = process.hrtime();
    
    try {
      await ingestCodebaseBM25(folderToEmbed, extensionPath, { fastMode });
      const bm25EndTime = process.hrtime(bm25StartTime);
      const bm25Duration = (bm25EndTime[0] * 1000 + bm25EndTime[1] / 1000000).toFixed(2);
      console.log(`   ✅ BM25 indexing completed in ${bm25Duration}ms`);
      console.log(`   🕐 BM25 end time: ${new Date().toLocaleTimeString()}`);
    } catch (error) {
      console.error(`   ❌ BM25 indexing failed:`, error);
      if (!skipQwen) {
        console.log(`   ⚠️  Continuing with Qwen indexing...`);
      }
    }
  } else {
    console.log(`\n⏭️  STEP 1: Skipping BM25 indexing (skipBM25=true)`);
  }

  // Step 2: Create Qwen embeddings (slower but more accurate)
  if (!skipQwen) {
    console.log(`\n🔄 STEP 2: Creating Qwen embeddings...`);
    console.log(`   🕐 Qwen start time: ${new Date().toLocaleTimeString()}`);
    console.log(`   🧠 Model path: ${modelPath}`);
    const qwenStartTime = process.hrtime();
    
    try {
      await ingestCodebase(folderToEmbed, modelPath, extensionPath, { fastMode });
      const qwenEndTime = process.hrtime(qwenStartTime);
      const qwenDuration = (qwenEndTime[0] * 1000 + qwenEndTime[1] / 1000000).toFixed(2);
      console.log(`   ✅ Qwen indexing completed in ${qwenDuration}ms`);
      console.log(`   🕐 Qwen end time: ${new Date().toLocaleTimeString()}`);
    } catch (error) {
      console.error(`   ❌ Qwen indexing failed:`, error);
    }
  } else {
    console.log(`\n⏭️  STEP 2: Skipping Qwen indexing (skipQwen=true)`);
  }

  const totalEndTime = process.hrtime(totalStartTime);
  const totalDuration = (totalEndTime[0] * 1000 + totalEndTime[1] / 1000000).toFixed(2);
  
  console.log(`\n🎉 HYBRID INDEXING COMPLETED!`);
  console.log(`   ⏱️  Total time: ${totalDuration}ms`);
  console.log(`   🕐 End time: ${new Date().toLocaleTimeString()}`);
  
  console.log(`\n✅ Hybrid indexing completed!`);
  console.log(`   🚀 BM25 provides fast text-based search`);
  console.log(`   🧠 Qwen provides semantic similarity search`);
  console.log(`   🔄 Hybrid search will combine both for optimal results`);
  
  console.log(`\n🔍 HYBRID INDEXING COMPLETED\n`);
}

// Command line interface for the ingest script
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length < 3) {
    console.error('Usage: node ingest-hybrid.js <folderPath> <modelPath> <extensionPath> [options]');
    console.error('Options:');
    console.error('  --fast-mode: Enable fast mode');
    console.error('  --skip-bm25: Skip BM25 indexing');
    console.error('  --skip-qwen: Skip Qwen indexing');
    process.exit(1);
  }

  const [folderPath, modelPath, extensionPath, ...options] = args;
  
  const parsedOptions: any = {};
  options.forEach(option => {
    if (option === '--fast-mode') parsedOptions.fastMode = true;
    if (option === '--skip-bm25') parsedOptions.skipBM25 = true;
    if (option === '--skip-qwen') parsedOptions.skipQwen = true;
  });

  ingestCodebaseHybrid(folderPath, modelPath, extensionPath, parsedOptions)
    .then(() => {
      console.log('Hybrid indexing completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Hybrid indexing failed:', error);
      process.exit(1);
    });
} 
import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';

// NOTE: The previous Qwen model required authentication.
// This has been switched to a popular, open-access embedding model that can be downloaded without a token.
const MODEL_URL = 'https://huggingface.co/Qwen/Qwen3-Embedding-0.6B-GGUF/resolve/main/Qwen3-Embedding-0.6B-Q8_0.gguf?download=true';
export const MODEL_FILENAME = 'Qwen3-Embedding-0.6B-Q8_0.gguf';

export async function ensureModelDownloaded(
  extensionPath: string,
  outputChannel?: vscode.OutputChannel
): Promise<string> {
  const modelDir = path.join(extensionPath, '.models');
  const modelPath = path.join(modelDir, MODEL_FILENAME);

  if (fs.existsSync(modelPath)) {
    outputChannel?.appendLine('Model already exists. Skipping download.');
    return modelPath;
  }

  await vscode.window.withProgress({
    location: vscode.ProgressLocation.Notification,
    title: "Downloading Embedding Model",
    cancellable: false
  }, async (progress) => {
    progress.report({ message: "Starting download..." });
    outputChannel?.appendLine(`Model not found. Downloading to ${modelPath}...`);

    if (!fs.existsSync(modelDir)) {
      fs.mkdirSync(modelDir, { recursive: true });
    }

    const writer = fs.createWriteStream(modelPath);

    try {
      const response = await axios({
        url: MODEL_URL,
        method: 'GET',
        responseType: 'stream',
      });
      console.log(`Downloading model from ${response}...`);
      const totalLength = response.headers['content-length'];
      let downloadedLength = 0;
      let lastReportedPercent = 0;

      if (totalLength) {
        outputChannel?.appendLine(`Model size: ${(parseInt(totalLength, 10) / 1024 / 1024).toFixed(2)} MB`);
      }

      response.data.on('data', (chunk: Buffer) => {
        downloadedLength += chunk.length;
        if (totalLength) {
          const percent = Math.floor((downloadedLength / parseInt(totalLength, 10)) * 100);
          progress.report({
            message: `${percent}% downloaded`,
            increment: percent - lastReportedPercent
          });
          lastReportedPercent = percent;
        }
      });

      response.data.pipe(writer);

      await new Promise<void>((resolve, reject) => {
        writer.on('finish', () => {
          if (totalLength && downloadedLength < parseInt(totalLength, 10)) {
            const err = new Error(`Model download incomplete. Expected ${totalLength} bytes, but got ${downloadedLength}.`);
            outputChannel?.appendLine(err.message);
            fs.unlink(modelPath, () => reject(err));
          } else {
            outputChannel?.appendLine('Model downloaded successfully.');
            progress.report({ message: "Download complete!", increment: 100 });
            resolve();
          }
        });
        writer.on('error', (err) => {
          outputChannel?.appendLine(`Error writing model file: ${err.message}`);
          fs.unlink(modelPath, () => reject(err));
        });
        response.data.on('error', (err: Error) => {
          outputChannel?.appendLine(`Error during model download stream: ${err.message}`);
          reject(err);
        });
      });
    } catch (error) {
      const err = error as Error;
      outputChannel?.appendLine(`Download failed: ${err.message}`);
      if (fs.existsSync(modelPath)) {
        fs.unlinkSync(modelPath);
      }
      vscode.window.showErrorMessage(`Failed to download model: ${err.message}`);
      throw error;
    }
  });
  return modelPath;
} 
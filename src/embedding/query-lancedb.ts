import * as path from 'path';
import { connect, rerankers } from '@lancedb/lancedb';
import {
  QwenEmbeddingFunction,
  DB_PATH,
  TABLE_NAME,
  sanitizePathToDirectoryName,
  getRetrievalLimit,
} from './lancedb-utils';
import { ensureModelDownloaded } from './model-downloader';

export interface QueryResult {
  text: string;
  filePath: string;
  language: string;
  chunkIndex: number;
  totalChunks: number;
  _distance?: number;
  _score?: number;
}

export class LanceDBQueryService {
  private static instance: LanceDBQueryService;
  private embeddingFunction: QwenEmbeddingFunction | null = null;
  private reranker: any = null;

  private constructor() {}

  public static getInstance(): LanceDBQueryService {
    if (!LanceDBQueryService.instance) {
      LanceDBQueryService.instance = new LanceDBQueryService();
    }
    return LanceDBQueryService.instance;
  }

  private async initializeEmbeddingFunction(extensionPath: string): Promise<void> {
    if (this.embeddingFunction) {
      return;
    }

    try {
      const modelPath = await ensureModelDownloaded(extensionPath);
      this.embeddingFunction = new QwenEmbeddingFunction({ modelPath });
      await this.embeddingFunction.init();
    } catch (error) {
      throw new Error(`Failed to initialize embedding function: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async initializeReranker(): Promise<void> {
    if (this.reranker) {
      return;
    }

    try {
      this.reranker = await rerankers.RRFReranker.create();
    } catch (error) {
      console.warn(`Failed to initialize reranker, continuing without reranking: ${error instanceof Error ? error.message : String(error)}`);
      this.reranker = null;
    }
  }

  private async getDatabaseConnection(folderPath: string, extensionPath: string): Promise<any> {
    const repoId = sanitizePathToDirectoryName(folderPath);
    const embeddingsDir = path.join(extensionPath, '.embeddings', repoId);
    const dbPath = path.join(embeddingsDir, DB_PATH);

    try {
      const fs = require('fs');
      if (!fs.existsSync(dbPath)) {
        throw new Error(`Database not found at ${dbPath}. Please run indexing first.`);
      }

      const db = await connect(dbPath);
      const table = await db.openTable(TABLE_NAME);
      
      return table;
    } catch (error) {
      if (error instanceof Error && error.message.includes('Database not found')) {
        throw error;
      }
      throw new Error(`Failed to connect to database at ${dbPath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async query(
    query: string, 
    folderPath: string, 
    extensionPath: string, 
    options: { limit?: number } = {}
  ): Promise<QueryResult[]> {
    const qwenStartTime = process.hrtime();
    
    try {
      if (!query || typeof query !== 'string' || query.trim().length === 0) {
        throw new Error('Query must be a non-empty string');
      }

      const table = await this.getDatabaseConnection(folderPath, extensionPath);
      const limit = options.limit || getRetrievalLimit(5);

      console.log(`   🧠 Qwen: Starting semantic search for "${query}" (limit: ${limit})`);

      // Initialize services
      const initStartTime = process.hrtime();
      await this.initializeEmbeddingFunction(extensionPath);
      await this.initializeReranker();
      const initEndTime = process.hrtime(initStartTime);
      const initDuration = (initEndTime[0] * 1000 + initEndTime[1] / 1000000).toFixed(2);
      console.log(`   ⚙️  Qwen: Services initialized in ${initDuration}ms`);

      // Build query
      const queryStartTime = process.hrtime();
      let queryBuilder = table.query().fullTextSearch(query);

      try {
        console.log(`   🔄 Qwen: Computing query embeddings...`);
        const queryEmbedding = await this.embeddingFunction!.computeQueryEmbeddings(query);
        queryBuilder = queryBuilder.nearestTo(queryEmbedding, { nprobes: 10 });
        console.log(`   ✅ Qwen: Query embeddings computed successfully`);
      } catch (error) {
        console.warn(`   ⚠️  Qwen: Failed to compute embeddings, using full-text search only: ${error instanceof Error ? error.message : String(error)}`);
      }

      if (this.reranker) {
        console.log(`   🔄 Qwen: Applying reranker...`);
        queryBuilder = queryBuilder.rerank(this.reranker);
      }

      const results = await queryBuilder
        .limit(limit)
        .select(['text', 'filePath', 'language', 'chunkIndex', 'totalChunks'])
        .toArray();
      const queryEndTime = process.hrtime(queryStartTime);
      const queryDuration = (queryEndTime[0] * 1000 + queryEndTime[1] / 1000000).toFixed(2);

      console.log(`   ✅ Qwen: Found ${results.length} results in ${queryDuration}ms`);

      // Log some result details for debugging
      if (results.length > 0) {
        console.log(`   📄 Qwen: Sample results:`);
        results.slice(0, 2).forEach((result: any, i: number) => {
          const fileName = result.filePath ? result.filePath.split('/').pop() : 'unknown';
          const distance = result._distance ? result._distance.toFixed(4) : 'N/A';
          const score = result._score ? result._score.toFixed(4) : 'N/A';
          console.log(`      ${i + 1}. ${fileName} (${result.language}) - Distance: ${distance}, Score: ${score}`);
        });
      }

      return results.map((result: any) => ({
        text: result.text || '',
        filePath: result.filePath || '',
        language: result.language || 'unknown',
        chunkIndex: result.chunkIndex || 0,
        totalChunks: result.totalChunks || 1,
        _distance: result._distance,
        _score: result._score,
      }));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`   ❌ Qwen query failed: ${errorMessage}`);
      
      if (errorMessage.includes('Database not found')) {
        throw new Error(`Database not found. Please run "Xyne: Index Codebase" first to create the search index.`);
      }
      
      throw new Error(`Query failed: ${errorMessage}`);
    } finally {
      const qwenEndTime = process.hrtime(qwenStartTime);
      const totalDuration = (qwenEndTime[0] * 1000 + qwenEndTime[1] / 1000000).toFixed(2);
      console.log(`   ⏱️  Qwen: Total time including setup: ${totalDuration}ms`);
    }
  }

  public async dispose(): Promise<void> {
    try {
      if (this.embeddingFunction) {
        await this.embeddingFunction.dispose();
        this.embeddingFunction = null;
      }
      this.reranker = null;
    } catch (error) {
      console.error(`Error disposing LanceDBQueryService: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

// Backward compatibility function
export async function queryLanceDb(query: string, folderPath: string, extensionPath: string): Promise<QueryResult[]> {
  const queryService = LanceDBQueryService.getInstance();
  return queryService.query(query, folderPath, extensionPath);
}

import * as vscode from 'vscode';
import { AIProviders, Models, type XyneSettings, type ProviderConfig } from '../ai/types';
import { ModelRegistry } from '../ai/models/ModelRegistry';
import { getLogger, Subsystem } from '../logger';

const Logger = getLogger(Subsystem.Settings);

export class SettingsManager {
  private context: vscode.ExtensionContext;
  private static readonly SETTINGS_KEY = 'xyne.settings';
  private static readonly AWS_ACCESS_KEY_SECRET = 'xyne.aws.accessKey';
  private static readonly AWS_SECRET_KEY_SECRET = 'xyne.aws.secretKey';
  private static readonly OPENAI_API_KEY_SECRET = 'xyne.openai.apiKey';
  private static readonly GOOGLE_API_KEY_SECRET = 'xyne.google.apiKey';
  private static readonly VERTEX_PROJECT_ID_SECRET = 'xyne.vertex.projectId';
  private static readonly OPENROUTER_API_KEY_SECRET = 'xyne.openrouter.apiKey';

  constructor(context: vscode.ExtensionContext) {
    this.context = context;
  }

  async getSettings(): Promise<XyneSettings> {
    Logger.info('🔧 Loading settings from VS Code storage...');

    const settings = this.context.globalState.get<XyneSettings>(SettingsManager.SETTINGS_KEY);

    if (!settings) {
      Logger.info('📋 No existing settings found, using defaults');
      const defaultSettings = this.getDefaultSettings();
      Logger.info(`📋 Default settings: Provider=${defaultSettings.selectedProvider}, Model=${defaultSettings.selectedModel}`);
      return defaultSettings;
    }

    Logger.info(`📋 Existing settings found: Provider=${settings.selectedProvider}, Model=${settings.selectedModel}`);

    // Load sensitive data from SecretStorage
    const awsAccessKey = await this.context.secrets.get(SettingsManager.AWS_ACCESS_KEY_SECRET);
    const awsSecretKey = await this.context.secrets.get(SettingsManager.AWS_SECRET_KEY_SECRET);
    const openaiApiKey = await this.context.secrets.get(SettingsManager.OPENAI_API_KEY_SECRET);
    const googleApiKey = await this.context.secrets.get(SettingsManager.GOOGLE_API_KEY_SECRET);
    const vertexProjectId = await this.context.secrets.get(SettingsManager.VERTEX_PROJECT_ID_SECRET);
    const openRouterApiKey = await this.context.secrets.get(SettingsManager.OPENROUTER_API_KEY_SECRET);

    // Handle duplicated API key issue
    let correctedGoogleApiKey = googleApiKey;
    if (googleApiKey && googleApiKey.length > 50 && googleApiKey.includes('AIza') && googleApiKey.indexOf('AIza', 1) !== -1) {
      // Key appears to be duplicated, use first half
      const halfLength = Math.floor(googleApiKey.length / 2);
      correctedGoogleApiKey = googleApiKey.substring(0, halfLength);

      // Update the stored key with corrected version
      await this.context.secrets.store(SettingsManager.GOOGLE_API_KEY_SECRET, correctedGoogleApiKey);
    }

    // Set the Google API key
    if (correctedGoogleApiKey && settings.providers[AIProviders.GoogleAI]) {
      settings.providers[AIProviders.GoogleAI].apiKey = correctedGoogleApiKey;
    }

    Logger.info(`🔐 Loaded API keys: AWS=${!!awsAccessKey}, OpenAI=${!!openaiApiKey}, Google=${!!correctedGoogleApiKey}, Vertex=${!!vertexProjectId}, OpenRouter=${!!openRouterApiKey}`);

    // Merge with stored settings
    if (settings.providers[AIProviders.AwsBedrock]) {
      settings.providers[AIProviders.AwsBedrock].accessKeyId = awsAccessKey;
      settings.providers[AIProviders.AwsBedrock].secretAccessKey = awsSecretKey;
    }
    if (settings.providers[AIProviders.OpenAI]) {
      settings.providers[AIProviders.OpenAI].apiKey = openaiApiKey;
    }
    if (settings.providers[AIProviders.VertexAI]) {
      settings.providers[AIProviders.VertexAI].vertexProjectId = vertexProjectId;
    }
    if (settings.providers[AIProviders.OpenRouter]) {
      settings.providers[AIProviders.OpenRouter].openRouterApiKey = openRouterApiKey;
    }
    // Google API key is already handled above with duplication check

    Logger.info('✅ Settings loaded and merged with secrets successfully');
    return settings;
  }

  async updateSettings(settings: XyneSettings): Promise<void> {
    try {
      // Store sensitive data in SecretStorage
      const awsProvider = settings.providers[AIProviders.AwsBedrock];
      // OpenAI and Google provider handling moved up

      if (awsProvider) {
        if (awsProvider.accessKeyId && awsProvider.accessKeyId.trim() !== '') {
          await this.context.secrets.store(SettingsManager.AWS_ACCESS_KEY_SECRET, awsProvider.accessKeyId);
        } else {
          await this.context.secrets.delete(SettingsManager.AWS_ACCESS_KEY_SECRET);
        }
        if (awsProvider.secretAccessKey && awsProvider.secretAccessKey.trim() !== '') {
          await this.context.secrets.store(SettingsManager.AWS_SECRET_KEY_SECRET, awsProvider.secretAccessKey);
        } else {
          await this.context.secrets.delete(SettingsManager.AWS_SECRET_KEY_SECRET);
        }
      }
      // OpenAI and Google provider apiKey storage is handled above
      const openaiProvider = settings.providers[AIProviders.OpenAI]; // re-fetch for clarity for settingsToStore
      const googleProvider = settings.providers[AIProviders.GoogleAI]; // re-fetch for clarity for settingsToStore
      if (openaiProvider) {
        if (openaiProvider.apiKey && openaiProvider.apiKey.trim() !== '') {
          await this.context.secrets.store(SettingsManager.OPENAI_API_KEY_SECRET, openaiProvider.apiKey);
        } else {
          await this.context.secrets.delete(SettingsManager.OPENAI_API_KEY_SECRET);
        }
      }
      if (googleProvider) {
        // Save Google API Key to secrets if provided
        if (googleProvider.apiKey) {
          // Handle duplicated API key issue before saving
          let keyToSave = googleProvider.apiKey;
          if (googleProvider.apiKey.length > 50 && googleProvider.apiKey.includes('AIza') && googleProvider.apiKey.indexOf('AIza', 1) !== -1) {
            // Key appears to be duplicated, use first half
            const halfLength = Math.floor(googleProvider.apiKey.length / 2);
            keyToSave = googleProvider.apiKey.substring(0, halfLength);
          }

          await this.context.secrets.store(SettingsManager.GOOGLE_API_KEY_SECRET, keyToSave);
        } else {
          // Remove the key if it's empty or null
          await this.context.secrets.delete(SettingsManager.GOOGLE_API_KEY_SECRET);
        }
      }

      // Handle Vertex AI Project ID
      const vertexProvider = settings.providers[AIProviders.VertexAI];
      if (vertexProvider) {
        if (vertexProvider.vertexProjectId && vertexProvider.vertexProjectId.trim() !== '') {
          await this.context.secrets.store(SettingsManager.VERTEX_PROJECT_ID_SECRET, vertexProvider.vertexProjectId);
        } else {
          await this.context.secrets.delete(SettingsManager.VERTEX_PROJECT_ID_SECRET);
        }
      }

      // Handle OpenRouter API Key
      const openRouterProvider = settings.providers[AIProviders.OpenRouter];
      if (openRouterProvider) {
        if (openRouterProvider.openRouterApiKey && openRouterProvider.openRouterApiKey.trim() !== '') {
          await this.context.secrets.store(SettingsManager.OPENROUTER_API_KEY_SECRET, openRouterProvider.openRouterApiKey);
        } else {
          await this.context.secrets.delete(SettingsManager.OPENROUTER_API_KEY_SECRET);
        }
      }

      // Remove sensitive data before storing in globalState
      const settingsToStore: XyneSettings = {
        ...settings,
        providers: {
          ...settings.providers,
          [AIProviders.AwsBedrock]: awsProvider ? {
            ...awsProvider,
            accessKeyId: undefined,
            secretAccessKey: undefined,
          } : settings.providers[AIProviders.AwsBedrock],
          [AIProviders.OpenAI]: openaiProvider ? {
            ...openaiProvider,
            apiKey: undefined,
          } : settings.providers[AIProviders.OpenAI],
          [AIProviders.GoogleAI]: googleProvider ? {
            ...googleProvider,
            apiKey: undefined,
          } : settings.providers[AIProviders.GoogleAI],
          [AIProviders.VertexAI]: vertexProvider ? {
            ...vertexProvider,
            vertexProjectId: undefined,
          } : settings.providers[AIProviders.VertexAI],
          [AIProviders.OpenRouter]: openRouterProvider ? {
            ...openRouterProvider,
            openRouterApiKey: undefined,
          } : settings.providers[AIProviders.OpenRouter],
        },
      };

      await this.context.globalState.update(SettingsManager.SETTINGS_KEY, settingsToStore);
      Logger.info('Settings updated successfully');
    } catch (error) {
      Logger.error(error, 'Failed to update settings');
      throw error;
    }
  }

  async clearSettings(): Promise<void> {
    try {
      await this.context.globalState.update(SettingsManager.SETTINGS_KEY, undefined);
      await this.context.secrets.delete(SettingsManager.AWS_ACCESS_KEY_SECRET);
      await this.context.secrets.delete(SettingsManager.AWS_SECRET_KEY_SECRET);
      await this.context.secrets.delete(SettingsManager.OPENAI_API_KEY_SECRET);
      await this.context.secrets.delete(SettingsManager.GOOGLE_API_KEY_SECRET);
      await this.context.secrets.delete(SettingsManager.VERTEX_PROJECT_ID_SECRET);
      await this.context.secrets.delete(SettingsManager.OPENROUTER_API_KEY_SECRET);
      Logger.info('Settings cleared successfully');
    } catch (error) {
      Logger.error(error, 'Failed to clear settings');
      throw error;
    }
  }

  private getDefaultSettings(): XyneSettings {
    const defaultProvider = AIProviders.AwsBedrock;
    const defaultModel = ModelRegistry.getDefaultModelForProvider(defaultProvider) || Models.Claude_3_7_Sonnet;
    
    return {
      selectedProvider: defaultProvider,
      selectedModel: defaultModel,
      providers: {
        [AIProviders.AwsBedrock]: {
          provider: AIProviders.AwsBedrock,
          region: 'us-east-1',
        },
        [AIProviders.OpenAI]: {
          provider: AIProviders.OpenAI,
        },
        [AIProviders.AzureOpenAI]: {
          provider: AIProviders.AzureOpenAI,
        },
        [AIProviders.GoogleAI]: {
          provider: AIProviders.GoogleAI,
        },
        [AIProviders.VertexAI]: {
          provider: AIProviders.VertexAI,
          vertexRegion: 'us-east5',
        },
        [AIProviders.OpenRouter]: {
          provider: AIProviders.OpenRouter,
        },
      },
      completion: {
        enabled: true,
        triggerDelay: 300,
        maxCompletionLength: 200,
        enableMultiLineCompletions: true,
        contextLines: 50
      },
      autoCompact: {
        enabled: true,
        threshold: 0.95,
        maxContextTokens: 200000
      }
    };
  }

  async validateAwsCredentials(accessKeyId: string, secretAccessKey: string, region: string): Promise<boolean> {
    try {
      // This would typically make a test call to AWS to validate credentials
      // For now, we'll just check if the keys are provided and have the right format
      const accessKeyPattern = /^AKIA[0-9A-Z]{16}$/;
      const secretKeyPattern = /^[A-Za-z0-9/+=]{40}$/;

      return accessKeyPattern.test(accessKeyId) && secretKeyPattern.test(secretAccessKey) && region.length > 0;
    } catch (error) {
      Logger.error(error, 'Failed to validate AWS credentials');
      return false;
    }
  }
}

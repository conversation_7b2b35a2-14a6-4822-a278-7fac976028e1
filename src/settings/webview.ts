import * as vscode from 'vscode';
import { SettingsManager } from './manager';
import { ChatManager } from '../chat/ChatManager';
import { getLogger, Subsystem } from '../logger';

const Logger = getLogger(Subsystem.Settings);

export class SettingsWebviewProvider {
  private panel: vscode.WebviewPanel | undefined;
  private readonly extensionUri: vscode.Uri;
  private readonly context: vscode.ExtensionContext;
  private readonly settingsManager: SettingsManager;
  private readonly chatManager: ChatManager;

  constructor(extensionUri: vscode.Uri, context: vscode.ExtensionContext, chatManager: ChatManager) {
    this.extensionUri = extensionUri;
    this.context = context;
    this.settingsManager = new SettingsManager(context);
    this.chatManager = chatManager;
    Logger.info('SettingsWebviewProvider initialized');
  }

  public async show() {
    Logger.info('Attempting to show settings webview');

    if (this.panel) {
      Logger.info('Settings panel already exists, revealing it');
      this.panel.reveal();
      return;
    }

    Logger.info('Creating new settings webview panel');
    this.panel = vscode.window.createWebviewPanel(
      'xyneSettings',
      'Xyne Settings',
      vscode.ViewColumn.One,
      {
        enableScripts: true,
        localResourceRoots: [
          vscode.Uri.joinPath(this.extensionUri, "webview-ui", "dist")
        ],
        retainContextWhenHidden: true,
      }
    );

    Logger.info('Webview panel created, setting up content and message handling');

    try {
      this.panel.webview.html = await this.getWebviewContent(this.panel.webview);
      Logger.info('Webview HTML content set successfully');
    } catch (error) {
      Logger.error(error, 'Failed to set webview HTML content');
      vscode.window.showErrorMessage(`Failed to load settings: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return;
    }

    this.setupWebviewMessageHandling();

    this.panel.onDidDispose(() => {
      Logger.info('Settings webview panel disposed');
      this.panel = undefined;
    });

    // Send initial settings to webview
    try {
      const settings = await this.settingsManager.getSettings();
      Logger.info(`Sending initial settings to webview: ${JSON.stringify(settings)}`);
      this.panel.webview.postMessage({
        type: 'settingsLoaded',
        settings: settings
      });
      Logger.info('Initial settings sent successfully');
    } catch (error) {
      Logger.error(`Failed to send initial settings to webview: ${error instanceof Error ? error.message : 'Unknown error'}`);
      vscode.window.showErrorMessage(`Failed to load initial settings: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private setupWebviewMessageHandling() {
    if (!this.panel) {
      Logger.warn('Cannot setup message handling - panel is undefined');
      return;
    }

    Logger.info('Setting up webview message handling');

    this.panel.webview.onDidReceiveMessage(async (message) => {
      Logger.info(`Received message from webview: ${message.type}`);

      try {
        switch (message.type) {
          case 'webviewReady':
            Logger.info('Webview reported ready state');
            break;

          case 'pageLoaded':
            Logger.info(`Webview page loaded: ${message.page}`);
            break;

          case 'getSettings':
            Logger.info('Webview requested settings');
            const settings = await this.settingsManager.getSettings();
            Logger.info(`Retrieved settings: ${JSON.stringify(settings)}`);
            this.panel?.webview.postMessage({
              type: 'settingsLoaded',
              settings: settings
            });
            Logger.info('Settings sent to webview');
            break;

          case 'updateSettings':
            Logger.info(`Webview requested settings update: ${JSON.stringify(message.settings)}`);
            await this.settingsManager.updateSettings(message.settings);

            // Handle auto completion settings
            if (message.settings.autoCompletionEnabled !== undefined) {
              await this.handleAutoCompletionSettings(message.settings.autoCompletionEnabled);
            }

            // Handle auto-compact settings
            if (message.settings.autoCompact) {
              this.chatManager.updateAutoCompactSettings(message.settings.autoCompact);
              Logger.info('Auto-compact settings updated in ChatManager');
            }

            // Refresh the AI provider to use the new settings
            Logger.info('Refreshing AI provider with new settings...');
            vscode.commands.executeCommand('xyne.refreshProvider');

            this.panel?.webview.postMessage({
              type: 'settingsUpdated',
              success: true
            });
            Logger.info('Settings updated successfully');
            vscode.window.showInformationMessage('Settings saved successfully!');
            break;

          case 'validateAwsCredentials':
            Logger.info('Webview requested AWS credentials validation');
            const isValid = await this.settingsManager.validateAwsCredentials(
              message.accessKeyId,
              message.secretAccessKey,
              message.region
            );
            Logger.info(`AWS credentials validation result: ${isValid}`);
            this.panel?.webview.postMessage({
              type: 'awsCredentialsValidated',
              isValid: isValid
            });
            break;

          case 'clearSettings':
            Logger.info('Webview requested settings clear');
            await this.settingsManager.clearSettings();
            const defaultSettings = await this.settingsManager.getSettings();
            Logger.info(`Settings cleared, sending default settings: ${JSON.stringify(defaultSettings)}`);
            this.panel?.webview.postMessage({
              type: 'settingsLoaded',
              settings: defaultSettings
            });
            vscode.window.showInformationMessage('Settings cleared successfully!');
            break;

          case 'error':
            Logger.error(`Webview reported error: ${message.message} - ${JSON.stringify(message)}`);
            vscode.window.showErrorMessage(`Settings Error: ${message.message}`);
            break;

          case 'log':
            Logger.info(`Webview log [${message.level}]: ${message.message} - ${JSON.stringify(message.data)}`);
            break;

          default:
            Logger.warn(`Unknown message type: ${message.type} - ${JSON.stringify(message)}`);
        }
      } catch (error) {
        Logger.error(`Error handling webview message: ${message.type} - ${error instanceof Error ? error.message : 'Unknown error'}`);
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';

        // Show error to user
        vscode.window.showErrorMessage(`Settings Error: ${errorMessage}`);

        // Send error back to webview
        this.panel?.webview.postMessage({
          type: 'error',
          message: errorMessage
        });
      }
    });
  }

  private async handleAutoCompletionSettings(enabled: boolean): Promise<void> {
    try {
      Logger.info(`Handling auto completion settings: ${enabled}`);
      
      const config = vscode.workspace.getConfiguration();
      
      if (enabled) {
        // Apply the auto completion settings
        await config.update('editor.inlineSuggest.enabled', true, vscode.ConfigurationTarget.Global);
        await config.update('editor.inlineSuggest.showToolbar', 'onHover', vscode.ConfigurationTarget.Global);
        await config.update('editor.suggest.preview', true, vscode.ConfigurationTarget.Global);
        await config.update('editor.quickSuggestions', {
          other: 'on',
          comments: 'off',
          strings: 'off'
        }, vscode.ConfigurationTarget.Global);
        await config.update('editor.suggestOnTriggerCharacters', true, vscode.ConfigurationTarget.Global);
        await config.update('editor.acceptSuggestionOnCommitCharacter', true, vscode.ConfigurationTarget.Global);
        await config.update('editor.acceptSuggestionOnEnter', 'on', vscode.ConfigurationTarget.Global);
        
        Logger.info('Auto completion settings enabled successfully');
        vscode.window.showInformationMessage('Auto completion settings have been enabled!');
      } else {
        // Revert to default settings
        await config.update('editor.inlineSuggest.enabled', undefined, vscode.ConfigurationTarget.Global);
        await config.update('editor.inlineSuggest.showToolbar', undefined, vscode.ConfigurationTarget.Global);
        await config.update('editor.suggest.preview', undefined, vscode.ConfigurationTarget.Global);
        await config.update('editor.quickSuggestions', undefined, vscode.ConfigurationTarget.Global);
        await config.update('editor.suggestOnTriggerCharacters', undefined, vscode.ConfigurationTarget.Global);
        await config.update('editor.acceptSuggestionOnCommitCharacter', undefined, vscode.ConfigurationTarget.Global);
        await config.update('editor.acceptSuggestionOnEnter', undefined, vscode.ConfigurationTarget.Global);
        
        Logger.info('Auto completion settings disabled successfully');
        vscode.window.showInformationMessage('Auto completion settings have been disabled!');
      }
    } catch (error) {
      Logger.error(`Failed to handle auto completion settings: ${error instanceof Error ? error.message : 'Unknown error'}`);
      vscode.window.showErrorMessage(`Failed to update auto completion settings: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async getWebviewContent(webview: vscode.Webview): Promise<string> {
    const isDevelopment = this.context.extensionMode === vscode.ExtensionMode.Development;
    const webviewDevServerUrl = 'http://localhost:5173';

    Logger.info(`Getting settings webview content - Development mode: ${isDevelopment}`);

    if (isDevelopment) {
      Logger.info('Using development mode - fetching content from Vite dev server');

      try {
        // Fetch the HTML content from Vite dev server
        const response = await fetch(`${webviewDevServerUrl}?page=settings`);
        if (!response.ok) {
          throw new Error(`Failed to fetch from Vite dev server: ${response.status}`);
        }

        let htmlContent = await response.text();

        // Convert relative URLs to absolute URLs pointing to the Vite dev server
        htmlContent = htmlContent
          .replace(/src="\/([^"]+)"/g, `src="${webviewDevServerUrl}/$1"`)
          .replace(/href="\/([^"]+)"/g, `href="${webviewDevServerUrl}/$1"`)
          .replace(/url\(\/([^)]+)\)/g, `url(${webviewDevServerUrl}/$1)`)
          .replace(/from "\/([^"]+)"/g, `from "${webviewDevServerUrl}/$1"`)
          .replace(/import "\/([^"]+)"/g, `import "${webviewDevServerUrl}/$1"`);

        // Update CSP to allow the webview resources
        const cspMetaTag = `
          <meta http-equiv="Content-Security-Policy" content="
            default-src 'none';
            style-src 'unsafe-inline' ${webview.cspSource} ${webviewDevServerUrl};
            script-src 'unsafe-eval' 'unsafe-inline' ${webview.cspSource} ${webviewDevServerUrl};
            connect-src ${webview.cspSource} ${webviewDevServerUrl} ws://localhost:5173 ws://localhost:8080;
            img-src ${webview.cspSource} ${webviewDevServerUrl} data:;
            font-src ${webview.cspSource} ${webviewDevServerUrl};
          ">`;

        // Replace or add CSP
        if (htmlContent.includes('<meta http-equiv="Content-Security-Policy"')) {
          htmlContent = htmlContent.replace(/<meta http-equiv="Content-Security-Policy"[^>]*>/i, cspMetaTag);
        } else if (htmlContent.includes('<head>')) {
          htmlContent = htmlContent.replace('<head>', `<head>\n    ${cspMetaTag}`);
        }

        // Add settings page indicator and VS Code API setup with keyboard shortcuts
        const bodyReplacement = `<body data-page="settings">
          <script>
            console.log('Settings webview loaded in development mode');

            // Set up global page context for React app
            window.__WEBVIEW_PAGE__ = 'settings';

            // Set up VS Code API once and make it globally available
            (function() {
              try {
                if (window.acquireVsCodeApi && !window.__vscodeApi) {
                  window.__vscodeApi = window.acquireVsCodeApi();
                  console.log('VS Code API acquired and stored globally');
                }
              } catch (error) {
                console.error('Error setting up VS Code API:', error);
              }
            })();

            // Keyboard shortcuts for input fields
            document.addEventListener('keydown', function(event) {
              const target = event.target;
              const isInput = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable;

              if (!isInput) return;

              const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
              const cmdKey = isMac ? event.metaKey : event.ctrlKey;

              if (cmdKey) {
                switch(event.key.toLowerCase()) {
                  case 'a':
                    event.stopPropagation();
                    try {
                      target.select();
                    } catch (error) {
                      console.error('Error selecting text:', error);
                    }
                    break;

                  case 'v':
                    // Let browser handle paste naturally - don't intercept
                    // This prevents double pasting issues
                    break;

                  case 'c':
                    event.stopPropagation();
                    try {
                      document.execCommand('copy');
                    } catch (error) {
                      console.error('Error copying:', error);
                    }
                    break;

                  case 'x':
                    event.stopPropagation();
                    try {
                      document.execCommand('cut');
                    } catch (error) {
                      console.error('Error cutting:', error);
                    }
                    break;

                  case 'z':
                    event.stopPropagation();
                    try {
                      if (event.shiftKey) {
                        document.execCommand('redo');
                      } else {
                        document.execCommand('undo');
                      }
                    } catch (error) {
                      console.error('Error with undo/redo:', error);
                    }
                    break;
                }
              }
            });

            // Error handling
            window.addEventListener('error', function(event) {
              console.error('Webview error:', event);
              if (window.__vscodeApi) {
                window.__vscodeApi.postMessage({type: 'error', message: event.message});
              }
            });
          </script>`;

        // Remove any existing vscode variable declarations to prevent conflicts
        htmlContent = htmlContent.replace(/(?:const|let|var)\s+vscode\s*=\s*[^;]+;?/g, '');
        htmlContent = htmlContent.replace(/window\.vscode\s*=\s*[^;]+;?/g, '');

        htmlContent = htmlContent.replace('<body>', bodyReplacement);

        Logger.info('Development HTML content prepared successfully');
        return htmlContent;

      } catch (error) {
        Logger.error(error, 'Failed to fetch from Vite dev server');
        throw new Error(`Failed to load settings webview: ${error}`);
      }
    } else {
      Logger.info('Using production mode - loading pre-built assets');
      // Production mode: Load pre-built static assets
      const builtIndexPath = vscode.Uri.joinPath(this.extensionUri, 'webview-ui', 'dist', 'index.html');
      try {
        Logger.info(`Reading built index.html from: ${builtIndexPath.fsPath}`);
        const htmlContentUint8Array = await vscode.workspace.fs.readFile(builtIndexPath);
        let htmlString = new TextDecoder().decode(htmlContentUint8Array);
        Logger.info('Successfully read built HTML content');

        const mainJsUri = webview.asWebviewUri(
          vscode.Uri.joinPath(this.extensionUri, 'webview-ui', 'dist', 'assets', 'main.js')
        );
        const indexCssUri = webview.asWebviewUri(
          vscode.Uri.joinPath(this.extensionUri, 'webview-ui', 'dist', 'assets', 'index.css')
        );

        htmlString = htmlString.replace('src="/assets/main.js"', `src="${mainJsUri}"`);
        htmlString = htmlString.replace('href="/assets/index.css"', `href="${indexCssUri}"`);

        const cspMetaTag = `
          <meta http-equiv="Content-Security-Policy" content="
            default-src 'none';
            style-src ${webview.cspSource} 'unsafe-inline';
            script-src ${webview.cspSource};
            img-src ${webview.cspSource} data:;
            font-src ${webview.cspSource};
          ">`;

        if (htmlString.includes("<head>")) {
            htmlString = htmlString.replace("<head>", `<head>\n    ${cspMetaTag}`);
        } else {
            htmlString = cspMetaTag + htmlString;
        }

        // Add settings page indicator
        htmlString = htmlString.replace('<body>', `<body data-page="settings">
          <script>
            console.log('Settings webview loaded in production mode');
            if (window.acquireVsCodeApi) {
              const vscode = acquireVsCodeApi();
              window.__vscodeApi = vscode;
            }
            window.addEventListener('error', function(event) {
              console.error('Webview error:', event);
              if (window.__vscodeApi) {
                window.__vscodeApi.postMessage({type: 'error', message: event.message});
              }
            });
          </script>`);

        Logger.info('Production HTML content prepared successfully');
        return htmlString;
      } catch (error) {
        Logger.error(error, "Error loading webview HTML for settings");
        throw new Error(`Failed to load webview content: ${error}`);
      }
    }
  }
}
import * as vscode from 'vscode';
import * as path from 'path';


function buildIgnorePatterns(): string[] {
  return [
    '**/node_modules/**',
    '**/.git/**',
    '**/.vscode/**',
    '**/dist/**',
    '**/build/**',
    '**/target/**',
    '**/bin/**',
    '**/obj/**',
    '**/__pycache__/**',
    '**/vendor/**',
    '**/*.min.js',
    '**/*.min.css',
    '**/*.pyc',
    '**/*.class',
    '**/*.o',
    '**/*.exe',
    '**/*.dll',
    '**/*.so',
    '**/*.dylib',
    '**/.spago/**',
    '**/output/**',
    '**/.DS_Store',
    '**/Thumbs.db',
    '**/*.log',
    '**/coverage/**',
    '**/.nyc_output/**',
    '**/tmp/**',
    '**/temp/**',
    '**/.next/**',
    '**/.nuxt/**',
    '**/out/**'
  ];
}

export async function listFiles(dirPath: string = '', recursive: boolean = true, limit: number = 10000): Promise<[string[], boolean]> {
  const workspaceFolders = vscode.workspace.workspaceFolders;

  if (!workspaceFolders || workspaceFolders.length === 0) {
    return [[], false];
  }

  const workspaceRoot = workspaceFolders[0].uri.fsPath;
  const targetPath = dirPath ? path.join(workspaceRoot, dirPath) : workspaceRoot;


  try {
    const searchPattern = recursive ? '**/*' : '*';

    const relativePattern = new vscode.RelativePattern(workspaceFolders[0], searchPattern);

    const excludePatterns = buildIgnorePatterns();
    const excludePattern = `{${excludePatterns.join(',')}}`;

    const fileUris = await vscode.workspace.findFiles(
      relativePattern,
      excludePattern,
      limit
    );

    console.log(`fileUris: ${fileUris}`);

    console.log(`Found ${fileUris.length} files in ${targetPath} with limit ${limit}`);
    const filePaths = fileUris.map(uri => {
      const relativePath = vscode.workspace.asRelativePath(uri);
      return relativePath;
    });

    filePaths.sort();

    const hitLimit = filePaths.length >= limit;

    return [filePaths, hitLimit];

  } catch (error) {
    console.error('Error listing files:', error);
    return [[], false];
  }
}

export async function getEnvironmentPrompt(): Promise<string> {
  const workspaceFolders = vscode.workspace.workspaceFolders;

  if (!workspaceFolders || workspaceFolders.length === 0) {
    return 'No workspace folder found.';
  }

  const workspaceRoot = workspaceFolders[0].uri.fsPath;
  const relativePath = path.relative(workspaceRoot, process.cwd());



  let prompt = `\n\n <environment> Current environment: ${relativePath ? relativePath + ' > ' : ''}`;
  const files = await listFiles(relativePath, true);

  prompt += `\nFiles in current directory:\n`;
  if (files[0].length === 0) {
    prompt += 'No files found.';
  } else {
    prompt += files[0].slice(0, 1000).join('\n'); // Show only the first 1000 files
  }

  prompt += `\n\nNote: The above files are listed from the workspace root: ${workspaceRoot}\n`;
  prompt += `(File list truncated. Use list_files on specific subdirectories if you need to explore further.)`;

  prompt += '\n </environment>\n\n';

  return prompt;

}
import * as fs from 'fs'
import * as path from 'path'
import * as vscode from 'vscode'

/**
 * Service for detecting and managing test mode activation
 * Test mode is activated when an evals.env file is present in the workspace
 */
export class TestMode {
    private static instance: TestMode | null = null
    private _isTestMode: boolean = false
    private evalsEnvPath: string | null = null

    private constructor() {
        this.detectTestMode()
    }

    public static getInstance(): TestMode {
        if (!TestMode.instance) {
            TestMode.instance = new TestMode()
        }
        return TestMode.instance
    }

    /**
     * Check if we're currently in test mode
     */
    public isInTestMode(): boolean {
        return this._isTestMode
    }

    /**
     * Get the path to the evals.env file if it exists
     */
    public getEvalsEnvPath(): string | null {
        return this.evalsEnvPath
    }

    /**
     * Detect test mode by checking for evals.env file in workspace or via environment variable
     */
    private detectTestMode(): void {
        const workspaceFolders = vscode.workspace.workspaceFolders
        console.log('[TestMode] Workspace folders:', workspaceFolders?.map(f => f.uri.fsPath))
        
        // Check for environment variable first
        const envEvalsPath = process.env.XYNE_EVALS_ENV_PATH
        if (envEvalsPath) {
            console.log('[TestMode] Using XYNE_EVALS_ENV_PATH:', envEvalsPath)
            if (fs.existsSync(envEvalsPath)) {
                this._isTestMode = true
                this.evalsEnvPath = envEvalsPath
                console.log('[TestMode] ✅ Test mode activated via env var - evals.env file detected at:', envEvalsPath)
                return
            } else {
                console.log('[TestMode] ❌ evals.env file not found at env path:', envEvalsPath)
            }
        }
        
        // Check for XYNE_TEST_MODE environment variable
        if (process.env.XYNE_TEST_MODE === 'true') {
            console.log('[TestMode] ✅ Test mode activated via XYNE_TEST_MODE=true')
            this._isTestMode = true
            this.evalsEnvPath = null
            return
        }
        
        if (!workspaceFolders || workspaceFolders.length === 0) {
            console.log('[TestMode] No workspace folders found')
            this._isTestMode = false
            this.evalsEnvPath = null
            return
        }

        // Check the first workspace folder for evals.env
        const workspaceRoot = workspaceFolders[0].uri.fsPath
        const evalsEnvPath = path.join(workspaceRoot, 'evals.env')
        console.log('[TestMode] Checking for evals.env at:', evalsEnvPath)

        try {
            if (fs.existsSync(evalsEnvPath)) {
                this._isTestMode = true
                this.evalsEnvPath = evalsEnvPath
                console.log('[TestMode] ✅ Test mode activated - evals.env file detected at:', evalsEnvPath)
            } else {
                this._isTestMode = false
                this.evalsEnvPath = null
                console.log('[TestMode] ❌ evals.env file not found at:', evalsEnvPath)
            }
        } catch (error) {
            console.error('[TestMode] Error checking for evals.env file:', error)
            this._isTestMode = false
            this.evalsEnvPath = null
        }
    }

    /**
     * Refresh test mode detection (useful when files change)
     */
    public refresh(): void {
        this.detectTestMode()
    }

    /**
     * Create an evals.env file in the workspace (for testing purposes)
     */
    public async createEvalsEnv(): Promise<void> {
        const workspaceFolders = vscode.workspace.workspaceFolders
        if (!workspaceFolders || workspaceFolders.length === 0) {
            throw new Error('No workspace folder available')
        }

        const workspaceRoot = workspaceFolders[0].uri.fsPath
        const evalsEnvPath = path.join(workspaceRoot, 'evals.env')

        const content = `# Xyne Code Test Mode Activation File
# Created at: ${new Date().toISOString()}
# 
# This file activates Xyne test mode for automated evaluations.
# Delete this file to deactivate test mode.
#
# This file is automatically detected by the Xyne extension
# and enables test mode for automated evaluations.
`

        try {
            fs.writeFileSync(evalsEnvPath, content)
            console.log('[TestMode] Created evals.env file at:', evalsEnvPath)
            this.refresh()
        } catch (error) {
            console.error('[TestMode] Error creating evals.env file:', error)
            throw error
        }
    }

    /**
     * Remove the evals.env file from the workspace
     */
    public async removeEvalsEnv(): Promise<void> {
        if (!this.evalsEnvPath) {
            return
        }

        try {
            if (fs.existsSync(this.evalsEnvPath)) {
                fs.unlinkSync(this.evalsEnvPath)
                console.log('[TestMode] Removed evals.env file from:', this.evalsEnvPath)
            }
            this.refresh()
        } catch (error) {
            console.error('[TestMode] Error removing evals.env file:', error)
            throw error
        }
    }
}

/**
 * Convenience function to check if we're in test mode
 */
export function isInTestMode(): boolean {
    return TestMode.getInstance().isInTestMode()
}
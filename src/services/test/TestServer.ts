import * as http from 'http'
import * as url from 'url'
import { isInTestMode } from './TestMode'
import { ToolExecutor } from '../../tools/executor'
import { Tool<PERSON>all, ToolResult } from '../../tools/types'

/**
 * HTTP server for test mode that provides API endpoints for evaluation control
 * This allows external evaluation tools to interact with Xyne Code
 */
// Singleton to prevent multiple TestServer instances
let globalTestServerInstance: TestServer | null = null;

export class TestServer {
    private server: http.Server | null = null
    private port: number = 9876
    private isRunning: boolean = false
    private toolExecutor: ToolExecutor | null = null

    constructor(private chatProvider: any, toolExecutor?: ToolExecutor) {
        // Singleton pattern - prevent multiple instances
        if (globalTestServerInstance) {
            console.log('[TestServer] Returning existing TestServer instance')
            return globalTestServerInstance
        }
        
        this.toolExecutor = toolExecutor || null
        
        // Only create server if we're in test mode
        const testMode = isInTestMode()
        console.log(`[TestServer] Constructor: testMode=${testMode}`)
        if (testMode) {
            console.log('[TestServer] Will create server when start() is called')
            globalTestServerInstance = this
        } else {
            console.log('[TestServer] Not in test mode, skipping server creation')
        }
    }

    /**
     * Start the test server with automatic port retry
     */
    public async start(): Promise<void> {
        if (this.isRunning) {
            console.log('[TestServer] Server already running')
            return
        }

        // Try ports from 9876 to 9885
        for (let port = 9876; port <= 9885; port++) {
            try {
                await this.startOnPort(port)
                this.port = port
                this.isRunning = true
                console.log(`[TestServer] ✅ Server successfully started on port ${port}`)
                return
            } catch (error: any) {
                if (error.code === 'EADDRINUSE') {
                    console.log(`[TestServer] Port ${port} in use, trying port ${port + 1}`)
                    continue
                } else {
                    console.error(`[TestServer] Failed to start server on port ${port}:`, error)
                    throw error
                }
            }
        }
        
        throw new Error('Could not start TestServer - all ports 9876-9885 are in use')
    }

    /**
     * Try to start server on a specific port
     */
    private startOnPort(port: number): Promise<void> {
        return new Promise((resolve, reject) => {
            // Create a fresh server instance for each attempt
            const server = http.createServer(async (req, res) => {
                console.log(`[TestServer] Request: ${req.method} ${req.url}`)
                // Enable CORS for cross-origin requests
                res.setHeader('Access-Control-Allow-Origin', '*')
                res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
                res.setHeader('Access-Control-Allow-Headers', 'Content-Type')

                if (req.method === 'OPTIONS') {
                    res.writeHead(204)
                    res.end()
                    return
                }

                const parsedUrl = url.parse(req.url || '', true)
                const pathname = parsedUrl.pathname

                try {
                    console.log(`[TestServer] Routing to: ${pathname}`)
                    switch (pathname) {
                        case '/health':
                            console.log('[TestServer] Handling /health')
                            await this.handleHealth(req, res)
                            break
                        case '/task':
                            await this.handleTask(req, res)
                            break
                        case '/status':
                            await this.handleStatus(req, res)
                            break
                        case '/shutdown':
                            await this.handleShutdown(req, res)
                            break
                        default:
                            res.writeHead(404, { 'Content-Type': 'application/json' })
                            res.end(JSON.stringify({ error: 'Not found' }))
                    }
                } catch (error) {
                    console.error('[TestServer] Request handling error:', error)
                    res.writeHead(500, { 'Content-Type': 'application/json' })
                    res.end(JSON.stringify({ 
                        error: 'Internal server error',
                        message: error instanceof Error ? error.message : 'Unknown error'
                    }))
                }
            })

            server.on('error', (error) => {
                reject(error)
            })

            server.listen(port, () => {
                this.server = server
                console.log(`[TestServer] Server bound to port ${port}`)
                resolve()
            })
        })
    }

    /**
     * Stop the test server
     */
    public async stop(): Promise<void> {
        if (!this.server || !this.isRunning) {
            return
        }

        return new Promise((resolve) => {
            this.server!.close(() => {
                this.isRunning = false
                console.log('[TestServer] Server stopped')
                resolve()
            })
        })
    }

    /**
     * Handle health check endpoint
     */
    private async handleHealth(req: http.IncomingMessage, res: http.ServerResponse): Promise<void> {
        res.writeHead(200, { 'Content-Type': 'application/json' })
        res.end(JSON.stringify({ 
            status: 'healthy',
            testMode: isInTestMode(),
            timestamp: new Date().toISOString()
        }))
    }

    /**
     * Handle task execution endpoint
     */
    private async handleTask(req: http.IncomingMessage, res: http.ServerResponse): Promise<void> {
        if (req.method === 'OPTIONS') {
            res.writeHead(204)
            res.end()
            return
        }

        if (req.method === 'POST') {
            // Handle task submission
            const body = await this.readRequestBody(req)
            try {
                const taskData = JSON.parse(body)
                
                // Execute the task through the chat provider
                const result = await this.executeTask(taskData)
                
                res.writeHead(200, { 'Content-Type': 'application/json' })
                res.end(JSON.stringify(result))
            } catch (error) {
                res.writeHead(400, { 'Content-Type': 'application/json' })
                res.end(JSON.stringify({ 
                    error: 'Invalid task data',
                    message: error instanceof Error ? error.message : 'Unknown error'
                }))
            }
        } else {
            res.writeHead(405, { 'Content-Type': 'application/json' })
            res.end(JSON.stringify({ error: 'Method not allowed' }))
        }
    }

    /**
     * Handle status endpoint
     */
    private async handleStatus(req: http.IncomingMessage, res: http.ServerResponse): Promise<void> {
        res.writeHead(200, { 'Content-Type': 'application/json' })
        res.end(JSON.stringify({
            testMode: isInTestMode(),
            serverRunning: this.isRunning,
            port: this.port,
            timestamp: new Date().toISOString()
        }))
    }

    /**
     * Handle shutdown endpoint
     */
    private async handleShutdown(req: http.IncomingMessage, res: http.ServerResponse): Promise<void> {
        if (req.method === 'POST') {
            res.writeHead(200, { 'Content-Type': 'application/json' })
            res.end(JSON.stringify({ message: 'Shutting down' }))
            
            // Stop the server after sending response
            setTimeout(() => {
                this.stop()
            }, 100)
        } else {
            res.writeHead(405, { 'Content-Type': 'application/json' })
            res.end(JSON.stringify({ error: 'Method not allowed' }))
        }
    }

    /**
     * Execute a task through the real Xyne Code ToolExecutor
     */
    private async executeTask(taskData: any): Promise<any> {
        const { toolCall, context } = taskData
        
        if (!this.toolExecutor) {
            return {
                success: false,
                error: 'ToolExecutor not available in TestServer',
                result: null
            }
        }
        
        if (!toolCall || !toolCall.name) {
            return {
                success: false,
                error: 'Invalid tool call - missing name',
                result: null
            }
        }
        
        try {
            console.log(`[TestServer] Executing tool: ${toolCall.name}`)
            console.log(`[TestServer] Workspace context: ${context?.workspaceRoot || 'none'}`)
            
            // Create a mock webview for tools that need it (like execute_command)
            const mockWebview = {
                postMessage: (message: any) => {
                    console.log(`[TestServer] Mock webview message:`, message)
                }
            }
            
            // Execute the tool using the real ToolExecutor with workspace override
            const result: ToolResult = await this.toolExecutor.executeToolCall(
                toolCall, 
                mockWebview as any, 
                context?.workspaceRoot
            )
            
            console.log(`[TestServer] Tool execution completed: ${result.error ? 'ERROR' : 'SUCCESS'}`)
            
            return {
                success: !result.error,
                error: result.error,
                result: result.result,
                toolCall: {
                    id: toolCall.id,
                    name: toolCall.name
                },
                timestamp: new Date().toISOString()
            }
        } catch (error) {
            console.error(`[TestServer] Tool execution failed:`, error)
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown execution error',
                result: null,
                toolCall: {
                    id: toolCall.id,
                    name: toolCall.name
                },
                timestamp: new Date().toISOString()
            }
        }
    }

    /**
     * Read the request body
     */
    private async readRequestBody(req: http.IncomingMessage): Promise<string> {
        return new Promise((resolve, reject) => {
            let body = ''
            req.on('data', (chunk) => {
                body += chunk.toString()
            })
            req.on('end', () => {
                resolve(body)
            })
            req.on('error', (error) => {
                reject(error)
            })
        })
    }

    /**
     * Get server status
     */
    public getStatus(): { running: boolean; port: number; testMode: boolean } {
        return {
            running: this.isRunning,
            port: this.port,
            testMode: isInTestMode()
        }
    }
}

/**
 * Create and start a test server instance
 */
export function createTestServer(chatProvider: any, toolExecutor?: ToolExecutor): TestServer {
    console.log('[TestServer] createTestServer function called')
    const server = new TestServer(chatProvider, toolExecutor)
    
    // Auto-start the server in test mode
    const testMode = isInTestMode()
    console.log(`[TestServer] Test mode in createTestServer: ${testMode}`)
    if (testMode) {
        console.log('[TestServer] Starting server...')
        server.start().then(() => {
            console.log('[TestServer] ✅ Server started successfully')
        }).catch((error) => {
            console.error('[TestServer] ❌ Failed to start test server:', error)
        })
    } else {
        console.log('[TestServer] Not starting server - test mode is false')
    }
    
    return server
}
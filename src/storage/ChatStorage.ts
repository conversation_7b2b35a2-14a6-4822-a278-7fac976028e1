import * as vscode from 'vscode';
import {
    Conversation,
    Message,
    ConversationSummary,
    StorageConfig,
    ExportOptions,
    ChatEvents
} from '../types/chat';

export class ChatStorage {
    private storagePath: vscode.Uri | null = null;
    private indexPath: vscode.Uri | null = null;
    private initialized = false;
    private config: StorageConfig;
    private eventHandlers: Partial<ChatEvents> = {};
    private index: any | null = null;

    constructor(
        private context: vscode.ExtensionContext,
        config: Partial<StorageConfig> = {}
    ) {
        this.config = {
            maxConversations: 100,
            maxMessagesPerConversation: 1000,
            autoArchiveAfterDays: 90,
            backupEnabled: true,
            compressionEnabled: false,
            ...config
        };
    }

    async initialize(): Promise<void> {
        if (this.initialized) {
            return;
        }

        try {
            // Use VSCode's global storage for cross-workspace persistence
            const globalStorageUri = this.context.globalStorageUri;
            this.storagePath = vscode.Uri.joinPath(globalStorageUri, 'xyne-conversations');
            this.indexPath = vscode.Uri.joinPath(this.storagePath, '_index.json');

            // Create storage directory using VSCode API
            await vscode.workspace.fs.createDirectory(this.storagePath);

            // Initialize index if it doesn't exist
            await this.ensureIndex();
            this.index = await this.readIndex();

            this.initialized = true;
        } catch (error) {
            const wrappedError = new Error(`Storage initialization failed: ${error instanceof Error ? error.message : error}`);
            this.emitEvent('storageError', wrappedError);
            throw wrappedError;
        }
    }

    private async ensureIndex(): Promise<void> {
        if (!this.indexPath) {
            return;
        }

        try {
            await vscode.workspace.fs.stat(this.indexPath);
        } catch {
            // Index doesn't exist, create it
            const index = {
                version: '1.0.0',
                conversations: [],
                lastCleanup: Date.now(),
                config: this.config
            };
            const content = Buffer.from(JSON.stringify(index, null, 2), 'utf8');
            await vscode.workspace.fs.writeFile(this.indexPath, content);
        }
    }

    private async readIndex(): Promise<any> {
        if (!this.indexPath) {
            throw new Error('Storage not initialized');
        }

        try {
            const content = await vscode.workspace.fs.readFile(this.indexPath);
            return JSON.parse(Buffer.from(content).toString('utf8'));
        } catch (error) {
            throw new Error(`Failed to read storage index: ${error}`);
        }
    }

    private async writeIndex(index: any): Promise<void> {
        if (!this.indexPath) {
            throw new Error('Storage not initialized');
        }

        try {
            const content = Buffer.from(JSON.stringify(index, null, 2), 'utf8');
            await vscode.workspace.fs.writeFile(this.indexPath, content);
        } catch (error) {
            throw new Error(`Failed to write storage index: ${error}`);
        }
    }

    async saveConversation(conversation: Conversation): Promise<void> {
        await this.initialize();

        try {
            // Apply limits
            if (conversation.messages.length > this.config.maxMessagesPerConversation!) {
                conversation.messages = conversation.messages.slice(-this.config.maxMessagesPerConversation!);
            }

            // Save conversation file
            const filePath = this.getConversationPath(conversation.id);
            const content = Buffer.from(JSON.stringify(conversation, null, 2), 'utf8');
            await vscode.workspace.fs.writeFile(filePath, content);

            // Update index
            await this.updateIndex(conversation);

            this.emitEvent('conversationUpdated', conversation);
        } catch (error) {
            this.emitEvent('storageError', new Error(`Failed to save conversation: ${error}`));
            throw error;
        }
    }

    async loadConversation(conversationId: string): Promise<Conversation | null> {
        await this.initialize();

        try {
            const filePath = this.getConversationPath(conversationId);
            const content = await vscode.workspace.fs.readFile(filePath);
            return JSON.parse(Buffer.from(content).toString('utf8')) as Conversation;
        } catch (error) {
            if ((error as vscode.FileSystemError).code === 'FileNotFound') {
                return null;
            }
            this.emitEvent('storageError', new Error(`Failed to load conversation: ${error}`));
            throw error;
        }
    }

    async listConversations(): Promise<ConversationSummary[]> {
        await this.initialize();

        try {
            // Always read fresh index from disk to ensure we have the latest conversations
            const freshIndex = await this.readIndex();
            return freshIndex.conversations
                .sort((a: any, b: any) => b.updatedAt - a.updatedAt)
                .slice(0, this.config.maxConversations);
        } catch (error) {
            this.emitEvent('storageError', new Error(`Failed to list conversations: ${error}`));
            return [];
        }
    }

    async deleteConversation(conversationId: string): Promise<void> {
        await this.initialize();

        try {
            // Delete conversation file
            const filePath = this.getConversationPath(conversationId);
            await vscode.workspace.fs.delete(filePath);

            // Remove from index
            this.index.conversations = this.index.conversations.filter((c: any) => c.id !== conversationId);
            await this.writeIndex(this.index);

            this.emitEvent('conversationDeleted', conversationId);
        } catch (error) {
            if ((error as vscode.FileSystemError).code !== 'FileNotFound') {
                this.emitEvent('storageError', new Error(`Failed to delete conversation: ${error}`));
                throw error;
            }
        }
    }

    async createConversation(title: string): Promise<Conversation> {
        await this.initialize();

        const conversation: Conversation = {
            id: this.generateId(),
            title,
            messages: [],
            createdAt: Date.now(),
            updatedAt: Date.now(),
            metadata: {}
        };

        await this.saveConversation(conversation);
        this.emitEvent('conversationCreated', conversation);

        return conversation;
    }

    async addMessage(conversationId: string, message: Message): Promise<void> {
        await this.initialize();

        const conversation = await this.loadConversation(conversationId);
        if (!conversation) {
            throw new Error(`Conversation ${conversationId} not found`);
        }

        conversation.messages.push(message);
        conversation.updatedAt = Date.now();

        await this.saveConversation(conversation);
        this.emitEvent('messageAdded', conversationId, message);
    }

    async addMessageAfter(conversationId: string, afterMessageId: string, message: Message): Promise<void> {
        await this.initialize();

        const conversation = await this.loadConversation(conversationId);
        if (!conversation) {
            throw new Error(`Conversation ${conversationId} not found`);
        }

        const idx = conversation.messages.findIndex(m => m.id === afterMessageId);
        if (idx === -1) {
            // If not found, just append to end
            conversation.messages.push(message);
        } else {
            conversation.messages.splice(idx + 1, 0, message);
        }
        conversation.updatedAt = Date.now();

        await this.saveConversation(conversation);
        this.emitEvent('messageAdded', conversationId, message);
    }

    async summarizeConversation(
        conversationId: string, 
        summaryMessage: Message, 
        summarizationCost: number
    ): Promise<void> {
        await this.initialize();

        const conversation = await this.loadConversation(conversationId);
        if (!conversation) {
            throw new Error(`Conversation ${conversationId} not found`);
        }

        // Mark conversation as summarized
        conversation.metadata = conversation.metadata || {};
        conversation.metadata.summaryMessageId = summaryMessage.id;
        conversation.metadata.isSummarized = true;
        conversation.metadata.lastSummarizedAt = Date.now();
        conversation.metadata.summarizationCost = (conversation.metadata.summarizationCost || 0) + summarizationCost;

        // Insert summary message at the end of existing messages
        // This creates: oldMessages → summaryMessage → (future newMessages)
        // UI shows full history, AI gets context from summary onwards
        conversation.messages.push(summaryMessage);
        conversation.updatedAt = Date.now();

        await this.saveConversation(conversation);
        this.emitEvent('conversationUpdated', conversation);
    }

    async truncateMessagesBeforeSummary(conversationId: string, summaryMessageId: string): Promise<void> {
        await this.initialize();

        const conversation = await this.loadConversation(conversationId);
        if (!conversation) {
            throw new Error(`Conversation ${conversationId} not found`);
        }

        // Find the summary message
        const summaryIndex = conversation.messages.findIndex(m => m.id === summaryMessageId);
        if (summaryIndex === -1) {
            throw new Error(`Summary message ${summaryMessageId} not found`);
        }

        // Keep only messages from the summary onwards
        conversation.messages = conversation.messages.slice(summaryIndex);
        conversation.updatedAt = Date.now();

        await this.saveConversation(conversation);
        this.emitEvent('conversationUpdated', conversation);
    }

    async getSummarizationStatus(conversationId: string): Promise<{
        isSummarized: boolean;
        summaryMessageId?: string;
        lastSummarizedAt?: number;
        summarizationCost?: number;
    }> {
        await this.initialize();

        const conversation = await this.loadConversation(conversationId);
        if (!conversation || !conversation.metadata) {
            return { isSummarized: false };
        }

        return {
            isSummarized: conversation.metadata.isSummarized || false,
            summaryMessageId: conversation.metadata.summaryMessageId,
            lastSummarizedAt: conversation.metadata.lastSummarizedAt,
            summarizationCost: conversation.metadata.summarizationCost
        };
    }

    async setAutoCompactEnabled(conversationId: string, enabled: boolean): Promise<void> {
        await this.initialize();

        const conversation = await this.loadConversation(conversationId);
        if (!conversation) {
            throw new Error(`Conversation ${conversationId} not found`);
        }

        conversation.metadata = conversation.metadata || {};
        conversation.metadata.autoCompactEnabled = enabled;
        conversation.updatedAt = Date.now();

        await this.saveConversation(conversation);
        this.emitEvent('conversationUpdated', conversation);
    }

    async exportConversation(
        conversationId: string,
        options: ExportOptions
    ): Promise<string> {
        await this.initialize();

        const conversation = await this.loadConversation(conversationId);
        if (!conversation) {
            throw new Error(`Conversation ${conversationId} not found`);
        }

        let messages = conversation.messages;

        // Apply date range filter
        if (options.dateRange) {
            messages = messages.filter(m =>
                m.timestamp >= options.dateRange!.start &&
                m.timestamp <= options.dateRange!.end
            );
        }

        switch (options.format) {
            case 'json':
                return JSON.stringify({
                    conversation,
                    exportedAt: Date.now(),
                    includeMetadata: options.includeMetadata
                }, null, 2);

            case 'markdown':
                return this.exportToMarkdown(conversation, messages, options.includeMetadata);

            case 'txt':
                return this.exportToText(conversation, messages);

            default:
                throw new Error(`Unsupported export format: ${options.format}`);
        }
    }

    async searchConversations(query: string): Promise<ConversationSummary[]> {
        await this.initialize();

        const conversations = await this.listConversations();
        const lowercaseQuery = query.toLowerCase();

        return conversations.filter(conv =>
            conv.title.toLowerCase().includes(lowercaseQuery) ||
            conv.preview?.toLowerCase().includes(lowercaseQuery)
        );
    }

    async cleanup(): Promise<void> {
        await this.initialize();

        try {
            const cutoffDate = Date.now() - (this.config.autoArchiveAfterDays! * 24 * 60 * 60 * 1000);

            // Archive old conversations
            const toArchive = this.index.conversations.filter((c: any) =>
                c.updatedAt < cutoffDate && !c.metadata?.pinned
            );

            for (const conv of toArchive) {
                if (this.config.backupEnabled) {
                    await this.backupConversation(conv.id);
                }
                await this.deleteConversation(conv.id);
            }

            // Update last cleanup time
            this.index.lastCleanup = Date.now();
            await this.writeIndex(this.index);
        } catch (error) {
            this.emitEvent('storageError', new Error(`Cleanup failed: ${error}`));
        }
    }

    // Event handling
    on<K extends keyof ChatEvents>(event: K, handler: ChatEvents[K]): void {
        this.eventHandlers[event] = handler;
    }

    // Force refresh index from disk
    async refreshIndex(): Promise<void> {
        await this.initialize();
        this.index = await this.readIndex();
    }

    private emitEvent<K extends keyof ChatEvents>(
        event: K,
        ...args: Parameters<ChatEvents[K]>
    ): void {
        const handler = this.eventHandlers[event];
        if (handler) {
            (handler as any)(...args);
        }
    }

    private async updateIndex(conversation: Conversation): Promise<void> {
        const summary: ConversationSummary = {
            id: conversation.id,
            title: conversation.title,
            messageCount: conversation.messages.length,
            updatedAt: conversation.updatedAt,
            createdAt: conversation.createdAt,
            isActive: false,
            preview: this.generatePreview(conversation),
            metadata: conversation.metadata,
            externalId: conversation.externalId,
            workspaceExternalId: conversation.workspaceExternalId,
            isBookmarked: conversation.isBookmarked || false,
            agentId: conversation.agentId
        };

        // Update or add conversation summary
        const existingIndex = this.index.conversations.findIndex((c: any) => c.id === conversation.id);
        if (existingIndex >= 0) {
            this.index.conversations[existingIndex] = summary;
        } else {
            this.index.conversations.push(summary);
        }

        // Enforce max conversations limit
        if (this.index.conversations.length > this.config.maxConversations!) {
            this.index.conversations = this.index.conversations
                .sort((a: any, b: any) => b.updatedAt - a.updatedAt)
                .slice(0, this.config.maxConversations);
        }

        await this.writeIndex(this.index);
    }

    private generatePreview(conversation: Conversation): string {
        const lastMessage = conversation.messages[conversation.messages.length - 1];
        if (!lastMessage) {
            return '';
        }

        return lastMessage.content.length > 100
            ? lastMessage.content.substring(0, 100) + '...'
            : lastMessage.content;
    }

    private getConversationPath(conversationId: string): vscode.Uri {
        return vscode.Uri.joinPath(this.storagePath!, `${conversationId}.json`);
    }

    private generateId(): string {
        return Math.random().toString(36).substring(2, 11) + Date.now().toString(36);
    }

    private exportToMarkdown(
        conversation: Conversation,
        messages: Message[],
        includeMetadata: boolean
    ): string {
        let content = `# ${conversation.title}\n\n`;

        if (includeMetadata) {
            content += `**Created:** ${new Date(conversation.createdAt).toLocaleString()}\n`;
            content += `**Updated:** ${new Date(conversation.updatedAt).toLocaleString()}\n`;
            content += `**Messages:** ${conversation.messages.length}\n\n`;
        }

        content += `**Exported:** ${new Date().toLocaleString()}\n\n---\n\n`;

        for (const msg of messages) {
            content += `## ${msg.type.toUpperCase()}\n`;
            content += `*${new Date(msg.timestamp).toLocaleString()}*\n\n`;
            content += `${msg.content}\n\n`;

            if (includeMetadata && msg.metadata) {
                content += `<details>\n<summary>Metadata</summary>\n\n`;
                content += `\`\`\`json\n${JSON.stringify(msg.metadata, null, 2)}\n\`\`\`\n\n`;
                content += `</details>\n\n`;
            }

            content += `---\n\n`;
        }

        return content;
    }

    private exportToText(conversation: Conversation, messages: Message[]): string {
        let content = `${conversation.title}\n`;
        content += `${'='.repeat(conversation.title.length)}\n\n`;
        content += `Exported: ${new Date().toLocaleString()}\n\n`;

        for (const msg of messages) {
            content += `[${msg.type.toUpperCase()}] ${new Date(msg.timestamp).toLocaleString()}\n`;
            content += `${msg.content}\n\n`;
            content += `${'-'.repeat(50)}\n\n`;
        }

        return content;
    }

    private async backupConversation(conversationId: string): Promise<void> {
        try {
            const conversation = await this.loadConversation(conversationId);
            if (!conversation) {
                return;
            }

            const backupDir = vscode.Uri.joinPath(this.storagePath!, '_backups');
            await vscode.workspace.fs.createDirectory(backupDir);

            const backupPath = vscode.Uri.joinPath(backupDir, `${conversationId}_${Date.now()}.json`);
            const content = Buffer.from(JSON.stringify(conversation, null, 2), 'utf8');
            await vscode.workspace.fs.writeFile(backupPath, content);
        } catch (error) {
            console.warn(`Failed to backup conversation ${conversationId}:`, error);
        }
    }
}

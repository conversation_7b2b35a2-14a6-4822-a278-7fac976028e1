const WebSocket = require('ws');
const pty = require('node-pty');

console.log('[PTY SERVER] Starting PTY WebSocket server...');

const wss = new WebSocket.Server({ port: 8080 });

wss.on('connection', function connection(ws, req) {
  console.log(`[PTY SERVER] New WebSocket connection from ${req.socket.remoteAddress}:${req.socket.remotePort}`);
  
  let ptyProcess = null;
  let started = false;
  let shell, shellArgs, cwd;
  let connectionId = Date.now().toString(36);

  ws.on('message', msg => {
    let message;
    try {
      message = JSON.parse(msg);
    } catch (e) {
      return;
    }
    
    if (!started) {
      // Accept cwd in the first message
      shell = process.env.SHELL || (process.platform === 'win32' ? 'cmd.exe' : 'bash');
      shellArgs = [];
      if (process.platform !== 'win32') {
        shellArgs.push('-l');
      }
      cwd = message.cwd || process.env.HOME;
      try {
        ptyProcess = pty.spawn(shell, shellArgs, {
          name: 'xterm-color',
          cols: 80,
          rows: 24,
          cwd,
          env: process.env
        });
        // Send pty output to client
        ptyProcess.onData(data => {
          ws.send(JSON.stringify({ type: 'output', data }));
        });
        
        ptyProcess.onExit(({ exitCode, signal }) => {
        });
        
        started = true;
        return;
      } catch (error) {
        console.error(`[PTY SERVER] [${connectionId}] Failed to spawn PTY process:`, error);
        ws.close();
        return;
      }
    }
    
    if (message.type === 'input') {
      if (ptyProcess) {
        ptyProcess.write(message.data);
      } else {
        console.error(`[PTY SERVER] [${connectionId}] No PTY process available for input`);
      }
    } else if (message.type === 'resize') {
      if (ptyProcess) {
        ptyProcess.resize(message.cols, message.rows);
      } else {
        console.error(`[PTY SERVER] [${connectionId}] No PTY process available for resize`);
      }
    } else {
    }
  });

  ws.on('close', (code, reason) => {
    if (ptyProcess) {
      ptyProcess.kill();
    }
  });

  ws.on('error', (error) => {
    console.error(`[PTY SERVER] [${connectionId}] WebSocket error:`, error);
  });
});

wss.on('error', (error) => {
  console.error('[PTY SERVER] WebSocket server error:', error);
});

console.log('[PTY SERVER] PTY WebSocket server running on ws://localhost:8080');
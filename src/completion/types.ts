export interface CompletionContext {
  filePath: string;
  language: string;
  cursorPosition: {
    line: number;
    character: number;
  };
  textBeforeCursor: string;
  textAfterCursor: string;
  currentLine: string;
  surroundingCode: string;
  imports: string[];
  functionSignatures: string[];
  localVariables: string[];
}


export interface CompletionResult {
  text: string;
  range?: {
    start: { line: number; character: number };
    end: { line: number; character: number };
  };
  confidence?: number;
}
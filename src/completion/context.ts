import * as vscode from 'vscode';
import { CompletionContext } from './types';
import { getLogger, Subsystem } from '../logger';

const Logger = getLogger(Subsystem.Extension);

export class ContextAnalyzer {
  private static readonly CONTEXT_LINES = 50; // Lines of context to extract around cursor

  /**
   * Analyzes the current editor context to provide rich information for AI completion
   */
  public static async analyzeContext(
    document: vscode.TextDocument,
    position: vscode.Position
  ): Promise<CompletionContext> {
    const filePath = document.uri.fsPath;
    const language = document.languageId;
    
    // Get text before and after cursor
    const textBeforeCursor = document.getText(new vscode.Range(
      new vscode.Position(0, 0),
      position
    ));
    
    const textAfterCursor = document.getText(new vscode.Range(
      position,
      new vscode.Position(document.lineCount, 0)
    ));

    // Get current line
    const currentLine = document.lineAt(position.line).text;

    // Get surrounding code context
    const startLine = Math.max(0, position.line - this.CONTEXT_LINES);
    const endLine = Math.min(document.lineCount - 1, position.line + this.CONTEXT_LINES);
    const surroundingCode = document.getText(new vscode.Range(
      new vscode.Position(startLine, 0),
      new vscode.Position(endLine, 0)
    ));

    // Use enhanced regex-based analysis (TreeSitter disabled due to environment issues)
    const imports = this.extractImports(document.getText());
    const functionSignatures = this.extractFunctionSignatures(document.getText(), language);
    const localVariables = this.extractLocalVariables(textBeforeCursor, language);

    return {
      filePath,
      language,
      cursorPosition: {
        line: position.line,
        character: position.character
      },
      textBeforeCursor,
      textAfterCursor,
      currentLine,
      surroundingCode,
      imports,
      functionSignatures,
      localVariables
    };
  }


  /**
   * Extracts import statements from the document
   */
  private static extractImports(text: string): string[] {
    const imports: string[] = [];
    
    // TypeScript/JavaScript imports
    const importRegex = /^import\s+.*?from\s+['"].*?['"];?$/gm;
    const requireRegex = /^const\s+.*?=\s+require\(['"].*?['"]\);?$/gm;
    
    let match;
    while ((match = importRegex.exec(text)) !== null) {
      imports.push(match[0].trim());
    }
    
    while ((match = requireRegex.exec(text)) !== null) {
      imports.push(match[0].trim());
    }

    // Python imports
    const pythonImportRegex = /^(?:from\s+\S+\s+)?import\s+.*$/gm;
    while ((match = pythonImportRegex.exec(text)) !== null) {
      imports.push(match[0].trim());
    }

    return imports;
  }

  /**
   * Extracts function signatures from the document
   */
  private static extractFunctionSignatures(text: string, language: string): string[] {
    const signatures: string[] = [];
    
    if (language === 'typescript' || language === 'javascript') {
      // Enhanced function declarations and expressions
      const functionRegex = /(?:export\s+(?:default\s+)?)?(?:async\s+)?function\s+\w+\s*\([^)]*\)(?:\s*:\s*[^{]+)?/g;
      // Enhanced arrow functions with better parameter detection
      const arrowFunctionRegex = /(?:export\s+)?const\s+\w+\s*:\s*[^=]*=\s*(?:async\s+)?\([^)]*\)\s*=>\s*[^;]*/g;
      const simpleArrowRegex = /(?:export\s+)?const\s+\w+\s*=\s*(?:async\s+)?\([^)]*\)\s*=>/g;
      // Enhanced method definitions
      const methodRegex = /(?:public|private|protected)?\s*(?:static\s+)?(?:async\s+)?\w+\s*\([^)]*\)(?:\s*:\s*[^{]+)?(?:\s*{)?/g;
      // Class method definitions
      const classMethodRegex = /^\s*(?:public|private|protected)?\s*(?:static\s+)?(?:async\s+)?\w+\s*\([^)]*\)(?:\s*:\s*[^{]+)?/gm;
      
      [functionRegex, arrowFunctionRegex, simpleArrowRegex, methodRegex, classMethodRegex].forEach(regex => {
        let match;
        while ((match = regex.exec(text)) !== null) {
          const signature = match[0].trim().replace(/\s*{\s*$/, ''); // Remove trailing {
          if (signature && !signatures.includes(signature)) {
            signatures.push(signature);
          }
        }
      });
    }
    
    if (language === 'python') {
      // Python function definitions
      const pythonFunctionRegex = /def\s+\w+\s*\([^)]*\)(?:\s*->\s*[^:]+)?:/g;
      let match;
      while ((match = pythonFunctionRegex.exec(text)) !== null) {
        signatures.push(match[0].trim());
      }
    }

    return signatures;
  }

  /**
   * Extracts local variables from text before cursor
   */
  private static extractLocalVariables(textBeforeCursor: string, language: string): string[] {
    const variables: string[] = [];
    
    if (language === 'typescript' || language === 'javascript') {
      // Variable declarations
      const varRegex = /(?:const|let|var)\s+(\w+)/g;
      // Function parameters
      const paramRegex = /function\s+\w+\s*\(([^)]*)\)|=\s*\(([^)]*)\)\s*=>/g;
      
      let match;
      while ((match = varRegex.exec(textBeforeCursor)) !== null) {
        variables.push(match[1]);
      }
      
      while ((match = paramRegex.exec(textBeforeCursor)) !== null) {
        const params = (match[1] || match[2] || '').split(',');
        params.forEach(param => {
          const cleanParam = param.trim().split(':')[0].split('=')[0].trim();
          if (cleanParam) {
            variables.push(cleanParam);
          }
        });
      }
    }
    
    if (language === 'python') {
      // Python variable assignments
      const pythonVarRegex = /(\w+)\s*=/g;
      let match;
      while ((match = pythonVarRegex.exec(textBeforeCursor)) !== null) {
        variables.push(match[1]);
      }
    }

    // Remove duplicates and filter out common keywords
    const filteredVars = [...new Set(variables)].filter(v => 
      !['if', 'else', 'for', 'while', 'return', 'function', 'class'].includes(v)
    );

    return filteredVars;
  }

  /**
   * Determines if the current position is a good candidate for completion
   */
  public static shouldTriggerCompletion(
    document: vscode.TextDocument,
    position: vscode.Position
  ): boolean {
    const line = document.lineAt(position.line);
    const textBeforeCursor = line.text.substring(0, position.character);
    
    Logger.debug(`Checking trigger for: "${textBeforeCursor}" (line: "${line.text}")`);
    
    // Don't trigger in comments or strings
    if (this.isInCommentOrString(document, position)) {
      Logger.debug('Skipping completion: in comment or string');
      return false;
    }
    
    // Don't trigger if line is empty or only whitespace
    if (textBeforeCursor.trim().length === 0) {
      Logger.debug('Skipping completion: empty line');
      return false;
    }
    
    // Don't trigger immediately after certain characters
    const lastChar = textBeforeCursor.slice(-1);
    if (['{', '}', '(', ')', '[', ']', ';', ','].includes(lastChar)) {
      Logger.debug(`Skipping completion: after special character "${lastChar}"`);
      return false;
    }

    Logger.debug('Trigger condition met');
    return true;
  }

  /**
   * Checks if the position is inside a comment or string
   */
  private static isInCommentOrString(
    document: vscode.TextDocument,
    position: vscode.Position
  ): boolean {
    try {
      // Get tokens from VS Code's language service if available
      // This is a simplified implementation - in practice, you'd want to use
      // the language service or a proper tokenizer
      const line = document.lineAt(position.line);
      const textBeforeCursor = line.text.substring(0, position.character);
      
      // Simple heuristic: check for common comment patterns
      if (textBeforeCursor.includes('//') || textBeforeCursor.includes('/*')) {
        return true;
      }
      
      // Simple heuristic: check for strings
      const singleQuotes = (textBeforeCursor.match(/'/g) || []).length;
      const doubleQuotes = (textBeforeCursor.match(/"/g) || []).length;
      const backticks = (textBeforeCursor.match(/`/g) || []).length;
      
      return (singleQuotes % 2 === 1) || (doubleQuotes % 2 === 1) || (backticks % 2 === 1);
    } catch (error) {
      Logger.error(error, 'Error checking comment/string context');
      return false;
    }
  }
}
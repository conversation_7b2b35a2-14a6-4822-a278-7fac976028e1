import * as vscode from 'vscode';
import { AIService } from '../ai/service';
import { ContextAnalyzer } from './context';
import { CompletionContext, CompletionResult } from './types';
import { CompletionSettings } from '../ai/types';
import { getLogger, Subsystem } from '../logger';
import { SettingsManager } from '../settings/manager';

const Logger = getLogger(Subsystem.Extension);

export class XyneCompletionProvider implements vscode.InlineCompletionItemProvider {
  private readonly aiService: AIService;
  private readonly settingsManager: SettingsManager;
  private readonly debounceMap = new Map<string, NodeJS.Timeout>();
  private completionSettings: CompletionSettings;

  constructor(aiService: AIService, settingsManager: SettingsManager) {
    Logger.info('XyneCompletionProvider constructor called');
    this.aiService = aiService;
    this.settingsManager = settingsManager;
    this.completionSettings = {
      enabled: true,
      triggerDelay: 300,
      maxCompletionLength: 200,
      enableMultiLineCompletions: true,
      contextLines: 50
    };
    
    this.loadSettings();
    Logger.info(`XyneCompletionProvider initialized with enabled=${this.completionSettings.enabled}`);
  }

  private async loadSettings(): Promise<void> {
    try {
      const settings = await this.settingsManager.getSettings();
      this.completionSettings = {
        ...this.completionSettings,
        ...(settings.completion || {})
      };
    } catch (error) {
      Logger.error(error, 'Failed to load completion settings, using defaults');
    }
  }

  async provideInlineCompletionItems(
    document: vscode.TextDocument,
    position: vscode.Position,
    context: vscode.InlineCompletionContext,
    token: vscode.CancellationToken
  ): Promise<vscode.InlineCompletionItem[] | vscode.InlineCompletionList | null> {
    
    Logger.debug(`Completion requested for ${document.languageId} at ${position.line}:${position.character}`);
    
    // Check if completions are enabled
    if (!this.completionSettings.enabled) {
      Logger.debug('Completions disabled in settings');
      return null;
    }

    // Check if AI service is available
    if (!this.aiService.isAvailable()) {
      Logger.debug('AI service not available for completion');
      return null;
    }

    // Check if this is a good position for completion
    if (!ContextAnalyzer.shouldTriggerCompletion(document, position)) {
      Logger.debug('Position not suitable for completion');
      return null;
    }

    Logger.debug('All checks passed, proceeding with completion generation');

    try {
      // Debounce completions to avoid too many requests
      const documentKey = document.uri.toString() + position.line + position.character;
      
      return new Promise((resolve) => {
        // Clear existing timeout for this document position
        const existingTimeout = this.debounceMap.get(documentKey);
        if (existingTimeout) {
          clearTimeout(existingTimeout);
        }

        // Set new timeout
        const timeout = setTimeout(async () => {
          try {
            if (token.isCancellationRequested) {
              Logger.debug('Completion cancelled');
              resolve(null);
              return;
            }

            Logger.debug('Generating completions after debounce delay');
            const completions = await this.generateCompletions(document, position, token);
            this.debounceMap.delete(documentKey);
            Logger.debug(`Generated ${completions?.length || 0} completions`);
            Logger.debug(`Completion is ${JSON.stringify(completions)}`);
            resolve(completions);
          } catch (error) {
            Logger.error(error, 'Error generating completions');
            this.debounceMap.delete(documentKey);
            resolve(null);
          }
        }, this.completionSettings.triggerDelay);

        this.debounceMap.set(documentKey, timeout);
      });

    } catch (error) {
      Logger.error(error, 'Error in provideInlineCompletionItems');
      return null;
    }
  }

  private async generateCompletions(
    document: vscode.TextDocument,
    position: vscode.Position,
    token: vscode.CancellationToken
  ): Promise<vscode.InlineCompletionItem[] | null> {
    
    try {
      // Analyze context
      const context = await ContextAnalyzer.analyzeContext(document, position);
      
      if (token.isCancellationRequested) {
        return null;
      }

      // Generate completion using AI
      const completion = await this.requestCompletion(context, token);
      
      if (!completion || token.isCancellationRequested) {
        return null;
      }

      // Create VSCode completion item
      const item = new vscode.InlineCompletionItem(completion.text);
      
      // Set range and command properties
      item.range = new vscode.Range(position, position);
      item.command = {
        command: 'editor.action.inlineSuggest.commit',
        title: 'Accept Suggestion'
      };

      Logger.debug(`Generated completion item: text="${completion.text}", range=${item.range?.start.line}:${item.range?.start.character}`);
      Logger.debug(`Completion item properties:`, JSON.stringify({ 
        text: completion.text, 
        hasRange: !!item.range, 
        hasCommand: !!item.command 
      }));
      return [item];

    } catch (error) {
      Logger.error(error, 'Error generating completions');
      return null;
    }
  }

  private async requestCompletion(
    context: CompletionContext,
    token: vscode.CancellationToken
  ): Promise<CompletionResult | null> {
    
    try {
      const prompt = this.buildCompletionPrompt(context);
      
      if (token.isCancellationRequested) {
        return null;
      }

      // Use the existing AI service to get completion
      const conversationHistory = [{
        role: "user" as const,
        content: [{ text: prompt }]
      }];

      let completionText = '';
      
      // Stream the completion
      for await (const chunk of this.aiService.converseStream(conversationHistory, {}, undefined)) {
        if (token.isCancellationRequested) {
          return null;
        }
        
        if (chunk.text) {
          completionText += chunk.text;
        }
        
        if (chunk.isComplete) {
          break;
        }

        // Stop if we've reached the max length
        if (completionText.length >= this.completionSettings.maxCompletionLength) {
          break;
        }
      }

      // Clean up the completion text
      const cleanedText = this.cleanCompletionText(completionText, context);
      
      if (!cleanedText) {
        return null;
      }

      return {
        text: cleanedText,
        confidence: 0.8 // Default confidence
      };

    } catch (error) {
      Logger.error(error, 'Error requesting completion from AI');
      return null;
    }
  }

  private buildCompletionPrompt(context: CompletionContext): string {
    const { language, currentLine } = context;
    
    // Get the full file content
    const fullFileContent = context.textBeforeCursor + context.textAfterCursor;
    
    // Get context around cursor
    const beforeCursor = context.textBeforeCursor.slice(-50); // Last 50 chars before cursor
    const afterCursor = context.textAfterCursor.slice(0, 50); // Next 50 chars after cursor
    
    let prompt = `You are an expert code completion assistant. Complete the code at the cursor position.

LANGUAGE: ${language}
FILE: ${context.filePath}

FULL FILE CONTENT:
\`\`\`${language}
${fullFileContent}
\`\`\`

CURSOR POSITION:
Current line: "${currentLine}"
Text before cursor: "${beforeCursor}"
Text after cursor: "${afterCursor}"

CRITICAL INSTRUCTIONS:
1. Return ONLY the text that should be inserted at the cursor position
2. DO NOT repeat any text that is already before the cursor
3. DO NOT include the full line - only the missing part
4. Analyze the full file context to understand what should come next
5. Match the existing code style and patterns in the file
6. For function calls, suggest appropriate parameters
7. For object property access, suggest relevant properties/methods
8. Keep completions concise and syntactically correct
9. Maximum ${this.completionSettings.maxCompletionLength} characters
10. NO explanations, markdown, or extra text

EXAMPLE:
If current line is "let x" and cursor is after "x", return " = 10" (not "let x = 10")

Complete the code at cursor:`;

    return prompt;
  }

  private cleanCompletionText(rawText: string, context: CompletionContext): string {
    if (!rawText || typeof rawText !== 'string') {
      return '';
    }

    let cleaned = rawText.trim();

    // Remove common prefixes that might be added by the AI
    const prefixesToRemove = [
      'Completion:',
      'Complete:',
      'Here is the completion:',
      'The completion is:',
      '```' + context.language,
      '```',
      'javascript',
      'typescript',
      'python'
    ];

    for (const prefix of prefixesToRemove) {
      if (cleaned.toLowerCase().startsWith(prefix.toLowerCase())) {
        cleaned = cleaned.substring(prefix.length).trim();
      }
    }

    // Remove trailing markdown or explanations
    const lines = cleaned.split('\n');
    const codeLines: string[] = [];
    
    for (const line of lines) {
      // Stop at explanations or comments that look like AI responses
      if (line.includes('This completion') || 
          line.includes('The above code') ||
          line.includes('Explanation:') ||
          line.startsWith('//') && line.includes('completion')) {
        break;
      }
      codeLines.push(line);
    }

    cleaned = codeLines.join('\n').trim();

    // Remove overlapping content - if AI returned full line instead of just completion
    const currentLineContent = context.currentLine.trim();
    const textBeforeCursor = context.textBeforeCursor.split('\n').pop() || '';
    
    // If the completion starts with text that's already before the cursor, remove the overlap
    if (textBeforeCursor && cleaned.startsWith(textBeforeCursor.trim())) {
      cleaned = cleaned.substring(textBeforeCursor.trim().length).trim();
    }
    
    // If the completion contains the current line, extract only the part after cursor
    if (currentLineContent && cleaned.includes(currentLineContent)) {
      const currentLineIndex = cleaned.indexOf(currentLineContent);
      if (currentLineIndex === 0) {
        // The completion starts with the current line, get what comes after
        cleaned = cleaned.substring(currentLineContent.length).trim();
      }
    }

    // Limit length
    if (cleaned.length > this.completionSettings.maxCompletionLength) {
      cleaned = cleaned.substring(0, this.completionSettings.maxCompletionLength);
      
      // Try to cut at a reasonable boundary
      const lastSpace = cleaned.lastIndexOf(' ');
      const lastNewline = cleaned.lastIndexOf('\n');
      const cutPoint = Math.max(lastSpace, lastNewline);
      
      if (cutPoint > cleaned.length * 0.8) { // Only cut if we don't lose too much
        cleaned = cleaned.substring(0, cutPoint);
      }
    }

    // Don't return single-character completions unless they're meaningful
    if (cleaned.length === 1 && ![';', ')', '}', ']'].includes(cleaned)) {
      return '';
    }

    return cleaned;
  }

  /**
   * Updates completion settings
   */
  public async updateSettings(newSettings: Partial<CompletionSettings>): Promise<void> {
    this.completionSettings = { ...this.completionSettings, ...newSettings };
    Logger.info(`Completion settings updated: enabled=${this.completionSettings.enabled}`);
  }

  /**
   * Disposes of the provider and cleans up resources
   */
  public dispose(): void {
    // Clear all pending timeouts
    for (const timeout of this.debounceMap.values()) {
      clearTimeout(timeout);
    }
    this.debounceMap.clear();
  }
}
export interface Message {
    id: string;
    timestamp: number;
    type: MessageType;
    content: string | any[];
    metadata?: MessageMetadata;
    externalId?: string;
    workspaceExternalId?: string;
    thinking?: string;
    modelId?: string;
    sources?: any[];
    fileIds?: string[];
    errorMessage?: string;
    sessionId?: string;
    role?: 'user' | 'assistant'; // For AWS Bedrock compatibility
}

// Separate interface for AWS Bedrock message compatibility
export interface AwsBedrockMessage {
    role: 'user' | 'assistant';
    content: any[];
    id?: string;
    timestamp?: number;
    type?: MessageType;
}

export interface MessageMetadata {
    model?: string;
    tokens?: number;
    cost?: number;
    responseTime?: number;
    error?: string;
    attachments?: string[];
    attachedFiles?: string; // JSON string of AttachedFile[]
    edited?: boolean;
    editedAt?: number;
    feedback?: 'positive' | 'negative';
    queryRouterClassification?: any[];
    tool_executions?: any[]; // Array of tool execution results
    // For file change review
    fileChangeReview?: {
        filePath: string;
        originalContent: string;
        newContent: string;
        diffSummary: string;
        language?: string;
        staged?: boolean;
        changeId?: string;
        reviewStatus?: 'accepted' | 'rejected' | 'auto_approve';
    };
    // For summarization
    isSummary?: boolean;
    summaryData?: {
        originalMessageCount: number;
        summarizedAt: number;
        tokensUsed: {
            input: number;
            output: number;
        };
        cost: number;
    };
    // Add checkpoint commit hash for restore checkpoint feature
    checkpointCommit?: string;
    // For interrupted message tracking
    isInterrupted?: boolean;
    interruptedText?: string; // Partial text that was being generated
    wasStreaming?: boolean; // Whether this message was being streamed when interrupted
    interruptedAt?: number;
    streamingProgress?: number; // How much of the response was completed (0-1)
    // For streaming timeout
    isStreamingTimeout?: boolean;
    timeoutDurationMs?: number;
}

export interface Conversation {
    id: string;
    title: string;
    messages: Message[];
    createdAt: number;
    updatedAt: number;
    metadata?: ConversationMetadata;
    externalId?: string;
    workspaceExternalId?: string;
    attachments?: any[];
    agentId?: string;
    isBookmarked?: boolean;
}

export interface ConversationMetadata {
    tags?: string[];
    archived?: boolean;
    pinned?: boolean;
    totalTokens?: number;
    totalCost?: number;
    modelPreference?: string;
    email?: string;
    // Summarization metadata
    summaryMessageId?: string;
    isSummarized?: boolean;
    lastSummarizedAt?: number;
    autoCompactEnabled?: boolean;
    summarizationCost?: number;
    // Interrupted conversation tracking
    isInterrupted?: boolean;
    interruptedAt?: number;
    interruptionReason?: 'user_stop' | 'error' | 'timeout' | 'manual_switch';
    lastActiveMessageId?: string;
    interruptedResponseText?: string; // Partial response that was being generated
    canResume?: boolean;
    // Historical interruption data (for keeping track of previous interruptions)
    lastInterruptedAt?: number;
    lastInterruptionReason?: 'user_stop' | 'error' | 'timeout' | 'manual_switch';
}

export interface ConversationSummary {
    id: string;
    title: string;
    messageCount: number;
    updatedAt: number;
    createdAt: number;
    isActive: boolean;
    preview?: string;
    metadata?: ConversationMetadata;
    externalId?: string;
    workspaceExternalId?: string;
    isBookmarked?: boolean;
    agentId?: string;
}

export enum MessageType {
    USER = 'user',
    ASSISTANT = 'assistant',
    SYSTEM = 'system',
    ERROR = 'error',
    TERMINAL = 'terminal'
}

export interface StorageConfig {
    maxConversations?: number;
    maxMessagesPerConversation?: number;
    autoArchiveAfterDays?: number;
    backupEnabled?: boolean;
    compressionEnabled?: boolean;
}

export interface ExportOptions {
    format: 'json' | 'markdown' | 'txt';
    includeMetadata: boolean;
    dateRange?: {
        start: number;
        end: number;
    };
}

export interface ChatEvents {
    conversationCreated: (conversation: Conversation) => void;
    conversationDeleted: (conversationId: string) => void;
    conversationUpdated: (conversation: Conversation) => void;
    messageAdded: (conversationId: string, message: Message) => void;
    storageError: (error: Error) => void;
    summarizationStarted: (conversationId: string) => void;
    summarizationProgress: (conversationId: string, progress: { stage: string; message: string; progress?: number }) => void;
    summarizationCompleted: (conversationId: string, result: { cost: number; messageCount: number }) => void;
    summarizationFailed: (conversationId: string, error: Error) => void;
}

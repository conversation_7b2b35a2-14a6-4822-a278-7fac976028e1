import * as path from 'path';
import * as fs from 'fs-extra';
import simpleGit, { SimpleGit } from 'simple-git';
import { getLogger, Subsystem } from '../logger';
import * as vscode from 'vscode';

const Logger = getLogger(Subsystem.Extension);

export interface ShadowGitChange {
  id: string;
  filePath: string;
  relativePath: string;
  originalContent: string;
  newContent: string;
  diffSummary: string;
  timestamp: number;
  status: 'pending' | 'accepted' | 'rejected';
  commitHash?: string;
  checkpointId?: string; // Link to checkpoint
}

export interface CheckpointFileState {
  checkpointId: string;
  filePath: string;
  relativePath: string;
  content: string;
  timestamp: number;
}

export class ShadowGitManager {
  private git: SimpleGit;
  private changes: Map<string, ShadowGitChange> = new Map();
  private conversationId: string;
  private currentCheckpointId: string | null = null;
  private checkpointCommits: Map<string, string> = new Map(); // checkpointId -> commitHash
  private checkpointFiles: Map<string, CheckpointFileState[]> = new Map(); // checkpointId -> file states
  private allCheckpointIds: string[] = [];
  private originalFileContents: Map<string, string> = new Map();

  constructor(private shadowGitPath: string, conversationId: string) {
    this.conversationId = conversationId;
    Logger.info(`🔧 Creating ShadowGitManager for conversation: ${conversationId} at path: ${shadowGitPath}`);
    
    // Ensure the directory exists before creating simple-git instance
    try {
      if (!fs.existsSync(shadowGitPath)) {
        Logger.info(`📁 Creating shadow Git directory: ${shadowGitPath}`);
        fs.mkdirSync(shadowGitPath, { recursive: true });
      }
    } catch (error) {
      Logger.error(error, `Failed to create shadow Git directory: ${shadowGitPath}`);
      throw new Error(`Cannot create shadow Git directory: ${shadowGitPath}`);
    }
    
    this.git = simpleGit(shadowGitPath);
    Logger.info(`✅ ShadowGitManager created successfully for conversation: ${conversationId}`);
  }

  /**
   * Initialize the shadow Git repository
   */
  async init(): Promise<void> {
    try {
      Logger.info(`🔧 Initializing shadow Git repository for conversation: ${this.conversationId} at: ${this.shadowGitPath}`);
      
      // Ensure the directory exists
      await fs.ensureDir(this.shadowGitPath);
      
      // Check if Git is already initialized
      const isRepo = await this.git.checkIsRepo();
      if (isRepo) {
        Logger.info(`✅ Shadow Git repository already exists for conversation: ${this.conversationId}`);
      } else {
        // Initialize new Git repository
        await this.git.init();
        Logger.info(`✅ Shadow Git repository initialized for conversation: ${this.conversationId}`);
      }
      
      // Configure Git user for commits
      try {
        await this.git.addConfig('user.name', 'Xyne Shadow Git');
        await this.git.addConfig('user.email', '<EMAIL>');
        Logger.info(`✅ Shadow Git user configured for conversation: ${this.conversationId}`);
      } catch (error) {
        Logger.warn(`⚠️ Could not configure Git user (may already be set): ${error}`);
      }
      
      Logger.info(`🎉 Shadow Git manager initialized successfully for conversation: ${this.conversationId}`);
    } catch (error) {
      Logger.error(error, `Failed to initialize shadow Git repository for conversation: ${this.conversationId}`);
      throw error;
    }
  }

  /**
   * Start a new checkpoint (user message)
   */
  startCheckpoint(checkpointId: string): void {
    this.currentCheckpointId = checkpointId;
    if (!this.allCheckpointIds.includes(checkpointId)) {
      this.allCheckpointIds.push(checkpointId);
    }
    Logger.info(`🔄 Starting new checkpoint: ${checkpointId} for conversation: ${this.conversationId}`);
    Logger.info(`🔍 [SHADOW_GIT_DEBUG] Current checkpoint files count: ${this.checkpointFiles.size}`);
    Logger.info(`🔍 [SHADOW_GIT_DEBUG] All checkpoint IDs: ${Array.from(this.checkpointFiles.keys()).join(', ')}`);
    Logger.info(`🔍 [SHADOW_GIT_DEBUG] All ordered checkpoint IDs: ${this.allCheckpointIds.join(', ')}`);
  }

  /**
   * Sync a file to shadow Git and track the change
   */
  async syncFile(
    filePath: string,
    relativePath: string,
    originalContent: string,
    newContent: string,
    changeId: string
  ): Promise<ShadowGitChange> {
    try {
      Logger.info(`📝 Syncing file to shadow Git: ${filePath} (changeId: ${changeId}) for conversation: ${this.conversationId}`);
      Logger.info(`📊 File stats - Original: ${originalContent.length} chars, New: ${newContent.length} chars`);
      
      // Store checkpoint-level file state (for restore functionality)
      if (this.currentCheckpointId) {
        // Check if we already have a file state for this file in this checkpoint
        const existingFileStates = this.checkpointFiles.get(this.currentCheckpointId) || [];
        const existingFileState = existingFileStates.find(fs => fs.relativePath === relativePath);
        
        if (existingFileState) {
          Logger.info(`🔍 [SHADOW_GIT_DEBUG] File ${relativePath} already has checkpoint state for ${this.currentCheckpointId}, skipping duplicate storage`);
          Logger.info(`🔍 [SHADOW_GIT_DEBUG] Existing content length: ${existingFileState.content.length} chars`);
        } else {
          const checkpointFileState: CheckpointFileState = {
            checkpointId: this.currentCheckpointId,
            filePath: filePath,
            relativePath: relativePath,
            content: originalContent, // Store original content for restore
            timestamp: Date.now()
          };
          
          if (!this.checkpointFiles.has(this.currentCheckpointId)) {
            this.checkpointFiles.set(this.currentCheckpointId, []);
            Logger.info(`🔍 [SHADOW_GIT_DEBUG] Created new checkpoint file array for: ${this.currentCheckpointId}`);
          }
          this.checkpointFiles.get(this.currentCheckpointId)!.push(checkpointFileState);
          Logger.info(`🔍 [SHADOW_GIT_DEBUG] Stored checkpoint file state for ${this.currentCheckpointId}: ${relativePath} (${originalContent.length} chars)`);
          Logger.info(`🔍 [SHADOW_GIT_DEBUG] Total files in checkpoint ${this.currentCheckpointId}: ${this.checkpointFiles.get(this.currentCheckpointId)!.length}`);
        }
      } else {
        Logger.warn(`🔍 [SHADOW_GIT_DEBUG] No current checkpoint ID set, skipping checkpoint file state storage`);
      }
      
      // Track original file content the first time this file is changed
      if (!this.originalFileContents.has(relativePath)) {
        this.originalFileContents.set(relativePath, originalContent);
        Logger.info(`🔍 [SHADOW_GIT_DEBUG] Stored original content for file: ${relativePath} (${originalContent.length} chars)`);
      }
      
      // Create the shadow file path (for file change approval/rejection)
      const shadowFilePath = path.join(this.shadowGitPath, relativePath);
      Logger.info(`🎭 Shadow file path: ${shadowFilePath}`);
      
      // Ensure the directory exists
      const shadowDir = path.dirname(shadowFilePath);
      await fs.ensureDir(shadowDir);
      Logger.info(`✅ Shadow directory ensured: ${shadowDir}`);
      
      // Write the NEW content to shadow file (for file change approval/rejection)
      // This maintains the existing file change approval/rejection functionality
      await fs.writeFile(shadowFilePath, newContent, 'utf8');
      Logger.info(`✅ New content written to shadow file: ${shadowFilePath}`);
      
      // Add to Git staging area
      await this.git.add(relativePath);
      Logger.info(`✅ File added to shadow Git staging: ${relativePath}`);
      
      // Generate diff summary
      const diffSummary = this.generateDiffSummary(originalContent, newContent);
      Logger.info(`📊 Diff summary: ${diffSummary}`);
      
      // Create change record
      const change: ShadowGitChange = {
        id: changeId,
        filePath: filePath,
        relativePath: relativePath,
        originalContent: originalContent,
        newContent: newContent,
        diffSummary: diffSummary,
        timestamp: Date.now(),
        status: 'pending',
        checkpointId: this.currentCheckpointId || undefined
      };
      
      // Store the change
      this.changes.set(changeId, change);
      Logger.info(`💾 Change stored with ID: ${changeId} for checkpoint: ${this.currentCheckpointId}`);
      Logger.info(`📊 Total tracked changes: ${this.changes.size}`);
      
      return change;
    } catch (error) {
      Logger.error(error, `Failed to sync file to shadow Git: ${filePath} for conversation: ${this.conversationId}`);
      throw error;
    }
  }

  /**
   * Commit all pending changes for the current checkpoint
   */
  async commitCheckpoint(checkpointId: string): Promise<string | null> {
    try {
      Logger.info(`📝 Committing checkpoint: ${checkpointId} for conversation: ${this.conversationId}`);
      
      // Check if there are any staged changes
      const status = await this.git.status();
      Logger.info(`🔍 [SHADOW_GIT_DEBUG] Git status before commit: ${JSON.stringify(status)}`);
      if (status.staged.length === 0) {
        Logger.info(`ℹ️ No staged changes to commit for checkpoint: ${checkpointId}`);
        return null;
      }
      
      // Commit all staged changes
      const commitMessage = `Checkpoint: ${checkpointId} - ${status.staged.length} file(s) changed`;
      const commitResult = await this.git.commit(commitMessage);
      
      // Extract just the commit hash (remove "HEAD " prefix if present)
      const commitHash = commitResult.commit.replace(/^HEAD\s+/, '');
      Logger.info(`✅ Checkpoint committed: ${commitHash} for conversation: ${this.conversationId}`);
      
      // Verify the commit exists in the repository
      try {
        const allLogs = await this.git.log();
        const commitExists = allLogs.all.some(commit => commit.hash === commitHash);
        if (!commitExists) {
          Logger.warn(`🔍 [SHADOW_GIT_DEBUG] Commit ${commitHash} not found in log after creation`);
        } else {
          Logger.info(`🔍 [SHADOW_GIT_DEBUG] Verified commit ${commitHash} exists in repository`);
        }
      } catch (error) {
        Logger.warn(`🔍 [SHADOW_GIT_DEBUG] Could not verify commit ${commitHash}: ${error}`);
      }
      
      // Store the commit hash for this checkpoint
      this.checkpointCommits.set(checkpointId, commitHash);
      
      // Update all changes in this checkpoint with the commit hash
      for (const change of this.changes.values()) {
        if (change.checkpointId === checkpointId) {
          change.commitHash = commitHash;
          change.status = 'accepted';
        }
      }
      
      Logger.info(`📊 Checkpoint ${checkpointId} committed with hash: ${commitHash}`);
      return commitHash;
    } catch (error) {
      Logger.error(error, `Failed to commit checkpoint: ${checkpointId} for conversation: ${this.conversationId}`);
      throw error;
    }
  }

  /**
   * Get the commit hash for a specific checkpoint
   */
  getCheckpointCommit(checkpointId: string): string | undefined {
    return this.checkpointCommits.get(checkpointId);
  }

  /**
   * Get all checkpoints with their commit hashes
   */
  getAllCheckpoints(): Map<string, string> {
    return new Map(this.checkpointCommits);
  }

  /**
   * Get diff for a specific change
   */
  async getDiff(changeId: string): Promise<string> {
    try {
      const change = this.changes.get(changeId);
      if (!change) {
        throw new Error(`Change not found: ${changeId}`);
      }

      // Generate diff between original and new content
      const diff = this.generateDiff(change.originalContent, change.newContent);
      Logger.info(`📊 Generated diff for change: ${changeId}`);
      return diff;
    } catch (error) {
      Logger.error(error, `Failed to get diff for change: ${changeId}`);
      throw error;
    }
  }

  /**
   * Accept a file change (commit to shadow Git)
   */
  async acceptChange(changeId: string): Promise<void> {
    try {
      Logger.info(`✅ Accepting change: ${changeId} for conversation: ${this.conversationId}`);
      
      const change = this.changes.get(changeId);
      if (!change) {
        Logger.error(`❌ Change not found: ${changeId}`);
        throw new Error(`Change not found: ${changeId}`);
      }
      
      Logger.info(`📝 Accepting change for file: ${change.filePath}`);
      
      // If this change is part of a checkpoint, commit the entire checkpoint
      if (change.checkpointId) {
        await this.commitCheckpoint(change.checkpointId);
      } else {
        // Individual change commit (legacy support)
        const commitResult = await this.git.commit(`LLM change accepted: ${change.relativePath} (${changeId})`);
        const commitHash = commitResult.commit.replace(/^HEAD\s+/, '');
        Logger.info(`✅ Change committed to shadow Git: ${commitHash}`);
        
        // Update change status
        change.status = 'accepted';
        change.commitHash = commitHash;
        this.changes.set(changeId, change);
      }
      
      Logger.info(`✅ Change ${changeId} marked as accepted for conversation: ${this.conversationId}`);
      
    } catch (error) {
      Logger.error(error, `Failed to accept change: ${changeId} for conversation: ${this.conversationId}`);
      throw error;
    }
  }

  /**
   * Reject a file change (revert file to original content or target content)
   */
  async rejectChange(changeId: string, targetContent?: string): Promise<void> {
    try {
      Logger.info(`❌ Rejecting change: ${changeId} for conversation: ${this.conversationId}`);
      
      const change = this.changes.get(changeId);
      if (!change) {
        Logger.error(`❌ Change not found: ${changeId}`);
        throw new Error(`Change not found: ${changeId}`);
      }
      
      Logger.info(`📝 Rejecting change for file: ${change.filePath}`);
      
      // Use target content if provided, otherwise use the change's original content
      const contentToRestore = targetContent || change.originalContent;
      Logger.info(`📝 Restoring file to ${targetContent ? 'target' : 'original'} content`);
      
      // Write content back to the workspace file
      const contentBuffer = Buffer.from(contentToRestore, 'utf8');
      await vscode.workspace.fs.writeFile(vscode.Uri.file(change.filePath), contentBuffer);
      Logger.info(`✅ Content restored to workspace file: ${change.filePath}`);
      
      // Remove from shadow Git staging (if not committed)
      try {
        await this.git.reset(['HEAD', change.relativePath]);
        Logger.info(`✅ File removed from shadow Git staging: ${change.relativePath}`);
      } catch (error) {
        Logger.warn(`⚠️ Could not remove from staging (may already be committed): ${error}`);
      }
      
      // Update change status
      change.status = 'rejected';
      this.changes.set(changeId, change);
      
      Logger.info(`✅ Change ${changeId} marked as rejected for conversation: ${this.conversationId}`);
      
    } catch (error) {
      Logger.error(error, `Failed to reject change: ${changeId} for conversation: ${this.conversationId}`);
      throw error;
    }
  }

  /**
   * Get a specific change by ID
   */
  getChange(changeId: string): ShadowGitChange | undefined {
    const change = this.changes.get(changeId);
    if (change) {
      Logger.info(`📋 Retrieved change: ${changeId} for file: ${change.filePath} (status: ${change.status})`);
    } else {
      Logger.warn(`⚠️ Change not found: ${changeId}`);
    }
    return change;
  }

  /**
   * Get all changes
   */
  getAllChanges(): ShadowGitChange[] {
    return Array.from(this.changes.values());
  }

  /**
   * Get all pending changes
   */
  getPendingChanges(): ShadowGitChange[] {
    return Array.from(this.changes.values()).filter(change => change.status === 'pending');
  }

  /**
   * Get changes for a specific checkpoint
   */
  getCheckpointChanges(checkpointId: string): ShadowGitChange[] {
    return Array.from(this.changes.values()).filter(change => change.checkpointId === checkpointId);
  }

  /**
   * Clear all changes (useful for cleanup)
   */
  clearChanges(): void {
    this.changes.clear();
    this.checkpointCommits.clear();
    this.currentCheckpointId = null;
    Logger.info(`🧹 Shadow Git changes cleared for conversation: ${this.conversationId}`);
  }

  /**
   * Generate a summary of the changes
   */
  private generateDiffSummary(originalContent: string, newContent: string): string {
    const originalLines = originalContent.split('\n').length;
    const newLines = newContent.split('\n').length;
    const addedLines = newLines - originalLines;
    
    let summary: string;
    if (addedLines > 0) {
      summary = `+${addedLines} lines added`;
    } else if (addedLines < 0) {
      summary = `${Math.abs(addedLines)} lines removed`;
    } else {
      summary = 'No line count change';
    }
    
    return summary;
  }

  /**
   * Generate a diff between two strings
   */
  private generateDiff(originalContent: string, newContent: string): string {
    // Simple diff implementation - can be enhanced with a proper diff library
    const originalLines = originalContent.split('\n');
    const newLines = newContent.split('\n');
    
    let diff = '';
    const maxLines = Math.max(originalLines.length, newLines.length);
    
    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[i] || '';
      const newLine = newLines[i] || '';
      
      if (originalLine !== newLine) {
        diff += `- ${originalLine}\n`;
        diff += `+ ${newLine}\n`;
      } else {
        diff += `  ${originalLine}\n`;
      }
    }
    
    return diff;
  }

  /**
   * Get the status of the shadow Git repository
   */
  async getStatus(): Promise<{
    initialized: boolean;
    path: string;
    changeCount: number;
    checkpointCount: number;
    conversationId: string;
  }> {
    try {
      const isRepo = await this.git.checkIsRepo();
      
      Logger.info(`📊 Shadow Git status for conversation: ${this.conversationId} - Initialized: ${isRepo}, Path: ${this.shadowGitPath}, Changes: ${this.changes.size}, Checkpoints: ${this.checkpointCommits.size}`);
      return {
        initialized: isRepo,
        path: this.shadowGitPath,
        changeCount: this.changes.size,
        checkpointCount: this.checkpointCommits.size,
        conversationId: this.conversationId
      };
    } catch (error) {
      Logger.error(error, `Failed to get shadow Git status for conversation: ${this.conversationId}`);
      return {
        initialized: false,
        path: this.shadowGitPath,
        changeCount: this.changes.size,
        checkpointCount: this.checkpointCommits.size,
        conversationId: this.conversationId
      };
    }
  }

  /**
   * Get the shadow Git path
   */
  getShadowGitPath(): string {
    return this.shadowGitPath;
  }

  /**
   * Get the conversation ID
   */
  getConversationId(): string {
    return this.conversationId;
  }

  /**
   * Get all checkpoint IDs for this conversation
   */
  getCheckpointIds(): string[] {
    const checkpointIds = Array.from(this.checkpointFiles.keys());
    Logger.info(`🔍 [SHADOW_GIT_DEBUG] Getting checkpoint IDs for conversation ${this.conversationId}: ${checkpointIds.join(', ')}`);
    Logger.info(`🔍 [SHADOW_GIT_DEBUG] Total checkpoints in map: ${this.checkpointFiles.size}`);
    return checkpointIds;
  }

  /**
   * Restore workspace to a specific checkpoint using cumulative checkpoint-level file states
   */
  async restoreToCheckpoint(checkpointId: string, workspaceRoot: string): Promise<void> {
    Logger.info(`🔄 Restoring workspace to checkpoint: ${checkpointId} for conversation: ${this.conversationId}`);
    Logger.info(`🔍 [SHADOW_GIT_DEBUG] Workspace root: ${workspaceRoot}`);
    Logger.info(`🔍 [SHADOW_GIT_DEBUG] Total checkpoints available: ${this.checkpointFiles.size}`);
    Logger.info(`🔍 [SHADOW_GIT_DEBUG] All checkpoint IDs: ${Array.from(this.checkpointFiles.keys()).join(', ')}`);
    Logger.info(`🔍 [SHADOW_GIT_DEBUG] All ordered checkpoint IDs: ${this.allCheckpointIds.join(', ')}`);

    try {
      // Use allCheckpointIds for ordering
      const allIds = this.allCheckpointIds;
      const targetIdx = allIds.indexOf(checkpointId);
      if (targetIdx === -1) {
        Logger.warn(`🔍 [SHADOW_GIT_DEBUG] Checkpoint ${checkpointId} not found in allCheckpointIds`);
        throw new Error(`Checkpoint not found: ${checkpointId}`);
      }

      // Gather all files that have ever been changed in any checkpoint
      const allChangedFiles = new Set<string>();
      for (const fileStates of this.checkpointFiles.values()) {
        for (const fs of fileStates) {
          allChangedFiles.add(fs.relativePath);
        }
      }

      // For each file, find the latest state <= selected checkpoint, or use original content if not yet changed
      const fileStateMap = new Map<string, CheckpointFileState>();
      for (const filePath of allChangedFiles) {
        let latestState: CheckpointFileState | null = null;
        for (let i = 0; i <= targetIdx; i++) {
          const cpId = allIds[i];
          const fileStates = this.checkpointFiles.get(cpId) || [];
          for (const fs of fileStates) {
            if (fs.relativePath === filePath) {
              latestState = fs;
            }
          }
        }
        if (latestState) {
          fileStateMap.set(filePath, latestState);
        } else {
          // File was never changed up to this checkpoint, but changed later: restore to original content
          const originalContent = this.originalFileContents.get(filePath) ?? '';
          fileStateMap.set(filePath, {
            checkpointId: checkpointId,
            filePath: filePath,
            relativePath: filePath,
            content: originalContent,
            timestamp: Date.now()
          });
        }
      }

      Logger.info(`🔍 [SHADOW_GIT_DEBUG] Cumulative restore: ${fileStateMap.size} files to restore as of checkpoint ${checkpointId}`);
      Logger.info(`🔍 [SHADOW_GIT_DEBUG] Files to restore: ${Array.from(fileStateMap.keys()).join(', ')}`);

      // Restore each file to its state as of the selected checkpoint
      for (const fileState of fileStateMap.values()) {
        const destPath = path.join(workspaceRoot, fileState.relativePath);
        const destDir = path.dirname(destPath);

        Logger.info(`🔍 [SHADOW_GIT_DEBUG] Restoring file: ${fileState.relativePath}`);
        Logger.info(`🔍 [SHADOW_GIT_DEBUG] Source content length: ${fileState.content.length} chars`);
        Logger.info(`🔍 [SHADOW_GIT_DEBUG] Destination path: ${destPath}`);
        Logger.info(`🔍 [SHADOW_GIT_DEBUG] Destination directory: ${destDir}`);

        // Ensure the directory exists
        await fs.ensureDir(destDir);
        Logger.info(`🔍 [SHADOW_GIT_DEBUG] Directory ensured: ${destDir}`);

        // Write the checkpoint content to the workspace file
        await fs.writeFile(destPath, fileState.content, 'utf8');
        Logger.info(`🔍 [SHADOW_GIT_DEBUG] File written successfully: ${destPath}`);
        Logger.info(`🔍 [SHADOW_GIT_DEBUG] File content (first 200 chars): ${fileState.content.substring(0, 200)}...`);

        // Verify the file was written correctly
        try {
          const writtenContent = await fs.readFile(destPath, 'utf8');
          Logger.info(`🔍 [SHADOW_GIT_DEBUG] Verification - written content length: ${writtenContent.length} chars`);
          if (writtenContent === fileState.content) {
            Logger.info(`🔍 [SHADOW_GIT_DEBUG] ✅ File content verification passed`);
          } else {
            Logger.warn(`🔍 [SHADOW_GIT_DEBUG] ⚠️ File content verification failed - content mismatch`);
          }
        } catch (error) {
          Logger.warn(`🔍 [SHADOW_GIT_DEBUG] Could not verify written file: ${error}`);
        }
      }

      Logger.info(`✅ Workspace restored to checkpoint: ${checkpointId} for conversation: ${this.conversationId}`);
    } catch (error) {
      Logger.error(error, `Failed to restore to checkpoint: ${checkpointId} for conversation: ${this.conversationId}`);
      throw error;
    }
  }

  /**
   * Checkout the entire shadow repo to a specific commit and copy all files to the workspace
   * @deprecated Use restoreToCheckpoint instead for checkpoint-level restoration
   */
  async restoreToCommit(commitHash: string, workspaceRoot: string): Promise<void> {
    Logger.info(`🔄 Restoring shadow Git repo to commit: ${commitHash} for conversation: ${this.conversationId}`);
    
    try {
      // First, check if the commit exists
      const allLogs = await this.git.log();
      const commitExists = allLogs.all.some(commit => commit.hash === commitHash);
      if (!commitExists) {
        Logger.warn(`🔍 [SHADOW_GIT_DEBUG] Commit ${commitHash} not found. Available commits: ${allLogs.all.map(c => c.hash).join(', ')}`);
        throw new Error(`Commit ${commitHash} not found in shadow Git repository`);
      }
      
      const foundCommit = allLogs.all.find(commit => commit.hash === commitHash);
      Logger.info(`🔍 [SHADOW_GIT_DEBUG] Found commit in log: ${JSON.stringify(foundCommit)}`);
      
      // Get current status before checkout
      const status = await this.git.status();
      Logger.info(`🔍 [SHADOW_GIT_DEBUG] Current status before checkout: ${JSON.stringify(status)}`);
      
      // Checkout the commit in the shadow repo
      await this.git.checkout(commitHash);
      Logger.info(`✅ Successfully checked out commit: ${commitHash}`);
      
      // Check what files are in the repository after checkout
      try {
        const statusAfterCheckout = await this.git.status();
        Logger.info(`🔍 [SHADOW_GIT_DEBUG] Status after checkout: ${JSON.stringify(statusAfterCheckout)}`);
        
        // List all files in the repository
        const lsResult = await this.git.raw(['ls-files']);
        Logger.info(`🔍 [SHADOW_GIT_DEBUG] Files in repository: ${lsResult}`);
      } catch (error) {
        Logger.warn(`🔍 [SHADOW_GIT_DEBUG] Could not check repository status: ${error}`);
      }
      
      // Copy all files from shadow repo to workspace
      const copyRecursive = async (srcDir: string, destDir: string) => {
        Logger.info(`🔍 [SHADOW_GIT_DEBUG] Copying from ${srcDir} to ${destDir}`);
        const entries = await fs.readdir(srcDir, { withFileTypes: true });
        Logger.info(`🔍 [SHADOW_GIT_DEBUG] Found ${entries.length} entries in ${srcDir}`);
        
        for (const entry of entries) {
          if (entry.name === '.git') continue; // Skip .git directory!
          const srcPath = path.join(srcDir, entry.name);
          const destPath = path.join(destDir, entry.name);
          
          if (entry.isDirectory()) {
            Logger.info(`🔍 [SHADOW_GIT_DEBUG] Creating directory: ${destPath}`);
            await fs.ensureDir(destPath);
            await copyRecursive(srcPath, destPath);
          } else if (entry.isFile()) {
            Logger.info(`🔍 [SHADOW_GIT_DEBUG] Copying file: ${srcPath} -> ${destPath}`);
            await fs.copyFile(srcPath, destPath);
            
            // Read and log the content being copied (first 200 chars)
            try {
              const content = await fs.readFile(srcPath, 'utf8');
              Logger.info(`🔍 [SHADOW_GIT_DEBUG] File content (first 200 chars): ${content.substring(0, 200)}...`);
            } catch (error) {
              Logger.warn(`🔍 [SHADOW_GIT_DEBUG] Could not read file content: ${error}`);
            }
          }
        }
      };
      
      await copyRecursive(this.shadowGitPath, workspaceRoot);
      Logger.info(`✅ Workspace restored to checkpoint: ${commitHash} for conversation: ${this.conversationId}`);
    } catch (error) {
      Logger.error(error, `Failed to restore to commit: ${commitHash} for conversation: ${this.conversationId}`);
      throw error;
    }
  }

  /**
   * Get the latest commit hash in the shadow git repo
   */
  async getLatestCommitHash(): Promise<string | null> {
    try {
      const log = await this.git.log({ maxCount: 1 });
      if (log && log.latest && log.latest.hash) {
        return log.latest.hash;
      }
      return null;
    } catch (error) {
      Logger.error(error, `Failed to get latest commit hash from shadow git for conversation: ${this.conversationId}`);
      return null;
    }
  }
}
import * as vscode from 'vscode';
import * as fs from 'fs/promises';
import * as path from 'path';
import { Conversation, Message, MessageType } from '../types/chat';

export class StorageMigration {
    constructor(private context: vscode.ExtensionContext) {}

    async migrateIfNeeded(): Promise<void> {
        try {
            const globalStoragePath = this.context.globalStorageUri.fsPath;
            const oldConversationsPath = path.join(globalStoragePath, 'xyne-conversations');
            const newConversationsPath = path.join(globalStoragePath, 'xyne-conversations');
            const indexPath = path.join(newConversationsPath, '_index.json');

            // Check if we already have the new index format
            try {
                await fs.access(indexPath);
                return; // Migration already done
            } catch {
                // No index file, continue with migration
            }

            // Check if old conversations exist
            try {
                await fs.access(oldConversationsPath);
            } catch {
                return; // No old conversations to migrate
            }

            console.log('Migrating Xyne conversations to new format...');
            await this.performMigration(oldConversationsPath);
            console.log('Migration completed successfully');

        } catch (error) {
            console.error('Migration failed:', error);
            // Don't throw - let the app continue with empty state
        }
    }

    private async performMigration(conversationsPath: string): Promise<void> {
        const files = await fs.readdir(conversationsPath);
        const conversationFiles = files.filter(f => f.endsWith('.json') && f !== '_index.json');

        for (const file of conversationFiles) {
            try {
                const filePath = path.join(conversationsPath, file);
                const content = await fs.readFile(filePath, 'utf8');
                const oldConversation = JSON.parse(content);

                // Convert old format to new format
                const newConversation: Conversation = {
                    id: oldConversation.id || this.generateId(),
                    title: oldConversation.title || 'Untitled Chat',
                    messages: this.convertMessages(oldConversation.messages || []),
                    createdAt: oldConversation.createdAt || Date.now(),
                    updatedAt: oldConversation.updatedAt || Date.now(),
                    metadata: {}
                };

                // Write back the converted conversation
                await fs.writeFile(filePath, JSON.stringify(newConversation, null, 2));

            } catch (error) {
                console.warn(`Failed to migrate conversation ${file}:`, error);
            }
        }
    }

    private convertMessages(oldMessages: any[]): Message[] {
        return oldMessages.map(msg => ({
            id: msg.id || this.generateId(),
            timestamp: msg.timestamp || Date.now(),
            type: this.convertMessageType(msg.type),
            content: msg.content || '',
            metadata: {
                model: msg.metadata?.model,
                tokens: msg.metadata?.tokens,
                cost: msg.metadata?.cost,
                responseTime: msg.metadata?.responseTime,
                error: msg.metadata?.error
            }
        }));
    }

    private convertMessageType(oldType: string): MessageType {
        switch (oldType) {
            case 'user':
                return MessageType.USER;
            case 'assistant':
                return MessageType.ASSISTANT;
            case 'system':
                return MessageType.SYSTEM;
            case 'error':
                return MessageType.ERROR;
            default:
                return MessageType.USER;
        }
    }

    private generateId(): string {
        return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
    }
}
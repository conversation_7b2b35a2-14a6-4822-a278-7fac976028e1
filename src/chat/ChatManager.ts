import * as vscode from 'vscode';
import { ChatStorage } from '../storage/ChatStorage';
import {
    Conversation,
    Message,
    ConversationSummary,
    MessageType,
    ExportOptions,
    MessageMetadata
} from '../types/chat';
import { getLogger, Subsystem, Logger as AppLogger } from '../logger';
import { SummarizationService, SummarizationProgress, SummarizationResult } from '../ai/summarization/SummarizationService';
import { Models, AIProviders } from '../ai/types';
import { ModelRegistry } from '../ai/models/ModelRegistry';

const Logger: AppLogger = getLogger(Subsystem.Chat);

export interface ChatManagerConfig {
    autoSave?: boolean;
    defaultModel?: Models;
    maxRetries?: number;
    typingDelay?: number;
    autoCompactEnabled?: boolean;
    autoCompactThreshold?: number; // 0.95 = 95% of context window
    maxContextTokens?: number; // Default context window size
    streamingTimeoutEnabled?: boolean;
    streamingTimeoutMs?: number; // Default 2 minutes (120000ms)
}

export class ChatManager {
    public storage: ChatStorage;
    private activeConversationId: string | null = null;
    private config: ChatManagerConfig;
    private conversationsChangedEmitter = new vscode.EventEmitter<ConversationSummary[]>();
    private messagesChangedEmitter = new vscode.EventEmitter<{ conversationId: string; messages: Message[] }>();
    private activeConversationChangedEmitter = new vscode.EventEmitter<string | null>();
    private typingChangedEmitter = new vscode.EventEmitter<boolean>();
    private errorEmitter = new vscode.EventEmitter<Error>();
    // Summarization events
    private summarizationProgressEmitter = new vscode.EventEmitter<{ conversationId: string; progress: SummarizationProgress }>();
    private summarizationCompleteEmitter = new vscode.EventEmitter<{ conversationId: string; result: SummarizationResult }>();
    
    // Add debounce timer for conversations changed events
    private conversationsChangedDebounceTimer: NodeJS.Timeout | null = null;
    
    // Summarization service
    private summarizationService: SummarizationService | null = null;

    public readonly onConversationsChanged: vscode.Event<ConversationSummary[]> = (listener, thisArgs, disposables) =>
        this.conversationsChangedEmitter.event(listener, thisArgs, disposables);
    public readonly onMessagesChanged: vscode.Event<{ conversationId: string; messages: Message[] }> = (
        listener,
        thisArgs,
        disposables,
    ) => this.messagesChangedEmitter.event(listener, thisArgs, disposables);
    public readonly onActiveConversationChanged: vscode.Event<string | null> = (listener, thisArgs, disposables) =>
        this.activeConversationChangedEmitter.event(listener, thisArgs, disposables);
    public readonly onTypingChanged: vscode.Event<boolean> = (listener, thisArgs, disposables) =>
        this.typingChangedEmitter.event(listener, thisArgs, disposables);
    public readonly onError: vscode.Event<Error> = (listener, thisArgs, disposables) =>
        this.errorEmitter.event(listener, thisArgs, disposables);
    // Summarization events
    public readonly onSummarizationProgress: vscode.Event<{ conversationId: string; progress: SummarizationProgress }> = (listener, thisArgs, disposables) =>
        this.summarizationProgressEmitter.event(listener, thisArgs, disposables);
    public readonly onSummarizationComplete: vscode.Event<{ conversationId: string; result: SummarizationResult }> = (listener, thisArgs, disposables) =>
        this.summarizationCompleteEmitter.event(listener, thisArgs, disposables);

    // Add AI Service integration
    private aiService: any; // We'll inject this from the extension

    constructor(
        context: vscode.ExtensionContext,
        config: ChatManagerConfig = {}
    ) {
        this.config = {
            autoSave: true,
            defaultModel: ModelRegistry.getDefaultModelForProvider(AIProviders.AwsBedrock) || Models.Claude_3_7_Sonnet,
            maxRetries: 3,
            typingDelay: 1000,
            autoCompactEnabled: true,
            autoCompactThreshold: 0.95,
            maxContextTokens: 200000,
            streamingTimeoutEnabled: true,
            streamingTimeoutMs: 120000, // 2 minutes
            ...config
        };

        this.storage = new ChatStorage(context, {
            maxConversations: 100,
            maxMessagesPerConversation: 1000,
            autoArchiveAfterDays: 90,
            backupEnabled: true
        });

        this.setupStorageEventHandlers();
        Logger.info('ChatManager constructed');
    }

    // Method to inject AI service
    setAIService(aiService: any): void {
        this.aiService = aiService;
        if (this.aiService) {
            this.summarizationService = new SummarizationService(aiService);
            Logger.info('SummarizationService initialized');
        }
    }

    // Method to update auto-compact settings
    updateAutoCompactSettings(autoCompactSettings: {
        enabled: boolean;
        threshold: number;
        maxContextTokens: number;
    }): void {
        this.config.autoCompactEnabled = autoCompactSettings.enabled;
        this.config.autoCompactThreshold = autoCompactSettings.threshold;
        this.config.maxContextTokens = autoCompactSettings.maxContextTokens;
        
        Logger.info(`Auto-compact settings updated: enabled=${autoCompactSettings.enabled}, threshold=${autoCompactSettings.threshold}, maxTokens=${autoCompactSettings.maxContextTokens}`);
    }

    // Method to update streaming timeout settings
    updateStreamingTimeoutSettings(settings: {
        enabled: boolean;
        timeoutMs: number;
    }): void {
        this.config.streamingTimeoutEnabled = settings.enabled;
        this.config.streamingTimeoutMs = settings.timeoutMs;
        
        Logger.info(`Streaming timeout settings updated: enabled=${settings.enabled}, timeoutMs=${settings.timeoutMs}`);
    }

    // Method to get current streaming timeout settings
    getStreamingTimeoutSettings(): {
        enabled: boolean;
        timeoutMs: number;
    } {
        return {
            enabled: this.config.streamingTimeoutEnabled || false,
            timeoutMs: this.config.streamingTimeoutMs || 120000, // 2 minutes default
        };
    }

    async initialize(): Promise<void> {
        Logger.info('Initializing ChatManager...');
        await this.storage.initialize();

        // Don't automatically set active conversation - let it be set explicitly
        // This prevents state pollution between tests

        // Emit initial state
        this.emitConversationsChanged();
        Logger.info('ChatManager initialized');
    }

    // Conversation Management
    async createConversation(title?: string): Promise<string> {
        const defaultTitle = title || `Chat ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`;
        Logger.info(`Creating new conversation with title: "${defaultTitle}"`);
        const conversation = await this.storage.createConversation(defaultTitle);

        this.activeConversationId = conversation.id;
        this.emitActiveConversationChanged();
        this.emitConversationsChanged(true);
        this.emitMessagesChanged(conversation.id);

        Logger.info(`Created and set active conversation: ${conversation.id}`);
        return conversation.id;
    }

    async switchConversation(conversationId: string | null): Promise<void> {
        if (this.activeConversationId === conversationId) {
            return;
        }

        Logger.info(`Switching to conversation: ${conversationId}`);

        if (conversationId === null) {
            this.activeConversationId = null;
            this.emitActiveConversationChanged();
            this.emitMessagesChanged(null);
            this.emitConversationsChanged(true);
            Logger.info(`Switched to null conversation`);
            return;
        }

        const conversation = await this.storage.loadConversation(conversationId);
        if (!conversation) {
            const err = new Error(`Conversation ${conversationId} not found`);
            Logger.error(err, 'Failed to switch conversation');
            this.errorEmitter.fire(err);
            return;
        }

        this.activeConversationId = conversationId;
        this.emitActiveConversationChanged();
        this.emitMessagesChanged(conversationId);
        this.emitConversationsChanged(true);
        Logger.info(`Switched to conversation: ${conversationId}`);
    }

    async deleteConversation(conversationId: string): Promise<void> {
        Logger.info(`Deleting conversation: ${conversationId}`);
        try {
            await this.storage.deleteConversation(conversationId);

            if (this.activeConversationId === conversationId) {
                const conversations = await this.getConversations();
                const newActiveId = conversations[0]?.id || null;
                await this.switchConversation(newActiveId);
                Logger.info(newActiveId ? `Active conversation was deleted, switched to new active conversation: ${newActiveId}` : 'Last conversation deleted, no active conversation');
            }
        } catch (error) {
            Logger.error(error, `Failed to delete conversation ${conversationId}`);
            this.errorEmitter.fire(new Error(`Failed to delete conversation: ${error}`));
        }
    }

    async renameConversation(conversationId: string, newTitle: string): Promise<void> {
        Logger.info(`Renaming conversation ${conversationId} to "${newTitle}"`);
        const conversation = await this.storage.loadConversation(conversationId);
        if (!conversation) {
            const err = new Error(`Conversation ${conversationId} not found`);
            Logger.error(err, 'Failed to rename conversation');
            this.errorEmitter.fire(err);
            return;
        }

        conversation.title = newTitle;
        conversation.updatedAt = Date.now();

        await this.storage.saveConversation(conversation);
        this.emitConversationsChanged();
        Logger.info(`Renamed conversation ${conversationId}`);
    }

    // Message Management with Auto-Compact
    async sendMessage(content: string, model?: string): Promise<void> {
        if (!content.trim()) {
            throw new Error('Message content cannot be empty');
        }
        Logger.info('sendMessage called');

        // Get or create active conversation
        let conversationId = this.activeConversationId;
        let isNewConversation = false;

        if (!conversationId) {
            Logger.info('No active conversation, creating a new one...');
            conversationId = await this.createConversation();
            isNewConversation = true;
        }

        // Create user message
        const userMessage: Message = {
            id: this.generateId(),
            timestamp: Date.now(),
            type: MessageType.USER,
            content: content.trim()
        };

        // Add user message
        Logger.info(`Adding user message to conversation ${conversationId}`);
        await this.storage.addMessage(conversationId, userMessage);
        this.emitMessagesChanged(conversationId);

        // If this was a new conversation, emit conversations changed immediately
        // so the UI updates the conversation list right away
        if (isNewConversation) {
            this.emitConversationsChanged(true);
        }

        // Show typing indicator
        this.emitTypingChanged(true);

        try {
            // Generate AI response
            Logger.info('Generating AI response...');
            const assistantMessage = await this.generateAIResponse(
                content,
                conversationId,
                model || this.config.defaultModel!
            );

            // Add assistant message
            Logger.info('Adding assistant message to conversation');
            await this.storage.addMessage(conversationId, assistantMessage);
            this.emitMessagesChanged(conversationId);
            this.emitConversationsChanged(true);

        } catch (error) {
            // Add error message
            Logger.error(error, 'Failed to send message or get AI response');
            const errorMessage: Message = {
                id: this.generateId(),
                timestamp: Date.now(),
                type: MessageType.ERROR,
                content: `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
                metadata: { error: error?.toString() }
            };

            await this.storage.addMessage(conversationId, errorMessage);
            this.emitMessagesChanged(conversationId);
            this.emitConversationsChanged(true);
            this.emitError(error as Error);
        } finally {
            // Hide typing indicator
            Logger.info('Hiding typing indicator');
            this.emitTypingChanged(false);
        }
    }

    // Auto-Compact Check
    private async checkAndAutoCompact(conversationId: string): Promise<void> {
        if (!this.config.autoCompactEnabled || !this.summarizationService) {
            return;
        }

        const conversation = await this.storage.loadConversation(conversationId);
        if (!conversation || conversation.messages.length < 1) {
            // Don't compact very short conversations
            return;
        }

        // Check if conversation needs compacting
        const shouldCompact = SummarizationService.shouldAutoCompact(
            conversation.messages,
            this.config.maxContextTokens!,
            this.config.autoCompactThreshold!
        );

        if (shouldCompact) {
            Logger.info(`Auto-compacting conversation ${conversationId} due to token threshold`);
            await this.summarizeConversation(conversationId);
        }
    }

    // Manual Summarization
    async summarizeConversation(conversationId: string): Promise<void> {
        if (!this.summarizationService) {
            throw new Error('Summarization service not available');
        }

        Logger.info(`Starting summarization for conversation: ${conversationId}`);

        const conversation = await this.storage.loadConversation(conversationId);
        if (!conversation) {
            throw new Error(`Conversation ${conversationId} not found`);
        }

        if (conversation.messages.length === 0) {
            throw new Error('No messages to summarize');
        }

        // Emit summarization started event via storage
        // The storage will emit the event which we'll listen to

        try {
            const result = await this.summarizationService.summarizeConversation({
                sessionId: conversationId,
                messages: conversation.messages,
                onProgress: (progress) => {
                    this.summarizationProgressEmitter.fire({ conversationId, progress });
                }
            });

            // Create summary message
            const summaryMessage: Message = {
                id: result.summaryMessageId,
                timestamp: Date.now(),
                type: MessageType.ASSISTANT,
                content: result.summaryText,
                metadata: {
                    isSummary: true,
                    summaryData: {
                        originalMessageCount: result.originalMessageCount,
                        summarizedAt: Date.now(),
                        tokensUsed: result.tokensUsed,
                        cost: result.cost
                    }
                }
            };

            // Replace conversation messages with summary
            await this.storage.summarizeConversation(conversationId, summaryMessage, result.cost);

            // Emit completion events
            this.summarizationCompleteEmitter.fire({ conversationId, result });

            // Update UI
            this.emitMessagesChanged(conversationId);
            this.emitConversationsChanged(true);

            Logger.info(`Summarization completed for conversation: ${conversationId}`);

        } catch (error) {
            Logger.error(error, `Summarization failed for conversation: ${conversationId}`);
            throw error;
        }
    }

    // Cancel active summarization
    async cancelSummarization(conversationId: string): Promise<boolean> {
        if (!this.summarizationService) {
            return false;
        }

        return this.summarizationService.cancelSummarization(conversationId);
    }


    // Check if conversation is being summarized
    isConversationBeingSummarized(conversationId: string): boolean {
        if (!this.summarizationService) {
            return false;
        }

        return this.summarizationService.isSessionBeingSummarized(conversationId);
    }

    // Get summarization status
    async getSummarizationStatus(conversationId: string): Promise<{
        isSummarized: boolean;
        summaryMessageId?: string;
        lastSummarizedAt?: number;
        summarizationCost?: number;
    }> {
        return await this.storage.getSummarizationStatus(conversationId);
    }

    async editMessage(conversationId: string, messageId: string, newContent: string): Promise<void> {
        Logger.info(`Editing message ${messageId} in conversation ${conversationId}`);
        const conversation = await this.storage.loadConversation(conversationId);
        if (!conversation) {
            throw new Error(`Conversation ${conversationId} not found`);
        }

        const messageIndex = conversation.messages.findIndex(m => m.id === messageId);
        if (messageIndex === -1) {
            throw new Error(`Message ${messageId} not found`);
        }

        conversation.messages[messageIndex].content = newContent;
        conversation.messages[messageIndex].metadata = {
            ...conversation.messages[messageIndex].metadata,
            edited: true,
            editedAt: Date.now()
        };
        conversation.updatedAt = Date.now();

        await this.storage.saveConversation(conversation);
        this.emitMessagesChanged(conversationId);
        Logger.info(`Edited message ${messageId}`);
    }

    async deleteMessage(conversationId: string, messageId: string): Promise<void> {
        Logger.info(`Deleting message ${messageId} from conversation ${conversationId}`);
        const conversation = await this.storage.loadConversation(conversationId);
        if (!conversation) {
            throw new Error(`Conversation ${conversationId} not found`);
        }

        conversation.messages = conversation.messages.filter(m => m.id !== messageId);
        conversation.updatedAt = Date.now();

        await this.storage.saveConversation(conversation);
        this.emitMessagesChanged(conversationId);
        this.emitConversationsChanged(true);
        Logger.info(`Deleted message ${messageId}`);
    }

    async addMessage(conversationId: string, message: Message): Promise<void> {
        Logger.info(`Adding message of type ${message.type} to conversation ${conversationId}`);
        await this.checkAndAutoCompact(conversationId);     
        await this.storage.addMessage(conversationId, message);       
        this.emitMessagesChanged(conversationId);
        this.emitConversationsChanged(true);
    }

    async addMessageAfter(conversationId: string, afterMessageId: string, message: Message): Promise<void> {
        await this.storage.addMessageAfter(conversationId, afterMessageId, message);
        this.emitMessagesChanged(conversationId);
        this.emitConversationsChanged(true);
    }

    // Data Access
    async getConversations(): Promise<ConversationSummary[]> {
        Logger.info('Getting all conversations');
        try {
            const conversations = await this.storage.listConversations();
            return conversations;
        } catch (error) {
            Logger.error(error, 'Failed to get conversations');
            this.errorEmitter.fire(new Error(`Failed to get conversations: ${error}`));
            return [];
        }
    }

    // Force refresh from storage (for when we need absolute latest data)
    async refreshFromStorage(): Promise<void> {
        Logger.info('Refreshing conversations from storage...');
        await this.storage.refreshIndex();
        this.emitConversationsChanged(true);
        Logger.info('Finished refreshing conversations from storage.');
    }

    async getActiveConversation(): Promise<Conversation | null> {
        Logger.info(`Getting active conversation: ${this.activeConversationId || 'None'}`);
        if (!this.activeConversationId) {
            return null;
        }
        return await this.storage.loadConversation(this.activeConversationId);
    }

    async getMessages(conversationId?: string): Promise<Message[]> {
        const id = conversationId || this.activeConversationId;
        Logger.info(`Getting messages for conversation: ${id || 'None'}`);
        if (!id) {
            return [];
        }

        const conversation = await this.storage.loadConversation(id);
        const messages = conversation?.messages || [];
        Logger.info(`Found ${messages.length} messages for conversation ${id}`);
        return messages;
    }

    getActiveConversationId(): string | null {
        Logger.info(`Getting active conversation ID: ${this.activeConversationId || 'None'}`);
        return this.activeConversationId;
    }

    setActiveConversation(conversationId: string | null): void {
        Logger.info(`Setting active conversation to: ${conversationId || 'None'}`);
        this.activeConversationId = conversationId;
        this.emitActiveConversationChanged();
    }

    clearActiveConversation(): void {
        Logger.info('Clearing active conversation');
        this.activeConversationId = null;
        this.emitActiveConversationChanged();
    }

    // Search and Export
    async searchConversations(query: string): Promise<ConversationSummary[]> {
        return await this.storage.searchConversations(query);
    }

    async exportConversation(
        conversationId: string,
        options: ExportOptions
    ): Promise<string> {
        return await this.storage.exportConversation(conversationId, options);
    }

    async exportActiveConversation(options: ExportOptions): Promise<string | null> {
        if (!this.activeConversationId) {
            return null;
        }
        return await this.exportConversation(this.activeConversationId, options);
    }

    // Cleanup and Maintenance
    async cleanup(): Promise<void> {
        // Clear debounce timer
        if (this.conversationsChangedDebounceTimer) {
            clearTimeout(this.conversationsChangedDebounceTimer);
            this.conversationsChangedDebounceTimer = null;
        }
        
        await this.storage.cleanup();
        this.emitConversationsChanged();
    }

    // AI Response Generation with Summary Context Handling
    private async generateAIResponse(
        userMessage: string,
        conversationId: string,
        model: string
    ): Promise<Message> {
        if (!this.aiService || !this.aiService.isAvailable()) {
            throw new Error('AI service is not available');
        }
        Logger.info(`Generating AI response for conversation ${conversationId} with model ${model}`);

        // Get conversation context with summary awareness
        const conversation = await this.storage.loadConversation(conversationId);
        let contextMessages = conversation?.messages || [];

        // If conversation has been summarized, use all messages from summary onwards
        const summarizationStatus = await this.storage.getSummarizationStatus(conversationId);
        if (summarizationStatus.isSummarized && summarizationStatus.summaryMessageId) {
            // Find summary message and use from there
            const summaryIndex = contextMessages.findIndex(m => m.id === summarizationStatus.summaryMessageId);
            if (summaryIndex !== -1) {
                contextMessages = contextMessages.slice(summaryIndex);
                Logger.info(`Using summarized context: ${contextMessages.length} messages from summary onwards`);
            }
        } else {
            // Use last 10 messages for context (original behavior)
            contextMessages = contextMessages.slice(-10);
            Logger.info(`Using last ${contextMessages.length} messages as context`);
        }

        // Build conversation history for AI
        const conversationHistory = contextMessages
            .filter(m => m.type !== MessageType.ERROR)
            .map(m => ({
                role: m.type === MessageType.USER ? 'user' : 'assistant',
                content: m.content
            }));

        // Add the current user message
        conversationHistory.push({
            role: 'user',
            content: userMessage
        });

        try {
            // Use the actual AI service to generate response
            // Convert conversation history to AWS SDK Message format
            Logger.info(`Sending ${conversationHistory.length} messages to AI service`);
            const awsMessages = conversationHistory.map(msg => ({
                role: msg.role as 'user' | 'assistant',
                content: [{ text: msg.content }]
            }));

            const response = await this.aiService.converse(awsMessages);
            Logger.info('Received AI response');

            return {
                id: this.generateId(),
                timestamp: Date.now(),
                type: MessageType.ASSISTANT,
                content: response.text || '',
                metadata: {
                    model,
                    tokens: response.usage?.inputTokens || 0,
                    cost: response.cost || 0,
                    responseTime: Date.now() - Date.now() // Will be calculated properly by the calling function
                }
            };
        } catch (error) {
            console.error('Error generating AI response:', error);
            Logger.error(error, 'Error generating AI response');
            throw new Error(`Failed to generate AI response: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    // Event Emitters
    private emitConversationsChanged(immediate: boolean = false): void {
        Logger.info(`Emitting conversations changed (immediate: ${immediate})`);
        
        // Clear existing debounce timer
        if (this.conversationsChangedDebounceTimer) {
            clearTimeout(this.conversationsChangedDebounceTimer);
            this.conversationsChangedDebounceTimer = null;
        }
        
        if (immediate) {
            // Synchronous emission for immediate UI updates (prevents race conditions)
            this.storage.listConversations().then(conversations => {
                try {
                    // Mark active conversation
                    const updatedConversations = conversations.map(conv => ({
                        ...conv,
                        isActive: conv.id === this.activeConversationId
                    }));

                    Logger.info(`Firing conversationsChanged event with ${updatedConversations.length} conversations`);
                    this.conversationsChangedEmitter.fire(updatedConversations);
                } catch (error) {
                    this.emitError(error as Error);
                }
            }).catch(error => {
                this.emitError(error as Error);
            });
        } else {
            // Debounce non-immediate emissions to prevent excessive events
            this.conversationsChangedDebounceTimer = setTimeout(async () => {
                try {
                    const conversations = await this.storage.listConversations();
                    Logger.info(`Firing conversationsChanged event with ${conversations.length} conversations`);
                    // Mark active conversation
                    const updatedConversations = conversations.map(conv => ({
                        ...conv,
                        isActive: conv.id === this.activeConversationId
                    }));

                    this.conversationsChangedEmitter.fire(updatedConversations);
                } catch (error) {
                    this.emitError(error as Error);
                }
            }, 50); // 50ms debounce
        }
    }

    public emitMessagesChanged(conversationId: string | null): void {
        if (conversationId) {
            this.storage.loadConversation(conversationId)
                .then(conversation => {
                    if (conversation) {
                        this.messagesChangedEmitter.fire({
                            conversationId,
                            messages: conversation.messages,
                        });
                    }
                })
                .catch(err => {
                    Logger.error(err, `Failed to load conversation for emitting message change: ${conversationId}`);
                    this.errorEmitter.fire(err);
                });
        } else {
            this.messagesChangedEmitter.fire({
                conversationId: '',
                messages: [],
            });
        }
    }

    private emitActiveConversationChanged(): void {
        Logger.info(`Emitting active conversation changed: ${this.activeConversationId || 'None'}`);
        this.activeConversationChangedEmitter.fire(this.activeConversationId);
    }

    private emitTypingChanged(isTyping: boolean): void {
        Logger.info(`Emitting typing changed: ${isTyping}`);
        this.typingChangedEmitter.fire(isTyping);
    }

    private emitError(error: Error): void {
        Logger.error(error, 'Emitting error');
        this.errorEmitter.fire(error);
    }

    // Storage Event Handlers
    private setupStorageEventHandlers(): void {
        this.storage.on('conversationCreated', (conversation) => {
            Logger.info(`Storage event: conversationCreated ${conversation.id}`);
            this.emitConversationsChanged(true);
        });

        this.storage.on('conversationDeleted', (conversationId) => {
            Logger.info(`Storage event: conversationDeleted ${conversationId}`);
            this.emitConversationsChanged(true);
        });

        this.storage.on('conversationUpdated', (conversation) => {
            Logger.info(`Storage event: conversationUpdated ${conversation.id}`);
            this.emitConversationsChanged();
        });

        this.storage.on('messageAdded', (conversationId) => {
            Logger.info(`Storage event: messageAdded in ${conversationId}`);
            if (conversationId === this.activeConversationId) {
                this.emitMessagesChanged(conversationId);
            }
            this.emitConversationsChanged(); // Also update summary
        });

        this.storage.on('storageError', (error) => {
            Logger.error(error, 'Storage event: storageError');
            this.emitError(error);
        });

        // Add summarization event handlers
        this.storage.on('summarizationStarted', (conversationId) => {
            Logger.info(`Storage event: summarizationStarted ${conversationId}`);
        });

        this.storage.on('summarizationProgress', (conversationId, progress) => {
            this.summarizationProgressEmitter.fire({ conversationId, progress: progress as SummarizationProgress });
        });

        this.storage.on('summarizationCompleted', (conversationId) => {
            Logger.info(`Storage event: summarizationCompleted ${conversationId}`);
            this.emitConversationsChanged();
        });

        this.storage.on('summarizationFailed', (conversationId, error) => {
            Logger.error(error, `Storage event: summarizationFailed ${conversationId}`);
            this.emitError(error);
        });
    }

    private generateId(): string {
        return Math.random().toString(36).substring(2, 11) + Date.now().toString(36);
    }


    // Cleanup
    dispose(): void {
        this.conversationsChangedEmitter.dispose();
        this.messagesChangedEmitter.dispose();
        this.activeConversationChangedEmitter.dispose();
        this.typingChangedEmitter.dispose();
        this.errorEmitter.dispose();
        this.summarizationProgressEmitter.dispose();
        this.summarizationCompleteEmitter.dispose();
    }

    /**
     * Update a message's metadata by id in the active conversation
     */
    async updateMessageMetadata(messageId: string, metadata: Partial<MessageMetadata>): Promise<void> {
        if (!this.activeConversationId) {
            return;
        }
        const conversation = await this.storage.loadConversation(this.activeConversationId);
        if (!conversation) {
            return;
        }
        const idx = conversation.messages.findIndex(m => String(m.id) === String(messageId));
        if (idx === -1) {
            return;
        }
        conversation.messages[idx].metadata = {
            ...conversation.messages[idx].metadata,
            ...metadata
        };
        conversation.updatedAt = Date.now();
        await this.storage.saveConversation(conversation);
        this.emitMessagesChanged(this.activeConversationId);
    }

    /**
     * Truncate the active conversation to a specific message index (inclusive)
     */
    async truncateConversationToIndex(idx: number): Promise<void> {
        Logger.info(`🔍 [CHAT_MANAGER_DEBUG] Truncating conversation to index: ${idx} (inclusive)`);
        
        if (!this.activeConversationId) {
            Logger.error(`🔍 [CHAT_MANAGER_DEBUG] No active conversation ID`);
            return;
        }
        
        Logger.info(`🔍 [CHAT_MANAGER_DEBUG] Active conversation ID: ${this.activeConversationId}`);
        
        const conversation = await this.storage.loadConversation(this.activeConversationId);
        if (!conversation) {
            Logger.error(`🔍 [CHAT_MANAGER_DEBUG] No conversation found for ID: ${this.activeConversationId}`);
            return;
        }
        
        Logger.info(`🔍 [CHAT_MANAGER_DEBUG] Original message count: ${conversation.messages.length}`);
        Logger.info(`🔍 [CHAT_MANAGER_DEBUG] Original message IDs: ${conversation.messages.map(m => m.id).join(', ')}`);
        
        // Keep messages up to and including the specified index (inclusive)
        // Handle case where idx is -1 (clear all messages)
        if (idx === -1) {
          conversation.messages = [];
        } else {
          conversation.messages = conversation.messages.slice(0, idx + 1);
        }
        conversation.updatedAt = Date.now();
        
        Logger.info(`🔍 [CHAT_MANAGER_DEBUG] Truncated message count: ${conversation.messages.length}`);
        Logger.info(`🔍 [CHAT_MANAGER_DEBUG] Truncated message IDs: ${conversation.messages.map(m => m.id).join(', ')}`);
        
        Logger.info(`🔍 [CHAT_MANAGER_DEBUG] Saving conversation to storage...`);
        await this.storage.saveConversation(conversation);
        Logger.info(`🔍 [CHAT_MANAGER_DEBUG] Conversation saved to storage`);
        
        Logger.info(`🔍 [CHAT_MANAGER_DEBUG] Emitting messagesChanged event...`);
        this.emitMessagesChanged(this.activeConversationId);
        Logger.info(`🔍 [CHAT_MANAGER_DEBUG] messagesChanged event emitted`);
        
        Logger.info(`🔍 [CHAT_MANAGER_DEBUG] Emitting conversationsChanged event...`);
        this.emitConversationsChanged(true);
        Logger.info(`🔍 [CHAT_MANAGER_DEBUG] conversationsChanged event emitted`);
    }

    /**
     * Start a new checkpoint for the current conversation
     * This should be called when a user message is sent
     */
    async startCheckpoint(checkpointId: string): Promise<void> {
        if (!this.activeConversationId) {
            Logger.warn('No active conversation for startCheckpoint');
            return;
        }
        
        Logger.info(`🔄 Starting checkpoint: ${checkpointId} for conversation: ${this.activeConversationId}`);
        
        // This method will be called by the extension when a user message is sent
        // The actual shadow Git checkpoint will be handled by the extension
    }

    /**
     * Commit the current checkpoint for the active conversation
     * This should be called after all file changes for a user message are processed
     */
    async commitCheckpoint(checkpointId: string): Promise<string | null> {
        if (!this.activeConversationId) {
            Logger.warn('No active conversation for commitCheckpoint');
            return null;
        }
        
        Logger.info(`📝 Committing checkpoint: ${checkpointId} for conversation: ${this.activeConversationId}`);
        
        // This method will be called by the extension after file changes are processed
        // The actual shadow Git commit will be handled by the extension
        return null;
    }

    /**
     * Get the checkpoint commit hash for a specific message
     */
    async getCheckpointCommit(messageId: string): Promise<string | undefined> {
        if (!this.activeConversationId) {
            return undefined;
        }
        
        const conversation = await this.storage.loadConversation(this.activeConversationId);
        if (!conversation) {
            return undefined;
        }
        
        const message = conversation.messages.find(m => String(m.id) === String(messageId));
        return message?.metadata?.checkpointCommit;
    }

    /**
     * Get all checkpoints for the active conversation
     */
    async getCheckpoints(): Promise<Map<string, string>> {
        if (!this.activeConversationId) {
            return new Map();
        }
        
        const conversation = await this.storage.loadConversation(this.activeConversationId);
        if (!conversation) {
            return new Map();
        }
        
        const checkpoints = new Map<string, string>();
        conversation.messages.forEach(message => {
            if (message.type === MessageType.USER && message.metadata?.checkpointCommit) {
                checkpoints.set(message.id, message.metadata.checkpointCommit);
            }
        });
        
        return checkpoints;
    }

    /**
     * Mark the current conversation as interrupted
     */
    async markConversationAsInterrupted(
        reason: 'user_stop' | 'error' | 'timeout' | 'manual_switch',
        partialResponseText?: string,
        lastMessageId?: string
    ): Promise<void> {
        if (!this.activeConversationId) {
            Logger.warn('No active conversation to mark as interrupted');
            return;
        }

        Logger.info(`🛑 Marking conversation ${this.activeConversationId} as interrupted: ${reason}`);

        const conversation = await this.storage.loadConversation(this.activeConversationId);
        if (!conversation) {
            Logger.error(`Conversation ${this.activeConversationId} not found when marking as interrupted`);
            return;
        }

        // Update conversation metadata
        conversation.metadata = {
            ...conversation.metadata,
            isInterrupted: true,
            interruptedAt: Date.now(),
            interruptionReason: reason,
            lastActiveMessageId: lastMessageId,
            interruptedResponseText: partialResponseText,
            canResume: reason === 'user_stop' || reason === 'manual_switch'
        };

        // If there's a partial response, update the last message with interrupted metadata
        if (partialResponseText && conversation.messages.length > 0) {
            const lastMessage = conversation.messages[conversation.messages.length - 1];
            if (lastMessage.type === MessageType.ASSISTANT) {
                lastMessage.metadata = {
                    ...lastMessage.metadata,
                    isInterrupted: true,
                    interruptedText: partialResponseText,
                    wasStreaming: true,
                    interruptedAt: Date.now(),
                    streamingProgress: partialResponseText.length / (partialResponseText.length + 100) // Approximate progress
                };
                
                // Update content with partial text if it's longer than current content
                if (partialResponseText.length > lastMessage.content.length) {
                    lastMessage.content = partialResponseText;
                }
            }
        }

        conversation.updatedAt = Date.now();
        await this.storage.saveConversation(conversation);
        
        // Emit conversation updated event
        this.emitConversationsChanged(true);
        
        Logger.info(`✅ Conversation ${this.activeConversationId} marked as interrupted`);
    }

    /**
     * Resume an interrupted conversation
     */
    async resumeInterruptedConversation(conversationId: string): Promise<void> {
        Logger.info(`🔄 Resuming interrupted conversation: ${conversationId}`);

        const conversation = await this.storage.loadConversation(conversationId);
        if (!conversation) {
            throw new Error(`Conversation ${conversationId} not found`);
        }

        if (!conversation.metadata?.isInterrupted) {
            Logger.warn(`Conversation ${conversationId} is not marked as interrupted`);
            return;
        }

        // Clear interruption flags
        conversation.metadata = {
            ...conversation.metadata,
            isInterrupted: false,
            canResume: false,
            // Keep the interruption history for reference
            lastInterruptedAt: conversation.metadata.interruptedAt,
            lastInterruptionReason: conversation.metadata.interruptionReason
        };

        conversation.updatedAt = Date.now();
        await this.storage.saveConversation(conversation);

        // Switch to this conversation if not already active
        if (this.activeConversationId !== conversationId) {
            await this.switchConversation(conversationId);
        }

        Logger.info(`✅ Conversation ${conversationId} resumed successfully`);
    }

    /**
     * Get all interrupted conversations
     */
    async getInterruptedConversations(): Promise<ConversationSummary[]> {
        const allConversations = await this.getConversations();
        return allConversations.filter(conv => conv.metadata?.isInterrupted);
    }

    /**
     * Check if a conversation can be resumed
     */
    async canResumeConversation(conversationId: string): Promise<boolean> {
        const conversation = await this.storage.loadConversation(conversationId);
        return conversation?.metadata?.canResume || false;
    }

    /**
     * Auto-save current conversation state (for preventing loss during interruptions)
     */
    async autoSaveCurrentConversation(): Promise<void> {
        if (!this.activeConversationId) return;

        try {
            const conversation = await this.storage.loadConversation(this.activeConversationId);
            if (conversation) {
                conversation.updatedAt = Date.now();
                await this.storage.saveConversation(conversation);
                Logger.info(`📱 Auto-saved conversation ${this.activeConversationId}`);
            }
        } catch (error) {
            Logger.error(error, `Failed to auto-save conversation ${this.activeConversationId}`);
        }
    }

    /**
     * Update conversation with partial streaming content
     */
    async updateStreamingContent(messageId: string, partialContent: string): Promise<void> {
        if (!this.activeConversationId) return;

        try {
            const conversation = await this.storage.loadConversation(this.activeConversationId);
            if (!conversation) return;

            const messageIndex = conversation.messages.findIndex(m => m.id === messageId);
            if (messageIndex === -1) return;

            // Update message content and metadata
            conversation.messages[messageIndex].content = partialContent;
            conversation.messages[messageIndex].metadata = {
                ...conversation.messages[messageIndex].metadata,
                wasStreaming: true,
                streamingProgress: Math.min(0.9, partialContent.length / 1000) // Approximate progress
            };

            conversation.updatedAt = Date.now();
            await this.storage.saveConversation(conversation);
        } catch (error) {
            Logger.error(error, `Failed to update streaming content for message ${messageId}`);
        }
    }
}

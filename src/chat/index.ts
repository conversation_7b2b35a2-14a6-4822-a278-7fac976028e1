import * as vscode from 'vscode';
import { XyneChatProvider } from './XyneChatProvider';
import { ChatManager } from './ChatManager';
import { StorageMigration } from '../utils/migration';

let chatProvider: XyneChatProvider | undefined;

export async function activateChat(context: vscode.ExtensionContext): Promise<XyneChatProvider> {
    try {
        // Run migration if needed
        const migration = new StorageMigration(context);
        await migration.migrateIfNeeded();

        // Create and register chat provider
        chatProvider = new XyneChatProvider(context);

        // Register webview provider
        const webviewProvider = vscode.window.registerWebviewViewProvider(
            'xyne.chatView',
            chatProvider,
            {
                webviewOptions: {
                    retainContextWhenHidden: true
                }
            }
        );

        // Register commands
        const focusChatCommand = vscode.commands.registerCommand(
            'xyne.focusChat',
            () => chatProvider?.focusChat()
        );

        const newChatCommand = vscode.commands.registerCommand(
            'xyne.newChat',
            () => chatProvider?.createNewConversation()
        );

        const exportChatCommand = vscode.commands.registerCommand(
            'xyne.exportChat',
            () => chatProvider?.exportActiveConversation('markdown')
        );

        // Add to context subscriptions for cleanup
        context.subscriptions.push(
            webviewProvider,
            focusChatCommand,
            newChatCommand,
            exportChatCommand
        );

        console.log('Xyne Chat activated successfully');
        return chatProvider;

    } catch (error) {
        console.error('Failed to activate Xyne Chat:', error);
        vscode.window.showErrorMessage(`Failed to activate Xyne Chat: ${error}`);
        throw error;
    }
}

export function deactivateChat(): void {
    if (chatProvider) {
        chatProvider.dispose();
        chatProvider = undefined;
    }
    console.log('Xyne Chat deactivated');
}

export function getChatProvider(): XyneChatProvider | undefined {
    return chatProvider;
}

// Export types for external use
export * from '../types/chat';
export { ChatManager } from './ChatManager';
export { XyneChatProvider } from './XyneChatProvider';
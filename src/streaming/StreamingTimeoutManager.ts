import { getLogger, Subsystem } from '../logger';
import { Message, MessageType, MessageMetadata } from '../types/chat';

const Logger = getLogger(Subsystem.Extension);

export interface TimeoutSettings {
  enabled: boolean;
  timeoutMs: number;
}

export interface TimeoutInfo {
  durationMs: number;
  durationMinutes: number;
  message: Message;
}

export interface TimeoutCallbacks {
  onTimeout: (timeoutInfo: TimeoutInfo) => Promise<void>;
  onResume?: () => void;
  onStop?: () => void;
}

/**
 * Manages streaming timeouts with a clean, modular interface
 * Handles timer state, timeout detection, and message creation
 */
export class StreamingTimeoutManager {
  private timer: NodeJS.Timeout | null = null;
  private startTime: number | null = null;
  private isPaused: boolean = false;
  private settings: TimeoutSettings | null = null;
  private callbacks: TimeoutCallbacks | null = null;

  constructor() {
    Logger.info('StreamingTimeoutManager initialized');
  }

  /**
   * Start the timeout timer with given settings and callbacks
   */
  start(settings: TimeoutSettings, callbacks: TimeoutCallbacks): void {
    if (!settings.enabled) {
      Logger.info('Streaming timeout disabled, not starting timer');
      return;
    }

    this.stop(); // Clear any existing timer
    this.settings = settings;
    this.callbacks = callbacks;
    this.startTime = Date.now();
    this.isPaused = false;

    this.timer = setTimeout(() => {
      this.handleTimeout();
    }, settings.timeoutMs);

    Logger.info(`⏱️ Streaming timeout started: ${settings.timeoutMs}ms`);
  }

  /**
   * Stop the timeout timer and clear state
   */
  stop(): void {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
      Logger.info('⏹️ Streaming timeout stopped');
    }

    this.startTime = null;
    this.isPaused = false;
    this.settings = null;
    
    if (this.callbacks?.onStop) {
      this.callbacks.onStop();
    }
    this.callbacks = null;
  }

  /**
   * Pause the current timeout (used when timeout is triggered)
   */
  pause(): void {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    this.isPaused = true;
    Logger.info('⏸️ Streaming timeout paused');
  }

  /**
   * Resume from paused state and restart timer
   */
  resume(): void {
    if (!this.isPaused || !this.settings || !this.callbacks) {
      Logger.warn('Cannot resume: not in paused state or missing settings/callbacks');
      return;
    }

    this.isPaused = false;
    this.startTime = Date.now(); // Reset start time for new timeout period

    this.timer = setTimeout(() => {
      this.handleTimeout();
    }, this.settings.timeoutMs);

    Logger.info('▶️ Streaming timeout resumed');
    
    if (this.callbacks.onResume) {
      this.callbacks.onResume();
    }
  }

  /**
   * Check if timeout is currently active (timer running)
   */
  isActive(): boolean {
    return this.timer !== null;
  }

  /**
   * Check if timeout is in paused state
   */
  isPausedState(): boolean {
    return this.isPaused;
  }

  /**
   * Get current timeout settings
   */
  getSettings(): TimeoutSettings | null {
    return this.settings;
  }

  /**
   * Get elapsed time since start
   */
  getElapsedTime(): number {
    if (!this.startTime) return 0;
    return Date.now() - this.startTime;
  }

  /**
   * Handle timeout event - create message and notify callback
   */
  private async handleTimeout(): Promise<void> {
    if (!this.settings || !this.callbacks) {
      Logger.warn('Timeout triggered but missing settings or callbacks');
      return;
    }

    Logger.info('⏱️ Streaming timeout triggered');
    
    this.pause(); // Pause the timer
    
    const timeoutInfo = this.createTimeoutInfo();
    
    try {
      await this.callbacks.onTimeout(timeoutInfo);
      Logger.info('Timeout callback completed successfully');
    } catch (error) {
      Logger.error(error, 'Error in timeout callback');
    }
  }

  /**
   * Create timeout information including the message
   */
  private createTimeoutInfo(): TimeoutInfo {
    if (!this.settings) {
      throw new Error('Cannot create timeout info without settings');
    }

    const durationMs = this.settings.timeoutMs;
    const durationMinutes = Math.round(durationMs / 60000);

    const message: Message = {
      id: `timeout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      type: MessageType.SYSTEM,
      content: this.createTimeoutMessage(durationMinutes),
      metadata: {
        isStreamingTimeout: true,
        timeoutDurationMs: durationMs
      } as MessageMetadata
    };

    return {
      durationMs,
      durationMinutes,
      message
    };
  }

  /**
   * Create the timeout message content
   */
  private createTimeoutMessage(durationMinutes: number): string {
    return `⏱️ **Streaming Timeout**

I've been processing for ${durationMinutes} minutes. To prevent excessive resource usage, I'm pausing here.

Click the **Continue** button below to resume, or send a new message to redirect the conversation.`;
  }

  /**
   * Cleanup resources
   */
  dispose(): void {
    this.stop();
    Logger.info('StreamingTimeoutManager disposed');
  }
}
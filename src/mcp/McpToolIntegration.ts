import { Mc<PERSON><PERSON><PERSON>, Mc<PERSON>Tool } from './McpHub';
import { getLogger, Subsystem } from '../logger';

const Logger = getLogger(Subsystem.Extension);

export interface McpToolCall {
  serverName: string;
  toolName: string;
  parameters: any;
}

export interface McpToolResult {
  success: boolean;
  result?: any;
  error?: string;
}

/**
 * Service to integrate MCP tools with the AI chat system
 */
export class McpToolIntegration {
  private mcpHub: McpHub;

  constructor(mcpHub: McpHub) {
    this.mcpHub = mcpHub;
  }

  /**
   * Get all available MCP tools formatted for AI system
   */
  getAvailableTools(): Array<{
    name: string;
    description: string;
    parameters: any;
    serverName: string;
  }> {
    const tools: Array<{
      name: string;
      description: string;
      parameters: any;
      serverName: string;
    }> = [];

    for (const tool of this.mcpHub.getAllTools()) {
      tools.push({
        name: `mcp_${tool.serverName}_${tool.name}`,
        description: `[MCP:${tool.serverName}] ${tool.description || tool.name}`,
        parameters: tool.inputSchema || {},
        serverName: tool.serverName
      });
    }

    return tools;
  }

  /**
   * Check if a tool name is an MCP tool
   */
  isMcpTool(toolName: string): boolean {
    return toolName.startsWith('mcp_');
  }

  /**
   * Parse MCP tool name to extract server and tool name
   */
  parseMcpToolName(toolName: string): { serverName: string; toolName: string } | null {
    if (!this.isMcpTool(toolName)) {
      return null;
    }

    // Format: mcp_<serverName>_<toolName>
    const parts = toolName.split('_');
    if (parts.length < 3) {
      return null;
    }

    const serverName = parts[1];
    const actualToolName = parts.slice(2).join('_');

    return { serverName, toolName: actualToolName };
  }

  /**
   * Execute an MCP tool
   */
  async executeMcpTool(toolName: string, parameters: any): Promise<McpToolResult> {
    try {
      const parsed = this.parseMcpToolName(toolName);
      if (!parsed) {
        return {
          success: false,
          error: `Invalid MCP tool name: ${toolName}`
        };
      }

      const { serverName, toolName: actualToolName } = parsed;

      Logger.info(`Executing MCP tool: ${actualToolName} on server: ${serverName}`);

      const result = await this.mcpHub.callTool(serverName, actualToolName, parameters);

      return {
        success: true,
        result
      };
    } catch (error) {
      Logger.error(error, `Error executing MCP tool: ${toolName}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Get tool schema for AI system
   */
  getToolSchema(): any {
    const tools = this.getAvailableTools();
    
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters
      }
    }));
  }

  /**
   * Format MCP tool results for display in chat
   */
  formatToolResult(toolName: string, result: McpToolResult): string {
    const parsed = this.parseMcpToolName(toolName);
    const serverName = parsed?.serverName || 'unknown';
    const actualToolName = parsed?.toolName || toolName;

    if (!result.success) {
      return `❌ **MCP Tool Error** (${serverName}/${actualToolName}): ${result.error}`;
    }

    if (result.result?.content) {
      // Handle structured MCP response
      let formattedContent = '';
      for (const content of result.result.content) {
        if (content.type === 'text') {
          formattedContent += content.text;
        } else if (content.type === 'image') {
          formattedContent += `[Image: ${content.data?.substring(0, 50)}...]`;
        } else if (content.type === 'resource') {
          formattedContent += `[Resource: ${content.resource?.uri}]`;
        }
      }
      
      return `✅ **MCP Tool Result** (${serverName}/${actualToolName}):\n\n${formattedContent}`;
    }

    // Fallback for simple results
    const resultText = typeof result.result === 'string' 
      ? result.result 
      : JSON.stringify(result.result, null, 2);

    return `✅ **MCP Tool Result** (${serverName}/${actualToolName}):\n\n\`\`\`\n${resultText}\n\`\`\``;
  }

  /**
   * Get MCP server status summary
   */
  getServerStatusSummary(): string {
    const servers = this.mcpHub.getServers();
    const connected = servers.filter(s => s.status === 'connected').length;
    const total = servers.length;
    
    if (total === 0) {
      return '🔌 No MCP servers configured';
    }

    return `🔌 MCP Servers: ${connected}/${total} connected`;
  }
}
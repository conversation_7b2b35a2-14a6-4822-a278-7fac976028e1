import * as vscode from 'vscode';
import { getLogger, Subsystem } from '../logger';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import { 
  CallToolResultSchema,
  ListResourcesResultSchema,
  ListToolsResultSchema,
  ReadResourceResultSchema
} from '@modelcontextprotocol/sdk/types.js';

const Logger = getLogger(Subsystem.Extension);

export interface McpServerConfig {
  name: string;
  type: 'stdio' | 'sse' | 'http';
  command?: string;
  args?: string[];
  url?: string;
  env?: Record<string, { value: string; isSecret: boolean }>;
  disabled?: boolean;
  autoApprove?: string[];
  timeout?: number;
}

export interface McpServer {
  name: string;
  config: McpServerConfig;
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
  tools?: McpTool[];
  resources?: McpResource[];
  lastError?: string;
  connected?: boolean;
}

export interface McpTool {
  name: string;
  description?: string;
  inputSchema?: any;
  serverName: string;
}

export interface McpResource {
  uri: string;
  name?: string;
  description?: string;
  serverName: string;
}

export interface McpConnection {
  server: McpServer;
  client: Client;
  transport: StdioClientTransport | SSEClientTransport | StreamableHTTPClientTransport;
}

export class McpHub {
  private connections: Map<string, McpConnection> = new Map();
  private context: vscode.ExtensionContext;
  private onServersChangedEmitter = new vscode.EventEmitter<McpServer[]>();
  readonly onServersChanged = this.onServersChangedEmitter.event;

  constructor(context: vscode.ExtensionContext) {
    this.context = context;
    this.loadServersFromConfig();
  }

  /**
   * Get the secret storage key for an environment variable
   */
  private getSecretKey(serverName: string, envKey: string): string {
    return `xyne.mcp.${serverName}.env.${envKey}`;
  }

  /**
   * Store a secret environment variable
   */
  private async storeSecret(serverName: string, envKey: string, value: string): Promise<void> {
    const secretKey = this.getSecretKey(serverName, envKey);
    await this.context.secrets.store(secretKey, value);
    Logger.info(`Stored secret environment variable: ${envKey} for server: ${serverName}`);
  }

  /**
   * Retrieve a secret environment variable
   */
  private async getSecret(serverName: string, envKey: string): Promise<string | undefined> {
    const secretKey = this.getSecretKey(serverName, envKey);
    return await this.context.secrets.get(secretKey);
  }

  /**
   * Delete a secret environment variable
   */
  private async deleteSecret(serverName: string, envKey: string): Promise<void> {
    const secretKey = this.getSecretKey(serverName, envKey);
    await this.context.secrets.delete(secretKey);
    Logger.info(`Deleted secret environment variable: ${envKey} for server: ${serverName}`);
  }

  /**
   * Resolve environment variables, fetching secrets as needed
   */
  private async resolveEnvironmentVariables(serverConfig: McpServerConfig): Promise<Record<string, string>> {
    const resolvedEnv: Record<string, string> = {};
    
    if (serverConfig.env) {
      for (const [key, envConfig] of Object.entries(serverConfig.env)) {
        if (envConfig.isSecret) {
          const secretValue = await this.getSecret(serverConfig.name, key);
          if (secretValue !== undefined) {
            resolvedEnv[key] = secretValue;
          } else {
            Logger.warn(`Secret environment variable ${key} not found for server ${serverConfig.name}`);
          }
        } else {
          resolvedEnv[key] = envConfig.value;
        }
      }
    }
    
    return resolvedEnv;
  }

  /**
   * Get all configured MCP servers
   */
  public getServers(): McpServer[] {
    return Array.from(this.connections.values()).map(conn => conn.server);
  }

  /**
   * Get connected MCP servers
   */
  public getConnectedServers(): McpServer[] {
    return this.getServers().filter(server => server.status === 'connected');
  }

  /**
   * Get all available tools from connected servers
   */
  public getAllTools(): McpTool[] {
    const tools: McpTool[] = [];
    for (const server of this.getConnectedServers()) {
      if (server.tools) {
        tools.push(...server.tools);
      }
    }
    return tools;
  }

  /**
   * Get all available resources from connected servers
   */
  public getAllResources(): McpResource[] {
    const resources: McpResource[] = [];
    for (const server of this.getConnectedServers()) {
      if (server.resources) {
        resources.push(...server.resources);
      }
    }
    return resources;
  }

  /**
   * Add a new MCP server
   */
  public async addServer(config: McpServerConfig): Promise<void> {
    Logger.info(`Adding MCP server: ${config.name}`);
    
    if (this.connections.has(config.name)) {
      throw new Error(`Server ${config.name} already exists`);
    }

    // Store secret environment variables
    if (config.env) {
      for (const [key, envConfig] of Object.entries(config.env)) {
        if (envConfig.isSecret) {
          await this.storeSecret(config.name, key, envConfig.value);
        }
      }
    }

    const server: McpServer = {
      name: config.name,
      config,
      status: 'disconnected',
      tools: [],
      resources: []
    };

    this.connections.set(config.name, { server });
    await this.saveServersToConfig();
    this.onServersChangedEmitter.fire(this.getServers());

    if (!config.disabled) {
      await this.connectToServer(config.name);
    }
  }

  /**
   * Remove an MCP server
   */
  public async removeServer(serverName: string): Promise<void> {
    Logger.info(`Removing MCP server: ${serverName}`);
    
    const connection = this.connections.get(serverName);
    if (connection) {
      // Clean up secret environment variables
      if (connection.server.config.env) {
        for (const [key, envConfig] of Object.entries(connection.server.config.env)) {
          if (envConfig.isSecret) {
            await this.deleteSecret(serverName, key);
          }
        }
      }

      await this.disconnectFromServer(serverName);
      this.connections.delete(serverName);
      await this.saveServersToConfig();
      this.onServersChangedEmitter.fire(this.getServers());
    }
  }

  /**
   * Connect to an MCP server
   */
  public async connectToServer(serverName: string): Promise<void> {
    const connection = this.connections.get(serverName);
    if (!connection) {
      throw new Error(`Server ${serverName} not found`);
    }

    const { server } = connection;
    if (server.status === 'connected' || server.status === 'connecting') {
      return;
    }

    Logger.info(`Connecting to MCP server: ${serverName}`);
    server.status = 'connecting';
    this.onServersChangedEmitter.fire(this.getServers());

    try {
      // Create MCP client
      const client = new Client(
        {
          name: 'Xyne',
          version: '1.0.0'
        },
        {
          capabilities: {}
        }
      );

      let transport: StdioClientTransport | SSEClientTransport | StreamableHTTPClientTransport;

      if (server.config.type === 'stdio') {
        if (!server.config.command) {
          throw new Error('Command is required for stdio servers');
        }
        
        // Resolve environment variables (including secrets)
        const resolvedEnv = await this.resolveEnvironmentVariables(server.config);
        
        transport = new StdioClientTransport({
          command: server.config.command,
          args: server.config.args || [],
          env: resolvedEnv
        });
      } else if (server.config.type === 'sse') {
        if (!server.config.url) {
          throw new Error('URL is required for SSE servers');
        }
        
        transport = new SSEClientTransport(new URL(server.config.url));
      } else if (server.config.type === 'http') {
        if (!server.config.url) {
          throw new Error('URL is required for HTTP servers');
        }
        
        // Build headers from environment variables
        const headers: Record<string, string> = {};
        const resolvedEnv = await this.resolveEnvironmentVariables(server.config);
        
        // Convert environment variables to HTTP headers
        for (const [key, value] of Object.entries(resolvedEnv)) {
          headers[key] = value;
        }
        
        transport = new StreamableHTTPClientTransport(new URL(server.config.url), {
          requestInit: {
            headers: headers
          }
        });
      } else {
        throw new Error(`Unsupported transport type: ${server.config.type}`);
      }

      // Connect to the MCP server
      await client.connect(transport);

      // Update connection with client and transport
      connection.client = client;
      connection.transport = transport;

      // Fetch available tools and resources
      await this.fetchServerCapabilities(connection);

      server.status = 'connected';
      server.connected = true;
      server.lastError = undefined;
      Logger.info(`Successfully connected to MCP server: ${serverName}`);
    } catch (error) {
      server.status = 'error';
      server.lastError = error instanceof Error ? error.message : 'Unknown error';
      server.connected = false;
      Logger.error(error, `Failed to connect to MCP server: ${serverName}`);
    }

    this.onServersChangedEmitter.fire(this.getServers());
  }

  /**
   * Disconnect from an MCP server
   */
  public async disconnectFromServer(serverName: string): Promise<void> {
    const connection = this.connections.get(serverName);
    if (!connection) {
      return;
    }

    Logger.info(`Disconnecting from MCP server: ${serverName}`);
    
    try {
      if (connection.client) {
        await connection.client.close();
      }
      if (connection.transport) {
        await connection.transport.close();
      }
    } catch (error) {
      Logger.warn(`Error closing MCP connection: ${error}`);
    }

    connection.server.status = 'disconnected';
    connection.server.connected = false;
    connection.server.tools = [];
    connection.server.resources = [];

    this.onServersChangedEmitter.fire(this.getServers());
  }

  /**
   * Toggle server enabled/disabled state
   */
  public async toggleServer(serverName: string): Promise<void> {
    const connection = this.connections.get(serverName);
    if (!connection) {
      throw new Error(`Server ${serverName} not found`);
    }

    const { server } = connection;
    server.config.disabled = !server.config.disabled;

    if (server.config.disabled) {
      await this.disconnectFromServer(serverName);
    } else {
      await this.connectToServer(serverName);
    }

    await this.saveServersToConfig();
  }

  /**
   * Call a tool on an MCP server
   */
  public async callTool(serverName: string, toolName: string, args: any): Promise<any> {
    const connection = this.connections.get(serverName);
    if (!connection || connection.server.status !== 'connected') {
      throw new Error(`Server ${serverName} is not connected`);
    }

    if (!connection.client) {
      throw new Error(`No client available for server ${serverName}`);
    }

    Logger.info(`Calling MCP tool: ${toolName} on server: ${serverName}`);
    
    try {
      const result = await connection.client.request(
        {
          method: 'tools/call',
          params: {
            name: toolName,
            arguments: args
          }
        },
        CallToolResultSchema
      );

      return result;
    } catch (error) {
      Logger.error(error, `Error calling tool ${toolName} on server ${serverName}`);
      throw error;
    }
  }

  /**
   * Read a resource from an MCP server
   */
  public async readResource(serverName: string, uri: string): Promise<any> {
    const connection = this.connections.get(serverName);
    if (!connection || connection.server.status !== 'connected') {
      throw new Error(`Server ${serverName} is not connected`);
    }

    if (!connection.client) {
      throw new Error(`No client available for server ${serverName}`);
    }

    Logger.info(`Reading MCP resource: ${uri} from server: ${serverName}`);
    
    try {
      const result = await connection.client.request(
        {
          method: 'resources/read',
          params: { uri }
        },
        ReadResourceResultSchema
      );

      return result;
    } catch (error) {
      Logger.error(error, `Error reading resource ${uri} from server ${serverName}`);
      throw error;
    }
  }

  /**
   * Restart an MCP server
   */
  public async restartServer(serverName: string): Promise<void> {
    await this.disconnectFromServer(serverName);
    await this.connectToServer(serverName);
  }

  /**
   * Load servers from VS Code configuration
   */
  private async loadServersFromConfig(): Promise<void> {
    try {
      const config = vscode.workspace.getConfiguration('xyne');
      const mcpServers = config.get<Record<string, McpServerConfig>>('mcpServers', {});

      for (const [name, serverConfig] of Object.entries(mcpServers)) {
        const server: McpServer = {
          name,
          config: { name, ...serverConfig },
          status: 'disconnected',
          tools: [],
          resources: []
        };

        this.connections.set(name, { server });
      }

      Logger.info(`Loaded ${Object.keys(mcpServers).length} MCP servers from configuration`);
      
      // Auto-connect enabled servers
      for (const [name, serverConfig] of Object.entries(mcpServers)) {
        if (!serverConfig.disabled) {
          Logger.info(`Auto-connecting to MCP server: ${name}`);
          await this.connectToServer(name);
        }
      }
    } catch (error) {
      Logger.error(error, 'Failed to load MCP servers from configuration');
    }
  }

  /**
   * Save servers to VS Code configuration
   */
  private async saveServersToConfig(): Promise<void> {
    try {
      const config = vscode.workspace.getConfiguration('xyne');
      const mcpServers: Record<string, McpServerConfig> = {};

      for (const [name, connection] of this.connections) {
        const serverConfig = { ...connection.server.config };
        
        // Remove secret values from the configuration (they're stored separately)
        if (serverConfig.env) {
          const sanitizedEnv: Record<string, { value: string; isSecret: boolean }> = {};
          for (const [key, envConfig] of Object.entries(serverConfig.env)) {
            sanitizedEnv[key] = {
              value: envConfig.isSecret ? '' : envConfig.value, // Clear secret values
              isSecret: envConfig.isSecret
            };
          }
          serverConfig.env = sanitizedEnv;
        }
        
        mcpServers[name] = serverConfig;
      }

      await config.update('mcpServers', mcpServers, vscode.ConfigurationTarget.Global);
      Logger.info('Saved MCP servers to configuration');
    } catch (error) {
      Logger.error(error, 'Failed to save MCP servers to configuration');
    }
  }

  /**
   * Fetch capabilities (tools and resources) from an MCP server
   */
  private async fetchServerCapabilities(connection: McpConnection): Promise<void> {
    const { server, client } = connection;
    
    if (!client) {
      throw new Error('No client available');
    }

    try {
      // Fetch tools
      const toolsResponse = await client.request(
        { method: 'tools/list' },
        ListToolsResultSchema
      );
      
      server.tools = (toolsResponse.tools || []).map(tool => ({
        name: tool.name,
        description: tool.description,
        inputSchema: tool.inputSchema,
        serverName: server.name
      }));

      Logger.info(`Fetched ${server.tools.length} tools from ${server.name}`);
    } catch (error) {
      Logger.warn(`Failed to fetch tools from ${server.name}:`, error);
      server.tools = [];
    }

    try {
      // Fetch resources
      const resourcesResponse = await client.request(
        { method: 'resources/list' },
        ListResourcesResultSchema
      );
      
      server.resources = (resourcesResponse.resources || []).map(resource => ({
        uri: resource.uri,
        name: resource.name,
        description: resource.description,
        serverName: server.name
      }));

      Logger.info(`Fetched ${server.resources.length} resources from ${server.name}`);
    } catch (error) {
      Logger.warn(`Failed to fetch resources from ${server.name}:`, error);
      server.resources = [];
    }
  }

  /**
   * Dispose of all connections
   */
  public dispose(): void {
    for (const serverName of this.connections.keys()) {
      this.disconnectFromServer(serverName);
    }
    this.connections.clear();
    this.onServersChangedEmitter.dispose();
  }
}
/**
 * MCP (Model Context Protocol) Type Definitions
 * Based on the MCP specification and <PERSON>line's implementation patterns
 */

export interface McpServerConfig {
  name: string;
  type: 'stdio' | 'sse' | 'http';
  command?: string;
  args?: string[];
  url?: string;
  env?: Record<string, string>;
  disabled?: boolean;
  autoApprove?: string[];
  timeout?: number;
  cwd?: string;
}

export interface McpServer {
  name: string;
  config: McpServerConfig;
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
  tools?: McpTool[];
  resources?: McpResource[];
  lastError?: string;
  connected?: boolean;
  connectedAt?: number;
  lastHeartbeat?: number;
}

export interface McpTool {
  name: string;
  description?: string;
  inputSchema?: {
    type: string;
    properties?: Record<string, any>;
    required?: string[];
  };
  serverName: string;
}

export interface McpResource {
  uri: string;
  name?: string;
  description?: string;
  mimeType?: string;
  serverName: string;
}

export interface McpConnection {
  server: McpServer;
  process?: any;
  transport?: any;
  client?: any;
  lastHeartbeat?: number;
}

export interface McpToolCall {
  id: string;
  name: string;
  arguments: Record<string, any>;
  serverName: string;
}

export interface McpToolResult {
  id: string;
  content?: McpContent[];
  error?: string;
  isError: boolean;
}

export interface McpContent {
  type: 'text' | 'image' | 'resource';
  text?: string;
  data?: string;
  mimeType?: string;
  uri?: string;
}

export interface McpNotification {
  method: string;
  params?: any;
}

// MCP Protocol Messages
export interface McpMessage {
  jsonrpc: '2.0';
  id?: string | number;
  method?: string;
  params?: any;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

export interface McpRequest extends McpMessage {
  method: string;
  params?: any;
}

export interface McpResponse extends McpMessage {
  id: string | number;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

// MCP Standard Methods
export interface McpInitializeRequest {
  protocolVersion: string;
  capabilities: {
    roots?: {
      listChanged?: boolean;
    };
    sampling?: {};
  };
  clientInfo: {
    name: string;
    version: string;
  };
}

export interface McpInitializeResponse {
  protocolVersion: string;
  capabilities: {
    logging?: {};
    prompts?: {
      listChanged?: boolean;
    };
    resources?: {
      subscribe?: boolean;
      listChanged?: boolean;
    };
    tools?: {
      listChanged?: boolean;
    };
  };
  serverInfo: {
    name: string;
    version: string;
  };
}

export interface McpListToolsResponse {
  tools: Array<{
    name: string;
    description?: string;
    inputSchema: {
      type: string;
      properties?: Record<string, any>;
      required?: string[];
    };
  }>;
}

export interface McpListResourcesResponse {
  resources: Array<{
    uri: string;
    name?: string;
    description?: string;
    mimeType?: string;
  }>;
}

export interface McpCallToolRequest {
  name: string;
  arguments?: Record<string, any>;
}

export interface McpCallToolResponse {
  content: Array<{
    type: 'text' | 'image' | 'resource';
    text?: string;
    data?: string;
    mimeType?: string;
  }>;
  isError?: boolean;
}

export interface McpReadResourceRequest {
  uri: string;
}

export interface McpReadResourceResponse {
  contents: Array<{
    uri: string;
    mimeType?: string;
    text?: string;
    blob?: string;
  }>;
}

// Configuration and UI Types
export interface McpConfiguration {
  servers: Record<string, McpServerConfig>;
  globalSettings: {
    autoStart: boolean;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    maxRetries: number;
    retryDelay: number;
  };
}

export interface McpViewState {
  selectedTab: 'marketplace' | 'remote' | 'installed';
  selectedServer?: string;
  expandedServers: Set<string>;
  showAdvanced: boolean;
}

export interface McpStats {
  totalServers: number;
  connectedServers: number;
  totalTools: number;
  totalResources: number;
  errors: string[];
}

// Event Types
export interface McpServerEvent {
  type: 'connected' | 'disconnected' | 'error' | 'tools_changed' | 'resources_changed';
  serverName: string;
  data?: any;
  timestamp: number;
}

export interface McpToolEvent {
  type: 'called' | 'completed' | 'error';
  serverName: string;
  toolName: string;
  data?: any;
  timestamp: number;
}

// Marketplace Types (for future implementation)
export interface McpMarketplaceServer {
  name: string;
  description: string;
  version: string;
  author: string;
  repository?: string;
  license?: string;
  tags: string[];
  config: Partial<McpServerConfig>;
  downloadCount?: number;
  rating?: number;
  lastUpdated: string;
}

export interface McpMarketplaceCategory {
  id: string;
  name: string;
  description: string;
  icon?: string;
  serverCount: number;
}

// Error Types
export class McpError extends Error {
  constructor(
    message: string,
    public code: number = -1,
    public data?: any
  ) {
    super(message);
    this.name = 'McpError';
  }
}

export class McpConnectionError extends McpError {
  constructor(message: string, public serverName: string) {
    super(message, -32000);
    this.name = 'McpConnectionError';
  }
}

export class McpToolError extends McpError {
  constructor(
    message: string, 
    public toolName: string, 
    public serverName: string
  ) {
    super(message, -32001);
    this.name = 'McpToolError';
  }
}

export class McpResourceError extends McpError {
  constructor(
    message: string, 
    public uri: string, 
    public serverName: string
  ) {
    super(message, -32002);
    this.name = 'McpResourceError';
  }
}
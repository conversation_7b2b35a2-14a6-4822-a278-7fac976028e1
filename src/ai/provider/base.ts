import type { ModelParams, ConverseResponse, AIProviders } from "../types";

// Use a generic message interface that's compatible with AWS SDK
export interface Message {
  role: "user" | "assistant" | "system";
  content: Array<{ text?: string }>;
}

export default abstract class BaseProvider {
  protected client: any;
  protected provider: AIProviders;

  constructor(client: any, provider: AIProviders) {
    this.client = client;
    this.provider = provider;
  }

  abstract converse(
    messages: any[], // Use any[] to allow provider-specific message types
    params: ModelParams,
  ): Promise<ConverseResponse>;

  abstract converseStream(
    messages: any[], // Use any[] to allow provider-specific message types
    params: ModelParams,
  ): AsyncIterableIterator<ConverseResponse>;

  protected getModelParams(params: ModelParams): ModelParams {
    return {
      modelId: params.modelId,
      systemPrompt: params.systemPrompt || "You are a helpful AI assistant.",
      maxTokens: params.maxTokens || 2500,
      temperature: params.temperature || 0.6,
      topP: params.topP || 0.9,
      reasoning: params.reasoning || false,
    };
  }
}
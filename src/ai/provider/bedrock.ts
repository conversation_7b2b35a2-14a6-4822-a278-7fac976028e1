import {
  BedrockRuntimeClient,
  ConverseCommand,
  ConverseStreamCommand,
  type Message as AWSMessage,
} from "@aws-sdk/client-bedrock-runtime";
import { modelDetailsMap } from "../mappers";
import type { ConverseResponse, ModelParams } from "../types";
import { AIProviders, Models } from "../types";
import BaseProvider from "./base";
import { calculateCost } from "../../utils/index";
import { getLogger } from "../../logger";
import { Subsystem } from "../../logger";
const Logger = getLogger(Subsystem.AI);
import config from "../../config";
const { StartThinkingToken, EndThinkingToken } = config;

export class BedrockProvider extends BaseProvider {
  constructor(client: any) {
    super(client, AIProviders.AwsBedrock);
  }

  private validateModel(modelId: string): void {
    const modelDetails = modelDetailsMap[modelId as Models];
    if (!modelDetails) {
      throw new Error(`Unknown model: ${modelId}`);
    }
    if (modelDetails.provider !== AIProviders.AwsBedrock) {
      throw new Error(`Model ${modelId} is not supported by AWS Bedrock provider. This model requires ${modelDetails.provider} provider.`);
    }
  }

  async converse(
    messages: AWSMessage[],
    params: ModelParams,
  ): Promise<ConverseResponse> {
    const modelParams = this.getModelParams(params);

    // Validate model compatibility
    this.validateModel(modelParams.modelId);

    const command = new ConverseCommand({
      modelId: modelParams.modelId,
      system: [
        {
          text: modelParams.systemPrompt!,
        },
      ],
      messages: messages,
      inferenceConfig: {
        maxTokens: modelParams.maxTokens || 512,
        topP: modelParams.topP || 0.9,
        temperature: modelParams.temperature || 0,
      },
    });
    const response = await (this.client as BedrockRuntimeClient).send(command);
    if (!response) {
      throw new Error("Invalid bedrock response");
    }

    let fullResponse = response.output?.message?.content?.reduce(
      (prev: string, current: any) => {
        prev += current.text;
        return prev;
      },
      "",
    );
    if (!response.usage) {
      throw new Error("Could not get usage");
    }
    const { inputTokens, outputTokens } = response.usage;
    return {
      text: fullResponse,
      cost: calculateCost(
        { inputTokens: inputTokens!, outputTokens: outputTokens! },
        modelDetailsMap[modelParams.modelId as Models].cost.onDemand,
      ),
    };
  }

  async *converseStream(
    messages: AWSMessage[],
    params: ModelParams,
  ): AsyncIterableIterator<ConverseResponse> {
    const modelParams = this.getModelParams(params);

    // Validate model compatibility
    this.validateModel(modelParams.modelId);

    const reasoningModel =
      modelParams.modelId === Models.Claude_Sonnet_4 ||
      modelParams.modelId === Models.Claude_3_7_Sonnet;
    const isThinkingEnabled = params.reasoning;

    const reasoningConfig =
      isThinkingEnabled && reasoningModel
        ? {
            thinking: {
              type: "enabled",
              budget_tokens: 1024,
            },
          }
        : undefined;

    const temperature =
      isThinkingEnabled && reasoningModel ? 1 : modelParams.temperature || 0.6;

    const inferenceConfig = isThinkingEnabled
      ? {
          maxTokens: modelParams.maxTokens || 2500,
          temperature: temperature,
        }
      : {
          maxTokens: modelParams.maxTokens || 2500,
          topP: modelParams.topP || 0.9,
          temperature: temperature,
        };

    const command = new ConverseStreamCommand({
      modelId: modelParams.modelId,
      additionalModelRequestFields: reasoningConfig,
      system: [{ text: modelParams.systemPrompt! }],
      messages: messages,
      inferenceConfig,
    });

    let modelId = modelParams.modelId!;
    let costYielded = false;
    let startedReasoning = false;
    let reasoningComplete = false;

    try {
      const response = await this.client.send(command);
      // we are handling the reasoning and normal text in the same iteration
      // using two different iteration for reasoning and normal text we are lossing some data of normal text
      if (response.stream) {
        for await (const chunk of response.stream) {
          // Handle reasoning content
          const reasoning =
            chunk.contentBlockDelta?.delta?.reasoningContent?.text || "";
          if (reasoning && isThinkingEnabled && !reasoningComplete) {
            if (!startedReasoning) {
              yield { text: `${StartThinkingToken}${reasoning}` };
              startedReasoning = true;
            } else {
              yield { text: reasoning };
            }
          }

          // Handle reasoning completion
          if (
            chunk.contentBlockStop &&
            startedReasoning &&
            !reasoningComplete
          ) {
            yield { text: EndThinkingToken };
            reasoningComplete = true;
            continue;
          }

          // Handle regular content
          const text = chunk.contentBlockDelta?.delta?.text || "";
          const metadata = chunk.metadata;

          if (text) {
            yield {
              text,
              metadata,
              cost:
                !costYielded && metadata?.usage
                  ? calculateCost(
                      {
                        inputTokens: metadata.usage.inputTokens!,
                        outputTokens: metadata.usage.outputTokens!,
                      },
                      modelDetailsMap[modelId as Models].cost.onDemand,
                    )
                  : undefined,
            };
            if (metadata?.usage) {costYielded = true;}
          } else if (metadata?.usage && !costYielded) {
            costYielded = true;
            yield {
              text: "",
              metadata,
              cost: calculateCost(
                {
                  inputTokens: metadata.usage.inputTokens!,
                  outputTokens: metadata.usage.outputTokens!,
                },
                modelDetailsMap[modelId as Models].cost.onDemand,
              ),
            };
          }
        }
      }
    } catch (error) {
      Logger.error(error, "Error in converseStream of Bedrock");
      throw error;
    }
  }
}
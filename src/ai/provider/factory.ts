import { BedrockRuntimeClient } from "@aws-sdk/client-bedrock-runtime";
import { OpenAI } from "openai";
import { AIProviders, ProviderConfig, Models } from "../types";
import { BedrockProvider } from "./bedrock";
import { OpenAIProvider } from "./openai";
import AzureOpenAIProvider from "./azure-openai";
import { GeminiAIProvider } from "./gemini";
import { VertexAIProvider } from "./vertex";
import { OpenRouterProvider } from "./openrouter";
import BaseProvider from "./base";
import { getLogger, Subsystem } from "../../logger";
import { modelDetailsMap } from "../mappers";

const logger = getLogger(Subsystem.AI);

export class ProviderFactory {
  static async createProvider(config: ProviderConfig, selectedModel?: Models): Promise<BaseProvider | null> {
    try {
      // Validate model compatibility if selectedModel is provided
      if (selectedModel) {
        const modelDetails = modelDetailsMap[selectedModel];
        if (modelDetails && modelDetails.provider !== config.provider) {
          logger.warn(`Model ${selectedModel} is not compatible with provider ${config.provider}. Model requires ${modelDetails.provider} provider.`);
          // Don't throw error here, just log warning - let the provider handle validation
        }
      }

      switch (config.provider) {
        case AIProviders.AwsBedrock:
          if (!config.accessKeyId || !config.secretAccessKey || !config.region) {
            logger.warn("Incomplete AWS Bedrock configuration");
            return null;
          }

          const bedrockClient = new BedrockRuntimeClient({
            region: config.region,
            credentials: {
              accessKeyId: config.accessKeyId,
              secretAccessKey: config.secretAccessKey,
            },
          });

          return new BedrockProvider(bedrockClient);

        case AIProviders.OpenAI:
          if (!config.apiKey) {
            logger.warn("OpenAI API key not provided");
            return null;
          }

          const openaiClient = new OpenAI({
            apiKey: config.apiKey,
          });

          return new OpenAIProvider(openaiClient);

        case AIProviders.AzureOpenAI:
          if (!config.apiKey || !config.azureEndpoint || !config.azureDeploymentName) {
            logger.warn("Incomplete Azure OpenAI configuration");
            return null;
          }

          return new AzureOpenAIProvider(config, selectedModel || "");

        case AIProviders.GoogleAI:
          const geminiKey = config.apiKey;
          if (!geminiKey) {
            throw new Error('Google AI API key is required but not provided');
          }

          return new GeminiAIProvider(geminiKey);

        case AIProviders.VertexAI:
          if (!config.vertexProjectId || !config.vertexRegion) {
            logger.warn("Incomplete Vertex AI configuration - missing project ID or region");
            return null;
          }

          return new VertexAIProvider({
            projectId: config.vertexProjectId,
            region: config.vertexRegion,
            apiKey: config.apiKey, // For Gemini models fallback
          });

        case AIProviders.OpenRouter:
          if (!config.openRouterApiKey) {
            logger.warn("OpenRouter API key not provided");
            return null;
          }

          return new OpenRouterProvider({
            apiKey: config.openRouterApiKey,
            providerSorting: config.openRouterProviderSorting,
          });

        default:
          logger.error(`Unknown provider: ${config.provider}`);
          return null;
      }
    } catch (error) {
      logger.error(error, `Failed to create provider for ${config.provider}`);
      return null;
    }
  }
}

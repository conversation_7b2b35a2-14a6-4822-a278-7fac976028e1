import { type Message } from "@aws-sdk/client-bedrock-runtime";
import OpenAI from "openai";
import { modelDetailsMap } from "../mappers";
import type { ConverseResponse, ModelParams, Models } from "../types";
import { AIProviders } from "../types";
import BaseProvider from "./base";
import { calculateCost } from "../../utils/index";

export class OpenAIProvider extends BaseProvider {
  constructor(client: OpenAI) {
    super(client, AIProviders.OpenAI);
  }

  private validateModel(modelId: string): void {
    const modelDetails = modelDetailsMap[modelId as Models];
    if (!modelDetails) {
      throw new Error(`Unknown model: ${modelId}`);
    }
    if (modelDetails.provider !== AIProviders.OpenAI) {
      throw new Error(`Model ${modelId} is not supported by OpenAI provider. This model requires ${modelDetails.provider} provider.`);
    }
  }

  async converse(
    messages: Message[],
    params: ModelParams,
  ): Promise<ConverseResponse> {
    const modelParams = this.getModelParams(params);

    // Validate model compatibility
    this.validateModel(modelParams.modelId);

    const chatCompletion = await (
      this.client as OpenAI
    ).chat.completions.create({
      messages: [
        {
          role: "system",
          content: modelParams.systemPrompt!,
        },
        ...messages.map((v) => {
          // Handle content blocks (text and images)
          const content: any[] = [];
          
          v.content?.forEach((block: any) => {
            if (block.text) {
              content.push({ type: "text", text: block.text });
            } else if (block.image) {
              // Convert image format to OpenAI format
              const mimeType = `image/${block.image.format}`;
              let base64Data: string;
              if (typeof block.image.source.bytes === 'string') {
                // Already base64 encoded
                base64Data = block.image.source.bytes;
              } else if (block.image.source.bytes instanceof Uint8Array) {
                // Convert Uint8Array to base64 safely for large images
                const uint8Array = block.image.source.bytes;
                let binaryString = '';
                const chunkSize = 32768; // Process in chunks to avoid stack overflow
                for (let i = 0; i < uint8Array.length; i += chunkSize) {
                  const chunk = uint8Array.slice(i, i + chunkSize);
                  binaryString += String.fromCharCode.apply(null, Array.from(chunk));
                }
                base64Data = btoa(binaryString);
              } else {
                // Fallback: convert to string and encode
                base64Data = btoa(String(block.image.source.bytes));
              }
              
              content.push({
                type: "image_url",
                image_url: {
                  url: `data:${mimeType};base64,${base64Data}`
                }
              });
            }
          });
          
          return {
            content: content.length === 1 && content[0].type === "text" ? content[0].text : content,
            role: v.role!,
          };
        }),
      ],
      model: modelParams.modelId,
      stream: false,
      max_tokens: modelParams.maxTokens,
      temperature: modelParams.temperature,
      top_p: modelParams.topP,
      ...(modelParams.json ? { response_format: { type: "json_object" } } : {}),
    });

    const fullResponse = chatCompletion.choices[0].message?.content || "";
    const modelDetails = modelDetailsMap[modelParams.modelId as Models];
    const cost = calculateCost(
      {
        inputTokens: chatCompletion.usage?.prompt_tokens!,
        outputTokens: chatCompletion.usage?.completion_tokens!,
      },
      modelDetails.cost.onDemand,
    );

    return {
      text: fullResponse,
      cost,
    };
  }

  async *converseStream(
    messages: Message[],
    params: ModelParams,
  ): AsyncIterableIterator<ConverseResponse> {
    const modelParams = this.getModelParams(params);

    // Validate model compatibility
    this.validateModel(modelParams.modelId);

    const apiKey = (this.client as OpenAI).apiKey;
    if (!apiKey) {
      throw new Error('OpenAI API key is required');
    }

    const modelDetails = modelDetailsMap[modelParams.modelId as Models];

    const chatCompletion = await (
      this.client as OpenAI
    ).chat.completions.create({
      messages: [
        {
          role: "system",
          content: modelParams.systemPrompt!,
        },
        ...messages.map((v) => {
          // Handle content blocks (text and images)
          const content: any[] = [];
          
          v.content?.forEach((block: any) => {
            if (block.text) {
              content.push({ type: "text", text: block.text });
            } else if (block.image) {
              // Convert image format to OpenAI format
              const mimeType = `image/${block.image.format}`;
              let base64Data: string;
              if (typeof block.image.source.bytes === 'string') {
                // Already base64 encoded
                base64Data = block.image.source.bytes;
              } else if (block.image.source.bytes instanceof Uint8Array) {
                // Convert Uint8Array to base64 safely for large images
                const uint8Array = block.image.source.bytes;
                let binaryString = '';
                const chunkSize = 32768; // Process in chunks to avoid stack overflow
                for (let i = 0; i < uint8Array.length; i += chunkSize) {
                  const chunk = uint8Array.slice(i, i + chunkSize);
                  binaryString += String.fromCharCode.apply(null, Array.from(chunk));
                }
                base64Data = btoa(binaryString);
              } else {
                // Fallback: convert to string and encode
                base64Data = btoa(String(block.image.source.bytes));
              }
              
              content.push({
                type: "image_url",
                image_url: {
                  url: `data:${mimeType};base64,${base64Data}`
                }
              });
            }
          });
          
          return {
            content: content.length === 1 && content[0].type === "text" ? content[0].text : content,
            role: v.role!,
          };
        }),
      ],
      model: modelParams.modelId,
      stream: true,
      stream_options: { include_usage: true },
      temperature: modelParams.temperature,
      top_p: modelParams.topP,
    });

    let costYielded = false;

    for await (const chunk of chatCompletion) {
      // Handle content chunks
      if (chunk.choices?.[0]?.delta?.content) {
        yield {
          text: chunk.choices[0].delta.content,
          metadata: chunk.choices[0].finish_reason ?? "",
          cost:
            !costYielded && chunk.usage
              ? calculateCost(
                  {
                    inputTokens: chunk.usage.prompt_tokens,
                    outputTokens: chunk.usage.completion_tokens,
                  },
                  modelDetails.cost.onDemand,
                )
              : undefined,
        };
      }
      // Handle completion token (finish_reason without content)
      else if (chunk.choices?.[0]?.finish_reason) {
        yield {
          text: "",
          metadata: chunk.choices[0].finish_reason,
        };
      }
      // Handle cost (if not yet yielded)
      else if (chunk.usage && !costYielded) {
        costYielded = true;
        yield {
          text: "",
          metadata: "",
          cost: calculateCost(
            {
              inputTokens: chunk.usage.prompt_tokens,
              outputTokens: chunk.usage.completion_tokens,
            },
            modelDetails.cost.onDemand,
          ),
        };
      }
    }
  }
}

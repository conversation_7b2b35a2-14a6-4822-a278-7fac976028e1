import BaseProvider from './base';
import { ModelParams, ConverseResponse, Models, AIProviders } from '../types';
import { modelDetailsMap } from '../mappers';
import { getLogger, Subsystem } from '../../logger';

const logger = getLogger(Subsystem.AI);

export class Gemini<PERSON><PERSON>rovider extends BaseProvider {
    private apiKey: string;
    private googleGenAI: any = null;

    constructor(apiKey: string) {
        super(null, AIProviders.GoogleAI);
        this.apiKey = apiKey;
        this.initializeClient(apiKey);
    }

    private async initializeClient(apiKey: string): Promise<void> {
        try {
            // Dynamic import of the new SDK
            const { GoogleGenAI } = await import('@google/genai');
            this.googleGenAI = new GoogleGenAI({ apiKey });
        } catch (error) {
            console.error(`[GeminiAIProvider] Failed to initialize GoogleGenAI client:`, error);
            throw error;
        }
    }

    private validateModel(modelId: string): void {
        const modelDetails = modelDetailsMap[modelId as Models];
        if (!modelDetails) {
            throw new Error(`Unknown model: ${modelId}`);
        }
        if (modelDetails.provider !== AIProviders.GoogleAI) {
            throw new Error(`Model ${modelId} is not supported by Google AI provider. Model requires ${modelDetails.provider} provider.`);
        }
    }

    private getGeminiModelName(modelId: string): string {
        const geminiModelMap: Record<string, string> = {
            [Models.Gemini_2_5_Flash]: 'gemini-2.5-flash-preview-05-20',
            [Models.Gemini_2_5_Flash_Preview]: Models.Gemini_2_5_Flash_Preview,
            [Models.Gemini_2_0_Flash_Exp]: Models.Gemini_2_0_Flash_Exp,
            [Models.Gemini_1_5_Pro]: 'gemini-1.5-pro-latest',
            [Models.Gemini_1_5_Flash]: 'gemini-1.5-flash-latest'
        };

        // Return the mapped model name if it exists
        if (geminiModelMap[modelId]) {
            return geminiModelMap[modelId];
        }

        // If the modelId starts with 'gemini-', return it directly (for direct model names)
        if (modelId.startsWith('gemini-')) {
            return modelId;
        }

        throw new Error(`Unsupported Gemini model: ${modelId}`);
    }

    // Helper method to convert messages to Gemini format with proper structure
    private convertMessagesToGeminiFormat(messages: any[]): { contents: any[], systemInstruction?: string } {
        const contents: any[] = [];
        let systemInstruction: string | undefined;

        for (const msg of messages) {
            if (msg.role === 'system') {
                systemInstruction = msg.content;
                continue;
            }

            // Ensure parts is always an array with correct structure
            const parts = Array.isArray(msg.content)
                ? msg.content.map((part: any) => {
                    if (typeof part === 'string') {
                        return { text: part };
                    }
                    if (part.text) {
                        return { text: part.text };
                    }
                    if (part.image) {
                        // Convert image to Gemini format
                        let base64Data: string;
                        if (typeof part.image.source.bytes === 'string') {
                            // Already base64 encoded
                            base64Data = part.image.source.bytes;
                        } else if (part.image.source.bytes instanceof Uint8Array) {
                            // Convert Uint8Array to base64 safely for large images
                            const uint8Array = part.image.source.bytes;
                            let binaryString = '';
                            const chunkSize = 32768; // Process in chunks to avoid stack overflow
                            for (let i = 0; i < uint8Array.length; i += chunkSize) {
                                const chunk = uint8Array.slice(i, i + chunkSize);
                                binaryString += String.fromCharCode.apply(null, Array.from(chunk));
                            }
                            base64Data = btoa(binaryString);
                        } else {
                            // Fallback: convert to string and encode
                            base64Data = btoa(String(part.image.source.bytes));
                        }
                        
                        return {
                            inlineData: {
                                mimeType: `image/${part.image.format}`,
                                data: base64Data
                            }
                        };
                    }
                    // Skip toolUse and other complex content blocks for Gemini
                    if (part.toolUse || part.toolResult || part.document) {
                        return null;
                    }
                    return { text: JSON.stringify(part) };
                }).filter(Boolean)
                : [{ text: typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content) }];

            // Only add message if it has valid parts
            if (parts.length > 0) {
                contents.push({
                    role: msg.role === 'assistant' ? 'model' : msg.role,
                    parts: parts
                });
            }
        }

        return { contents, systemInstruction };
    }

    async converse(messages: any[], params: ModelParams): Promise<ConverseResponse> {
        try {
            // Ensure client is initialized
            if (!this.googleGenAI) {
                await this.initializeClient(this.apiKey);
            }

            // Validate model compatibility
            this.validateModel(params.modelId);

            const modelName = this.getGeminiModelName(params.modelId);

            // Convert messages to proper Gemini format
            const { contents, systemInstruction } = this.convertMessagesToGeminiFormat(messages);

            // Use system instruction from params if not found in messages
            const finalSystemInstruction = systemInstruction || params.systemPrompt;

            const requestConfig = {
                temperature: 0,
                systemInstruction: finalSystemInstruction,
            };

            // Use the correct API structure for the @google/genai SDK v1.5.1
            const response = await this.googleGenAI.models.generateContentStream({
                model: modelName,
                contents: contents,
                config: requestConfig,
            });

            if (!response.text) {
                throw new Error('No response text received from Gemini API');
            }

            // Calculate costs
            const modelDetails = modelDetailsMap[params.modelId as Models];
            const inputTokens = response.usageMetadata?.promptTokenCount || this.estimateTokens(this.extractTextFromMessages(messages));
            const outputTokens = response.usageMetadata?.candidatesTokenCount || this.estimateTokens(response.text);

            const inputCost = (inputTokens / 1000) * modelDetails.cost.onDemand.inputTokens;
            const outputCost = (outputTokens / 1000) * modelDetails.cost.onDemand.outputTokens;

            return {
                text: response.text,
                cost: inputCost + outputCost,
                metadata: {
                    model: params.modelId,
                    inputTokens,
                    outputTokens,
                }
            };
        } catch (error) {
            console.error('[GeminiAIProvider] Error in converse:', error);
            logger.error(error, 'Gemini API error');
            throw new Error(`Gemini API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    async *converseStream(messages: any[], params: ModelParams): AsyncIterableIterator<ConverseResponse> {
        try {
            // Ensure client is initialized
            if (!this.googleGenAI) {
                await this.initializeClient(this.apiKey);
            }

            // Validate model compatibility
            this.validateModel(params.modelId);

            const modelName = this.getGeminiModelName(params.modelId);

            // Convert messages to proper Gemini format
            const { contents, systemInstruction } = this.convertMessagesToGeminiFormat(messages);

            // Use system instruction from params if not found in messages
            const finalSystemInstruction = systemInstruction || params.systemPrompt;

            const requestConfig = {
                temperature: 0,
                systemInstruction: finalSystemInstruction,
            };

            // Use the correct API structure for the @google/genai SDK v1.5.1
            const response = await this.googleGenAI.models.generateContentStream({
                model: modelName,
                contents: contents,
                config: requestConfig,
            });

            let fullText = '';

            for await (const chunk of response) {
                // Extract text from chunk - handle different possible formats
                let chunkText = '';

                if (typeof chunk.text === 'string') {
                    chunkText = chunk.text;
                } else if (chunk.candidates && chunk.candidates[0] && chunk.candidates[0].content && chunk.candidates[0].content.parts) {
                    // Handle the full response structure
                    chunkText = chunk.candidates[0].content.parts
                        .map((part: any) => part.text || '')
                        .join('');
                } else if (chunk.text && typeof chunk.text === 'object') {
                    // If chunk.text is an object, try to extract text from it
                    chunkText = String(chunk.text);
                } else {
                    // Fallback - convert whatever we have to string
                    chunkText = chunk.text ? String(chunk.text) : '';
                }

                fullText += chunkText;

                if (chunkText) {
                    yield {
                        text: chunkText,
                        metadata: {
                            model: params.modelId,
                            isStreaming: true,
                        }
                    };
                }
            }

            // Final response with cost calculation
            const modelDetails = modelDetailsMap[params.modelId as Models];
            const inputTokens = this.estimateTokens(this.extractTextFromMessages(messages));
            const outputTokens = this.estimateTokens(fullText);

            const inputCost = (inputTokens / 1000) * modelDetails.cost.onDemand.inputTokens;
            const outputCost = (outputTokens / 1000) * modelDetails.cost.onDemand.outputTokens;

            yield {
                text: '',
                cost: inputCost + outputCost,
                metadata: {
                    model: params.modelId,
                    inputTokens,
                    outputTokens,
                    isComplete: true,
                }
            };
        } catch (error) {
            console.error('[GeminiAIProvider] Error in converseStream:', error);
            logger.error(error, 'Gemini streaming error');
            throw new Error(`Gemini streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private estimateTokens(text: string): number {
        // Rough estimation: ~4 characters per token for English text
        return Math.ceil(text.length / 4);
    }

    private extractTextFromMessages(messages: any[]): string {
        return messages.map(m => {
            if (typeof m.content === 'string') {
                return m.content;
            } else if (Array.isArray(m.content)) {
                return m.content.map((part: any) => {
                    if (typeof part === 'string') {
                        return part;
                    } else if (part.text) {
                        return part.text;
                    }
                    return '';
                }).join(' ');
            }
            return '';
        }).join(' ');
    }
}

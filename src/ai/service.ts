import * as vscode from 'vscode';
import { ProviderFactory } from './provider/factory';
import { SettingsManager } from '../settings/manager';
import type { ConverseResponse, ModelParams } from './types';
import { Models, AIProviders } from './types';
import { getLogger, Subsystem } from '../logger';
import type BaseProvider from './provider/base';
import type { Message } from '@aws-sdk/client-bedrock-runtime';
import { ToolExecutor } from '../tools/executor';
import { ToolParser } from '../tools/parser';
import { SYSTEM_PROMPT_WITH_TOOLS } from '../tools/definitions';
import { ToolCall, ToolResult } from '../tools/types'; // Added for type safety
import { getEnvironmentPrompt } from '../context/environment';
import { McpToolIntegration } from '../mcp/McpToolIntegration';
import { ChatManager } from '../chat/ChatManager';

const Logger = getLogger(Subsystem.AI);

export class AIService {
  private provider: BaseProvider | null = null;
  private settingsManager: SettingsManager;
  private toolExecutor: ToolExecutor;
  private mcpToolIntegration: McpToolIntegration;
  private chatManager: ChatManager;
  private getCurrentConversationId: () => string | null;

  constructor(
    context: vscode.ExtensionContext,
    private shadowGitManagerFactory: any,
    chatManager: ChatManager,
    getCurrentConversationId: () => string | null,
    mcpToolIntegration: McpToolIntegration,
    onFileChange?: (filePath: string, originalContent: string, newContent: string, toolCallId: string) => void
  ) {
    this.settingsManager = new SettingsManager(context);
    this.shadowGitManagerFactory = shadowGitManagerFactory;
    // We'll create the tool executor with a default manager, but it will be updated per conversation
    const defaultManager = shadowGitManagerFactory.getManagerSync('default') || null;
    this.toolExecutor = new ToolExecutor(context, defaultManager, mcpToolIntegration, onFileChange);
    this.mcpToolIntegration = mcpToolIntegration;
    this.chatManager = chatManager;
    this.getCurrentConversationId = getCurrentConversationId;
  }

  // Method to update the tool executor with conversation-specific shadow Git manager
  async updateToolExecutorForConversation(conversationId: string): Promise<void> {
    if (this.shadowGitManagerFactory) {
      const manager = await this.shadowGitManagerFactory.getManager(conversationId);
      this.toolExecutor.updateShadowGitManager(manager);
    }
  }

  async initialize(): Promise<void> {
    Logger.info('🚀 AIService initializing...');
    await this.refreshProvider();
  }

  private generateSystemPromptWithAllTools(): string {
    // Start with the base system prompt
    let systemPrompt = SYSTEM_PROMPT_WITH_TOOLS;
    
    // Get MCP tools and add them to the prompt
    const mcpTools = this.mcpToolIntegration.getAvailableTools();
    
    if (mcpTools.length > 0) {
      systemPrompt += `\n\n## MCP (Model Context Protocol) Tools\n\n`;
      systemPrompt += `You also have access to these MCP tools from connected servers:\n\n`;
      
      mcpTools.forEach((tool, index) => {
        systemPrompt += `${index + 8}. ${tool.name}: ${tool.description}\n`;
      });
      
      systemPrompt += `\n### MCP Tool Usage\n\n`;
      systemPrompt += `MCP tools follow the same XML format as other tools. For example:\n\n`;
      systemPrompt += `<${mcpTools[0]?.name || 'mcp_tool_name'}>\n`;
      systemPrompt += `<parameter1>value1</parameter1>\n`;
      systemPrompt += `<parameter2>value2</parameter2>\n`;
      systemPrompt += `</${mcpTools[0]?.name || 'mcp_tool_name'}>\n\n`;
      
      systemPrompt += `Available MCP Tools:\n\n`;
      mcpTools.forEach(tool => {
        systemPrompt += `## ${tool.name}\n`;
        systemPrompt += `Description: ${tool.description}\n`;
        if (tool.parameters && tool.parameters.properties) {
          systemPrompt += `Parameters:\n`;
          Object.entries(tool.parameters.properties).forEach(([param, info]: [string, any]) => {
            const required = tool.parameters.required?.includes(param) ? ' (required)' : ' (optional)';
            systemPrompt += `- ${param}${required}: ${info.description || info.type || 'parameter'}\n`;
          });
        }
        systemPrompt += `Usage:\n<${tool.name}>\n`;
        if (tool.parameters && tool.parameters.properties) {
          Object.keys(tool.parameters.properties).forEach(param => {
            systemPrompt += `<${param}>value here</${param}>\n`;
          });
        }
        systemPrompt += `</${tool.name}>\n\n`;
      });
    }
    
    return systemPrompt;
  }

  async refreshProvider(): Promise<void> {
    try {
      Logger.info('🔄 Refreshing AI provider...');
      const settings = await this.settingsManager.getSettings();

      Logger.info(`📋 Current settings loaded: Provider=${settings.selectedProvider}, Model=${settings.selectedModel}, HasConfig=${!!settings.providers[settings.selectedProvider]}`);

      const providerConfig = settings.providers[settings.selectedProvider];

      this.provider = await ProviderFactory.createProvider(providerConfig, settings.selectedModel);

      if (this.provider) {
        Logger.info(`✅ AI provider successfully initialized: ${settings.selectedProvider} with model: ${settings.selectedModel}`);
      } else {
        Logger.warn(`❌ Failed to initialize AI provider: ${settings.selectedProvider} with model: ${settings.selectedModel}`);
      }
    } catch (error) {
      Logger.error(error, '💥 Failed to refresh AI provider');
      this.provider = null;
    }
  }

  isAvailable(): boolean {
    const available = this.provider !== null;
    Logger.info(`🔍 AI Service availability check: ${available ? 'AVAILABLE' : 'NOT AVAILABLE'}`);
    return available;
  }

  async converse(
    messages: Message[],
    params?: Partial<ModelParams>,
    customInstructions?: string
  ): Promise<ConverseResponse> {
    if (!this.provider) {
      Logger.error('❌ No AI provider available for converse');
      throw new Error('No AI provider available');
    }

    const settings = await this.settingsManager.getSettings();
    const environmentPrompt = await getEnvironmentPrompt();
    
    // Build system prompt with custom instructions and MCP tools
    let systemPrompt = this.generateSystemPromptWithAllTools();
    
    if (customInstructions && customInstructions.trim()) {
      systemPrompt += `\n\n## Custom Instructions\n${customInstructions.trim()}`;
    }
    
    systemPrompt += `\n\nEnvironment Context:\n${environmentPrompt}\n\n`;
    
    const modelParams: ModelParams = {
      modelId: settings.selectedModel,
      systemPrompt,
      maxTokens: 4000,
      temperature: 0.7,
      topP: 0.9,
      ...params,
    };

    Logger.info(`💬 Starting conversation with model: ${modelParams.modelId} via provider: ${settings.selectedProvider}`);
    Logger.info(`📝 Message content: "${messages[0]?.content?.[0]?.text?.substring(0, 50)}..."`);

    const response = await this.provider.converse(messages, modelParams);

    Logger.info(`✅ Conversation completed. Response length: ${response.text?.length || 0} chars, Cost: $${response.cost?.toFixed(6) || 'N/A'}`);

    return response;
  }

async *converseStream(
  messages: Message[],
  params?: Partial<ModelParams>,
  webview?: vscode.Webview,
  customInstructions?: string
): AsyncIterableIterator<ConverseResponse> {
  if (!this.provider) {
    Logger.error('❌ No AI provider available for converseStream');
    throw new Error('No AI provider available');
  }

  const environmentPrompt = await getEnvironmentPrompt();
  const settings = await this.settingsManager.getSettings();
  
  // Build system prompt with custom instructions and MCP tools
  let systemPrompt = this.generateSystemPromptWithAllTools();
  
  if (customInstructions && customInstructions.trim()) {
    systemPrompt += `\n\n## Custom Instructions\n${customInstructions.trim()}`;
  }
  
  systemPrompt += `\n\nEnvironment Context:\n${environmentPrompt}\n\n`;
  
  const modelParams: ModelParams = {
    modelId: settings.selectedModel,
    systemPrompt,
    maxTokens: 4000,
    temperature: 0.7,
    topP: 0.9,
    ...params,
  };

  Logger.info(`🌊 Starting streaming conversation with model: ${modelParams.modelId} via provider: ${settings.selectedProvider}`);
  Logger.info(`📝 Message content: "${messages[0]?.content?.[0]?.text?.substring(0, 50)}..."`);

  let totalChunks = 0;
  let totalCost = 0;
  let fullResponseTextFromLLM = '';
  let executedToolInfo: { toolCall: ToolCall, toolResult: ToolResult }[] = [];

  for await (const chunk of this.provider.converseStream(messages, modelParams)) {
    totalChunks++;
    if (chunk.cost) {
      totalCost = chunk.cost;
    }

    if (chunk.text) {
      fullResponseTextFromLLM += chunk.text;
    }

    yield chunk;

    if (chunk.isComplete) {
      break;
    }
  }

  Logger.info(`📝 Initial LLM response complete. Length: ${fullResponseTextFromLLM.length}`);

  const parsedToolCalls = ToolParser.parseToolCalls(fullResponseTextFromLLM);

  if (webview && parsedToolCalls.length > 0) {
    Logger.info(`🔧 Found ${parsedToolCalls.length} tool calls. Executing...`);

    for (const toolCall of parsedToolCalls) {
      const conversationId = this.getCurrentConversationId ? this.getCurrentConversationId() : undefined;
      const executionResult = await this.toolExecutor.executeToolCall(toolCall, webview, this.chatManager, conversationId || undefined);

      // Check for truncated response in write_to_file
      if (toolCall.name === 'write_to_file' && executionResult.error === 'TRUNCATED_RESPONSE: No EOF marker found. Content may be incomplete.') {
        Logger.warn('🔄 Detected truncated write_to_file response. Triggering replace_in_file fallback...');

        try {
          const fallbackResult = await this.handleWriteToFileFallback(toolCall, webview);
          executedToolInfo.push({ toolCall, toolResult: fallbackResult });
        } catch (fallbackError) {
          Logger.error(fallbackError, 'Failed to execute fallback replace_in_file');
          executedToolInfo.push({ toolCall, toolResult: executionResult }); // Use original failed result
        }
      } else {
        executedToolInfo.push({ toolCall, toolResult: executionResult });
      }
    }

    Logger.info(`🛠️ All ${parsedToolCalls.length} tool calls executed. Results will be processed by extension.`);
  }

  if (executedToolInfo.length > 0) {
    yield {
      text: undefined,
      cost: totalCost,
      isComplete: true,
      metadata: {
        tool_executions: executedToolInfo,
        raw_llm_response: fullResponseTextFromLLM
      }
    };
  }

  Logger.info(`✅ Streaming completed. Total chunks: ${totalChunks}, Final cost: $${totalCost.toFixed(6)}`);
}

  /**
   * Handle fallback when write_to_file fails due to truncated response
   */
  private async handleWriteToFileFallback(originalToolCall: ToolCall, webview: vscode.Webview): Promise<ToolResult> {
    if (!this.provider) {
      throw new Error('No AI provider available for fallback');
    }

    const { path: filePath } = originalToolCall.parameters;

    Logger.info(`🔄 Executing fallback for write_to_file: ${filePath}`);

    // Read the current file content
    let currentContent = '';
    try {
      const workspaceUri = vscode.workspace.workspaceFolders?.[0]?.uri;
      if (!workspaceUri) {
        throw new Error('No workspace folder available');
      }

      const fileUri = vscode.Uri.joinPath(workspaceUri, filePath);
      const contentBuffer = await vscode.workspace.fs.readFile(fileUri);
      currentContent = Buffer.from(contentBuffer).toString('utf8');
    } catch (error) {
      Logger.warn(`Could not read existing file ${filePath}: ${error}. Proceeding with empty content.`);
    }

    // Construct fallback prompt
    const fallbackPrompt = `The previous attempt to rewrite the file "${filePath}" failed because the response was incomplete.

You must now provide the required changes using the "Atomic Contextual Block" method. Your response must be a single JSON object containing three keys:

"context_before": A string containing 2-4 lines of code that come immediately before the code you want to change. If the change is at the very beginning of the file, this must be an empty string ("").

"block_to_replace": A string containing the exact, original block of code that needs to be removed or changed.

"replace_with_block": A string containing the new code that will replace the block_to_replace.

Constraint: The combination of context_before + block_to_replace must form a unique block within the file. Do not provide a context that is ambiguous.

Current file content:
\`\`\`
${currentContent}
\`\`\`

Please provide the JSON response to make the required changes to this file.`;

    // Create fallback message
    const fallbackMessage: Message = {
      role: 'user',
      content: [{ text: fallbackPrompt }]
    };

    // Get AI response for fallback
    const settings = await this.settingsManager.getSettings();
    const modelParams: ModelParams = {
      modelId: settings.selectedModel,
      systemPrompt: 'You are a precise code editor. Respond only with valid JSON as requested.',
      maxTokens: 2000,
      temperature: 0.1, // Lower temperature for precision
      topP: 0.9,
    };

    Logger.info('🤖 Requesting AI fallback response...');
    const fallbackResponse = await this.provider.converse([fallbackMessage], modelParams);

    if (!fallbackResponse.text) {
      throw new Error('No response from AI for fallback');
    }

    // Parse JSON response
    let jsonResponse: any;
    try {
      // Extract JSON from response (handle potential markdown code blocks)
      const jsonMatch = fallbackResponse.text.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/) ||
                       fallbackResponse.text.match(/(\{[\s\S]*?\})/);

      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }

      jsonResponse = JSON.parse(jsonMatch[1]);
    } catch (error) {
      Logger.error(error, 'Failed to parse AI fallback response as JSON');
      throw new Error(`Invalid JSON response from AI: ${error}`);
    }

    // Validate JSON structure
    if (!jsonResponse.context_before || !jsonResponse.block_to_replace || !jsonResponse.replace_with_block) {
      throw new Error('Invalid JSON structure: missing required fields');
    }

    // Create replace_in_file tool call
    const replaceToolCall: ToolCall = {
      id: `replace_${Date.now()}`,
      name: 'replace_in_file',
      parameters: {
        path: filePath,
        context_before: jsonResponse.context_before,
        block_to_replace: jsonResponse.block_to_replace,
        replace_with_block: jsonResponse.replace_with_block
      }
    };

    Logger.info('🔧 Executing replace_in_file with AI-generated parameters...');
    return await this.toolExecutor.executeToolCall(replaceToolCall, webview, null);
  }

  async testCredentials(model: Models, provider: AIProviders): Promise<{ success: boolean; error?: string }> {
    try {
      const settings = await this.settingsManager.getSettings();
      
      let providerToTest = null;
      
      if (this.provider && settings.selectedModel === model && settings.selectedProvider === provider) {
        providerToTest = this.provider;
      } else {
        const providerConfig = settings.providers[provider];
        if (!providerConfig) {
          return { success: false, error: `No configuration found for provider ${provider}` };
        }

        try {
          providerToTest = await ProviderFactory.createProvider(providerConfig, model);
        } catch (factoryError) {
          return {
            success: false,
            error: `Provider creation failed: ${factoryError instanceof Error ? factoryError.message : 'Unknown error'}`
          };
        }

        if (!providerToTest) {
          return { success: false, error: `Failed to create provider instance for ${provider}` };
        }
      }

      if (typeof providerToTest.converseStream !== 'function') {
        return {
          success: false,
          error: `Provider ${provider} implementation is invalid - missing converseStream method`
        };
      }

      const testMessages = [{
        role: 'user' as const,
        content: [{ text: 'Say "test" (one word only)' }]
      }];

      const testModelParams: ModelParams = {
        modelId: model,
        systemPrompt: "You are a helpful AI assistant. Respond with exactly one word: 'test'.",
        maxTokens: 10,
        temperature: 0,
        topP: 1
      };

      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Test timeout after 30 seconds')), 30000);
      });

      let hasReceivedData = false;
      
      const streamPromise = (async () => {
        const stream = providerToTest.converseStream(testMessages, testModelParams);
        
        for await (const chunk of stream) {
          hasReceivedData = true;
          if (chunk.text && chunk.text.trim()) {
            break;
          }
        }
        
        return hasReceivedData;
      })();
      
      await Promise.race([streamPromise, timeoutPromise]);
      
      return { success: true };

    } catch (error) {      
      let errorMessage = 'Unknown error occurred';
      if (error instanceof Error) {
        errorMessage = error.message;
        
        if (errorMessage.includes('fetch failed') || 
            errorMessage.includes('network') ||
            errorMessage.includes('ENOTFOUND') ||
            errorMessage.includes('ECONNREFUSED')) {
          return {
            success: false,
            error: 'Network connectivity issue - credentials will be tested when you send your first message'
          };
        }
        
        if (errorMessage.includes('UnauthorizedOperation') || 
            errorMessage.includes('InvalidAccessKeyId') ||
            errorMessage.includes('SignatureDoesNotMatch') ||
            errorMessage.includes('Authentication') ||
            errorMessage.includes('Invalid API Key') ||
            errorMessage.includes('Forbidden')) {
          errorMessage = 'Invalid credentials - please check your API keys';
        } else if (errorMessage.includes('QuotaExceeded') ||
                   errorMessage.includes('RateLimit')) {
          errorMessage = 'API quota exceeded or rate limited';
        } else if (errorMessage.includes('ModelNotAvailable') ||
                   errorMessage.includes('UnsupportedRegion')) {
          errorMessage = `Model ${model} not available in selected region/provider`;
        }
      }

      return { success: false, error: errorMessage };
    }
  }

  /**
   * Get the ToolExecutor instance for external integrations
   */
  getToolExecutor(): ToolExecutor {
    return this.toolExecutor;
  }
}
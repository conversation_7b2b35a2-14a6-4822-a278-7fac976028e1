import { Models, ModelDetails, AIProviders } from "./types";

export const modelDetailsMap: Record<Models, ModelDetails> = {
  [Models.Claude_Sonnet_4]: {
    name: "Claude 3.5 Sonnet (New)",
    provider: AIProviders.AwsBedrock,
    cost: {
      onDemand: {
        inputTokens: 0.003,
        outputTokens: 0.015,
      },
    },
  },
  [Models.Claude_3_7_Sonnet]: {
    name: "Claude 3.5 Sonnet",
    provider: AIProviders.AwsBedrock,
    cost: {
      onDemand: {
        inputTokens: 0.003,
        outputTokens: 0.015,
      },
    },
  },
  [Models.Claude_3_Haiku]: {
    name: "Claude 3 Haiku",
    provider: AIProviders.AwsBedrock,
    cost: {
      onDemand: {
        inputTokens: 0.00025,
        outputTokens: 0.00125,
      },
    },
  },
  [Models.Claude_3_Opus]: {
    name: "Claude 3 Opus",
    provider: AIProviders.AwsBedrock,
    cost: {
      onDemand: {
        inputTokens: 0.015,
        outputTokens: 0.075,
      },
    },
  },
  [Models.GPT_4o]: {
    name: "GPT-4o",
    provider: AIProviders.OpenAI,
    cost: {
      onDemand: {
        inputTokens: 0.0025,
        outputTokens: 0.01,
      },
    },
  },
  [Models.GPT_4o_Mini]: {
    name: "GPT-4o Mini",
    provider: AIProviders.OpenAI,
    cost: {
      onDemand: {
        inputTokens: 0.00015,
        outputTokens: 0.0006,
      },
    },
  },
  [Models.GPT_4_1]: {
    name: "GPT-4.1",
    provider: AIProviders.OpenAI,
    cost: {
      onDemand: {
        inputTokens: 0.0025,
        outputTokens: 0.01,
      },
    },
  },
  [Models.O1_Preview]: {
    name: "o1-preview",
    provider: AIProviders.OpenAI,
    cost: {
      onDemand: {
        inputTokens: 0.015,
        outputTokens: 0.06,
      },
    },
  },
  [Models.O1_Mini]: {
    name: "o1-mini",
    provider: AIProviders.OpenAI,
    cost: {
      onDemand: {
        inputTokens: 0.003,
        outputTokens: 0.012,
      },
    },
  },
  [Models.Azure_GPT_4o]: {
    name: "GPT-4o (Azure)",
    provider: AIProviders.AzureOpenAI,
    cost: {
      onDemand: {
        inputTokens: 0.0025,
        outputTokens: 0.01,
      },
    },
  },
  [Models.Azure_GPT_4o_Mini]: {
    name: "GPT-4o Mini (Azure)",
    provider: AIProviders.AzureOpenAI,
    cost: {
      onDemand: {
        inputTokens: 0.00015,
        outputTokens: 0.0006,
      },
    },
  },
  [Models.Azure_GPT_4_Turbo]: {
    name: "GPT-4 Turbo (Azure)",
    provider: AIProviders.AzureOpenAI,
    cost: {
      onDemand: {
        inputTokens: 0.01,
        outputTokens: 0.03,
      },
    },
  },
  [Models.Azure_GPT_35_Turbo]: {
    name: "GPT-3.5 Turbo (Azure)",
    provider: AIProviders.AzureOpenAI,
    cost: {
      onDemand: {
        inputTokens: 0.0015,
        outputTokens: 0.002,
      },
    },
  },
  [Models.Gemini_2_5_Flash]: {
    name: "Gemini 2.5 Flash",
    provider: AIProviders.GoogleAI,
    cost: {
      onDemand: {
        inputTokens: 0.000075,
        outputTokens: 0.0003,
      },
    },
  },
  [Models.Gemini_2_5_Flash_Preview]: {
    name: "Gemini 2.5 Flash (Preview)",
    provider: AIProviders.GoogleAI,
    cost: {
      onDemand: {
        inputTokens: 0.000075,
        outputTokens: 0.0003,
      },
    },
  },
  [Models.Gemini_2_5_Pro_Preview]: {
    name: "Gemini 2.5 Pro (Preview)",
    provider: AIProviders.GoogleAI,
    cost: {
      onDemand: {
        inputTokens: 0.00125,
        outputTokens: 0.005,
      },
    },
  },
  [Models.Gemini_2_0_Flash_Exp]: {
    name: "Gemini 2.0 Flash (Experimental)",
    provider: AIProviders.GoogleAI,
    cost: {
      onDemand: {
        inputTokens: 0.000075,
        outputTokens: 0.0003,
      },
    },
  },
  [Models.Gemini_1_5_Pro]: {
    name: "Gemini 1.5 Pro",
    provider: AIProviders.GoogleAI,
    cost: {
      onDemand: {
        inputTokens: 0.00125,
        outputTokens: 0.005,
      },
    },
  },
  [Models.Gemini_1_5_Flash]: {
    name: "Gemini 1.5 Flash",
    provider: AIProviders.GoogleAI,
    cost: {
      onDemand: {
        inputTokens: 0.000075,
        outputTokens: 0.0003,
      },
    },
  },
  // Vertex AI Models
  [Models.Vertex_Claude_Sonnet_4]: {
    name: "Claude Sonnet 4 (Vertex)",
    provider: AIProviders.VertexAI,
    cost: {
      onDemand: {
        inputTokens: 0.003,
        outputTokens: 0.015,
      },
    },
  },
  [Models.Vertex_Claude_3_5_Sonnet_V2]: {
    name: "Claude 3.5 Sonnet V2 (Vertex)",
    provider: AIProviders.VertexAI,
    cost: {
      onDemand: {
        inputTokens: 0.003,
        outputTokens: 0.015,
      },
    },
  },
  [Models.Vertex_Claude_3_5_Sonnet]: {
    name: "Claude 3.5 Sonnet (Vertex)",
    provider: AIProviders.VertexAI,
    cost: {
      onDemand: {
        inputTokens: 0.003,
        outputTokens: 0.015,
      },
    },
  },
  [Models.Vertex_Claude_3_5_Haiku]: {
    name: "Claude 3.5 Haiku (Vertex)",
    provider: AIProviders.VertexAI,
    cost: {
      onDemand: {
        inputTokens: 0.00025,
        outputTokens: 0.00125,
      },
    },
  },
  [Models.Vertex_Claude_3_Opus]: {
    name: "Claude 3 Opus (Vertex)",
    provider: AIProviders.VertexAI,
    cost: {
      onDemand: {
        inputTokens: 0.015,
        outputTokens: 0.075,
      },
    },
  },
  [Models.Vertex_Claude_3_Haiku]: {
    name: "Claude 3 Haiku (Vertex)",
    provider: AIProviders.VertexAI,
    cost: {
      onDemand: {
        inputTokens: 0.00025,
        outputTokens: 0.00125,
      },
    },
  },
  [Models.Vertex_Gemini_2_0_Flash]: {
    name: "Gemini 2.0 Flash (Vertex)",
    provider: AIProviders.VertexAI,
    cost: {
      onDemand: {
        inputTokens: 0.000075,
        outputTokens: 0.0003,
      },
    },
  },
  [Models.Vertex_Gemini_1_5_Pro]: {
    name: "Gemini 1.5 Pro (Vertex)",
    provider: AIProviders.VertexAI,
    cost: {
      onDemand: {
        inputTokens: 0.00125,
        outputTokens: 0.005,
      },
    },
  },
  [Models.Vertex_Gemini_1_5_Flash]: {
    name: "Gemini 1.5 Flash (Vertex)",
    provider: AIProviders.VertexAI,
    cost: {
      onDemand: {
        inputTokens: 0.000075,
        outputTokens: 0.0003,
      },
    },
  },
  // OpenRouter Models
  [Models.OpenRouter_Claude_3_5_Sonnet]: {
    name: "Claude 3.5 Sonnet (OpenRouter)",
    provider: AIProviders.OpenRouter,
    cost: {
      onDemand: {
        inputTokens: 0.003,
        outputTokens: 0.015,
      },
    },
  },
  [Models.OpenRouter_Claude_3_5_Haiku]: {
    name: "Claude 3.5 Haiku (OpenRouter)",
    provider: AIProviders.OpenRouter,
    cost: {
      onDemand: {
        inputTokens: 0.001,
        outputTokens: 0.005,
      },
    },
  },
  [Models.OpenRouter_Claude_3_Opus]: {
    name: "Claude 3 Opus (OpenRouter)",
    provider: AIProviders.OpenRouter,
    cost: {
      onDemand: {
        inputTokens: 0.015,
        outputTokens: 0.075,
      },
    },
  },
  [Models.OpenRouter_GPT_4o]: {
    name: "GPT-4o (OpenRouter)",
    provider: AIProviders.OpenRouter,
    cost: {
      onDemand: {
        inputTokens: 0.005,
        outputTokens: 0.015,
      },
    },
  },
  [Models.OpenRouter_GPT_4o_Mini]: {
    name: "GPT-4o Mini (OpenRouter)",
    provider: AIProviders.OpenRouter,
    cost: {
      onDemand: {
        inputTokens: 0.00015,
        outputTokens: 0.0006,
      },
    },
  },
  [Models.OpenRouter_O1_Preview]: {
    name: "o1-preview (OpenRouter)",
    provider: AIProviders.OpenRouter,
    cost: {
      onDemand: {
        inputTokens: 0.015,
        outputTokens: 0.06,
      },
    },
  },
  [Models.OpenRouter_O1_Mini]: {
    name: "o1-mini (OpenRouter)",
    provider: AIProviders.OpenRouter,
    cost: {
      onDemand: {
        inputTokens: 0.003,
        outputTokens: 0.012,
      },
    },
  },
  [Models.OpenRouter_Gemini_2_0_Flash]: {
    name: "Gemini 2.0 Flash (OpenRouter)",
    provider: AIProviders.OpenRouter,
    cost: {
      onDemand: {
        inputTokens: 0.000075,
        outputTokens: 0.0003,
      },
    },
  },
  [Models.OpenRouter_Gemini_1_5_Pro]: {
    name: "Gemini 1.5 Pro (OpenRouter)",
    provider: AIProviders.OpenRouter,
    cost: {
      onDemand: {
        inputTokens: 0.00125,
        outputTokens: 0.005,
      },
    },
  },
  [Models.OpenRouter_Llama_3_1_405B]: {
    name: "Llama 3.1 405B (OpenRouter)",
    provider: AIProviders.OpenRouter,
    cost: {
      onDemand: {
        inputTokens: 0.005,
        outputTokens: 0.015,
      },
    },
  },
  [Models.OpenRouter_Llama_3_1_70B]: {
    name: "Llama 3.1 70B (OpenRouter)",
    provider: AIProviders.OpenRouter,
    cost: {
      onDemand: {
        inputTokens: 0.0009,
        outputTokens: 0.0009,
      },
    },
  },
  [Models.OpenRouter_Qwen_2_5_72B]: {
    name: "Qwen 2.5 72B (OpenRouter)",
    provider: AIProviders.OpenRouter,
    cost: {
      onDemand: {
        inputTokens: 0.0009,
        outputTokens: 0.0009,
      },
    },
  },
  [Models.OpenRouter_DeepSeek_V3]: {
    name: "DeepSeek V3 (OpenRouter)",
    provider: AIProviders.OpenRouter,
    cost: {
      onDemand: {
        inputTokens: 0.00027,
        outputTokens: 0.00110,
      },
    },
  },
};
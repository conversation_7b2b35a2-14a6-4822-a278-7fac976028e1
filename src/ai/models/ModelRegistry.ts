import { Models, AIProviders } from '../types';

export enum ModelFamily {
  Claude = "claude",
  GPT = "gpt",
  Gemini = "gemini", 
  OpenRouter = "openrouter"
}

export interface ModelMetadata {
  id: Models;
  displayName: string;
  provider: AIProviders;
  family: ModelFamily;
  isDefault?: boolean;
}

/**
 * Centralized registry for all model metadata and operations.
 * Single source of truth for model information across the application.
 */
export class ModelRegistry {
  private static readonly MODELS = new Map<Models, ModelMetadata>([
    // AWS Bedrock Models
    [Models.Claude_Sonnet_4, {
      id: Models.Claude_Sonnet_4,
      displayName: "Claude Sonnet 4",
      provider: AIProviders.AwsBedrock,
      family: ModelFamily.Claude,
      isDefault: true
    }],
    [Models.Claude_3_7_Sonnet, {
      id: Models.Claude_3_7_Sonnet,
      displayName: "Claude 3.5 Sonnet",
      provider: AIProviders.AwsBedrock,
      family: ModelFamily.Claude
    }],
    [Models.Claude_3_Haiku, {
      id: Models.<PERSON>_3_Haiku,
      displayName: "Claude 3 Haiku",
      provider: AIProviders.AwsBedrock,
      family: ModelFamily.Claude
    }],
    [Models.Claude_3_Opus, {
      id: Models.Claude_3_Opus,
      displayName: "Claude 3 Opus",
      provider: AIProviders.AwsBedrock,
      family: ModelFamily.Claude
    }],

    // OpenAI Models
    [Models.GPT_4o, {
      id: Models.GPT_4o,
      displayName: "GPT-4o",
      provider: AIProviders.OpenAI,
      family: ModelFamily.GPT,
      isDefault: true
    }],
    [Models.GPT_4o_Mini, {
      id: Models.GPT_4o_Mini,
      displayName: "GPT-4o Mini",
      provider: AIProviders.OpenAI,
      family: ModelFamily.GPT
    }],
    [Models.GPT_4_1, {
      id: Models.GPT_4_1,
      displayName: "GPT-4.1",
      provider: AIProviders.OpenAI,
      family: ModelFamily.GPT
    }],
    [Models.O1_Preview, {
      id: Models.O1_Preview,
      displayName: "o1-preview",
      provider: AIProviders.OpenAI,
      family: ModelFamily.GPT
    }],
    [Models.O1_Mini, {
      id: Models.O1_Mini,
      displayName: "o1-mini",
      provider: AIProviders.OpenAI,
      family: ModelFamily.GPT
    }],

    // Azure OpenAI Models
    [Models.Azure_GPT_4o, {
      id: Models.Azure_GPT_4o,
      displayName: "GPT-4o",
      provider: AIProviders.AzureOpenAI,
      family: ModelFamily.GPT,
      isDefault: true
    }],
    [Models.Azure_GPT_4o_Mini, {
      id: Models.Azure_GPT_4o_Mini,
      displayName: "GPT-4o Mini",
      provider: AIProviders.AzureOpenAI,
      family: ModelFamily.GPT
    }],
    [Models.Azure_GPT_4_Turbo, {
      id: Models.Azure_GPT_4_Turbo,
      displayName: "GPT-4 Turbo",
      provider: AIProviders.AzureOpenAI,
      family: ModelFamily.GPT
    }],
    [Models.Azure_GPT_35_Turbo, {
      id: Models.Azure_GPT_35_Turbo,
      displayName: "GPT-3.5 Turbo",
      provider: AIProviders.AzureOpenAI,
      family: ModelFamily.GPT
    }],

    // Google AI Models
    [Models.Gemini_2_5_Flash, {
      id: Models.Gemini_2_5_Flash,
      displayName: "Gemini 2.5 Flash",
      provider: AIProviders.GoogleAI,
      family: ModelFamily.Gemini,
      isDefault: true
    }],
    [Models.Gemini_2_5_Flash_Preview, {
      id: Models.Gemini_2_5_Flash_Preview,
      displayName: "Gemini 2.5 Flash Preview",
      provider: AIProviders.GoogleAI,
      family: ModelFamily.Gemini
    }],
    [Models.Gemini_2_5_Pro_Preview, {
      id: Models.Gemini_2_5_Pro_Preview,
      displayName: "Gemini 2.5 Pro Preview",
      provider: AIProviders.GoogleAI,
      family: ModelFamily.Gemini
    }],
    [Models.Gemini_2_0_Flash_Exp, {
      id: Models.Gemini_2_0_Flash_Exp,
      displayName: "Gemini 2.0 Flash (Exp)",
      provider: AIProviders.GoogleAI,
      family: ModelFamily.Gemini
    }],
    [Models.Gemini_1_5_Pro, {
      id: Models.Gemini_1_5_Pro,
      displayName: "Gemini 1.5 Pro",
      provider: AIProviders.GoogleAI,
      family: ModelFamily.Gemini
    }],
    [Models.Gemini_1_5_Flash, {
      id: Models.Gemini_1_5_Flash,
      displayName: "Gemini 1.5 Flash",
      provider: AIProviders.GoogleAI,
      family: ModelFamily.Gemini
    }],

    // Vertex AI Models
    [Models.Vertex_Claude_Sonnet_4, {
      id: Models.Vertex_Claude_Sonnet_4,
      displayName: "Claude Sonnet 4",
      provider: AIProviders.VertexAI,
      family: ModelFamily.Claude,
      isDefault: true
    }],
    [Models.Vertex_Claude_3_5_Sonnet_V2, {
      id: Models.Vertex_Claude_3_5_Sonnet_V2,
      displayName: "Claude 3.5 Sonnet V2",
      provider: AIProviders.VertexAI,
      family: ModelFamily.Claude
    }],
    [Models.Vertex_Claude_3_5_Sonnet, {
      id: Models.Vertex_Claude_3_5_Sonnet,
      displayName: "Claude 3.5 Sonnet",
      provider: AIProviders.VertexAI,
      family: ModelFamily.Claude
    }],
    [Models.Vertex_Claude_3_5_Haiku, {
      id: Models.Vertex_Claude_3_5_Haiku,
      displayName: "Claude 3.5 Haiku",
      provider: AIProviders.VertexAI,
      family: ModelFamily.Claude
    }],
    [Models.Vertex_Claude_3_Opus, {
      id: Models.Vertex_Claude_3_Opus,
      displayName: "Claude 3 Opus",
      provider: AIProviders.VertexAI,
      family: ModelFamily.Claude
    }],
    [Models.Vertex_Claude_3_Haiku, {
      id: Models.Vertex_Claude_3_Haiku,
      displayName: "Claude 3 Haiku",
      provider: AIProviders.VertexAI,
      family: ModelFamily.Claude
    }],
    [Models.Vertex_Gemini_2_0_Flash, {
      id: Models.Vertex_Gemini_2_0_Flash,
      displayName: "Gemini 2.0 Flash",
      provider: AIProviders.VertexAI,
      family: ModelFamily.Gemini
    }],
    [Models.Vertex_Gemini_1_5_Pro, {
      id: Models.Vertex_Gemini_1_5_Pro,
      displayName: "Gemini 1.5 Pro",
      provider: AIProviders.VertexAI,
      family: ModelFamily.Gemini
    }],
    [Models.Vertex_Gemini_1_5_Flash, {
      id: Models.Vertex_Gemini_1_5_Flash,
      displayName: "Gemini 1.5 Flash",
      provider: AIProviders.VertexAI,
      family: ModelFamily.Gemini
    }],

    // OpenRouter Models
    [Models.OpenRouter_Claude_3_5_Sonnet, {
      id: Models.OpenRouter_Claude_3_5_Sonnet,
      displayName: "Claude 3.5 Sonnet",
      provider: AIProviders.OpenRouter,
      family: ModelFamily.Claude
    }],
    [Models.OpenRouter_Claude_3_5_Haiku, {
      id: Models.OpenRouter_Claude_3_5_Haiku,
      displayName: "Claude 3.5 Haiku",
      provider: AIProviders.OpenRouter,
      family: ModelFamily.Claude
    }],
    [Models.OpenRouter_Claude_3_Opus, {
      id: Models.OpenRouter_Claude_3_Opus,
      displayName: "Claude 3 Opus",
      provider: AIProviders.OpenRouter,
      family: ModelFamily.Claude
    }],
    [Models.OpenRouter_GPT_4o, {
      id: Models.OpenRouter_GPT_4o,
      displayName: "GPT-4o",
      provider: AIProviders.OpenRouter,
      family: ModelFamily.GPT,
      isDefault: true
    }],
    [Models.OpenRouter_GPT_4o_Mini, {
      id: Models.OpenRouter_GPT_4o_Mini,
      displayName: "GPT-4o Mini",
      provider: AIProviders.OpenRouter,
      family: ModelFamily.GPT
    }],
    [Models.OpenRouter_O1_Preview, {
      id: Models.OpenRouter_O1_Preview,
      displayName: "o1-preview",
      provider: AIProviders.OpenRouter,
      family: ModelFamily.GPT
    }],
    [Models.OpenRouter_O1_Mini, {
      id: Models.OpenRouter_O1_Mini,
      displayName: "o1-mini",
      provider: AIProviders.OpenRouter,
      family: ModelFamily.GPT
    }],
    [Models.OpenRouter_Gemini_2_0_Flash, {
      id: Models.OpenRouter_Gemini_2_0_Flash,
      displayName: "Gemini 2.0 Flash",
      provider: AIProviders.OpenRouter,
      family: ModelFamily.Gemini
    }],
    [Models.OpenRouter_Gemini_1_5_Pro, {
      id: Models.OpenRouter_Gemini_1_5_Pro,
      displayName: "Gemini 1.5 Pro",
      provider: AIProviders.OpenRouter,
      family: ModelFamily.Gemini
    }],
    [Models.OpenRouter_Llama_3_1_405B, {
      id: Models.OpenRouter_Llama_3_1_405B,
      displayName: "Llama 3.1 405B",
      provider: AIProviders.OpenRouter,
      family: ModelFamily.OpenRouter
    }],
    [Models.OpenRouter_Llama_3_1_70B, {
      id: Models.OpenRouter_Llama_3_1_70B,
      displayName: "Llama 3.1 70B",
      provider: AIProviders.OpenRouter,
      family: ModelFamily.OpenRouter
    }],
    [Models.OpenRouter_Qwen_2_5_72B, {
      id: Models.OpenRouter_Qwen_2_5_72B,
      displayName: "Qwen 2.5 72B",
      provider: AIProviders.OpenRouter,
      family: ModelFamily.OpenRouter
    }],
    [Models.OpenRouter_DeepSeek_V3, {
      id: Models.OpenRouter_DeepSeek_V3,
      displayName: "DeepSeek V3",
      provider: AIProviders.OpenRouter,
      family: ModelFamily.OpenRouter
    }]
  ]);

  /**
   * Get display name for a model
   */
  static getDisplayName(model: Models): string {
    const metadata = this.MODELS.get(model);
    return metadata?.displayName || model;
  }

  /**
   * Get provider for a model
   */
  static getProvider(model: Models): AIProviders | undefined {
    const metadata = this.MODELS.get(model);
    return metadata?.provider;
  }

  /**
   * Get all models for a specific provider
   */
  static getModelsForProvider(provider: AIProviders): Models[] {
    const models: Models[] = [];
    for (const [modelId, metadata] of this.MODELS) {
      if (metadata.provider === provider) {
        models.push(modelId);
      }
    }
    return models;
  }

  /**
   * Get all models grouped by provider
   */
  static getModelsByProvider(): Record<AIProviders, Models[]> {
    const result: Record<AIProviders, Models[]> = {
      [AIProviders.AwsBedrock]: [],
      [AIProviders.OpenAI]: [],
      [AIProviders.AzureOpenAI]: [],
      [AIProviders.GoogleAI]: [],
      [AIProviders.VertexAI]: [],
      [AIProviders.OpenRouter]: []
    };

    for (const [modelId, metadata] of this.MODELS) {
      result[metadata.provider].push(modelId);
    }

    return result;
  }

  /**
   * Get all models of a specific family
   */
  static getModelsByFamily(family: ModelFamily): Models[] {
    const models: Models[] = [];
    for (const [modelId, metadata] of this.MODELS) {
      if (metadata.family === family) {
        models.push(modelId);
      }
    }
    return models;
  }

  /**
   * Get default model for a provider
   */
  static getDefaultModelForProvider(provider: AIProviders): Models | undefined {
    for (const [modelId, metadata] of this.MODELS) {
      if (metadata.provider === provider && metadata.isDefault) {
        return modelId;
      }
    }
    
    // Fallback to first model for provider if no default set
    const providerModels = this.getModelsForProvider(provider);
    return providerModels[0];
  }

  /**
   * Check if a model exists
   */
  static isModelValid(model: Models): boolean {
    return this.MODELS.has(model);
  }

  /**
   * Get all available models
   */
  static getAllModels(): Models[] {
    return Array.from(this.MODELS.keys());
  }

  /**
   * Get model metadata
   */
  static getModelMetadata(model: Models): ModelMetadata | undefined {
    return this.MODELS.get(model);
  }

  /**
   * Get provider display names
   */
  static getProviderDisplayNames(): Record<AIProviders, string> {
    return {
      [AIProviders.AwsBedrock]: 'AWS Bedrock',
      [AIProviders.OpenAI]: 'OpenAI',
      [AIProviders.AzureOpenAI]: 'Azure OpenAI',
      [AIProviders.GoogleAI]: 'Google AI',
      [AIProviders.VertexAI]: 'Vertex AI',
      [AIProviders.OpenRouter]: 'OpenRouter'
    };
  }

  /**
   * Get provider display name
   */
  static getProviderDisplayName(provider: AIProviders): string {
    const displayNames = this.getProviderDisplayNames();
    return displayNames[provider] || provider;
  }
}
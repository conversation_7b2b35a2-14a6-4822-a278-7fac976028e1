import { Models, AIProviders, ProviderConfig } from '../types';
import { ModelRegistry } from './ModelRegistry';

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export interface ModelAvailabilityResult {
  availableModels: Models[];
  currentModel: Models | null;
}

/**
 * Service for handling model configuration, availability, and validation logic.
 * Centralizes all model-related business logic that was scattered across the codebase.
 */
export class ModelConfigurationService {
  /**
   * Get all available models based on provider configurations.
   * Only returns models for providers that have valid credentials.
   */
  async getAvailableModels(
    providerConfigs: Record<AIProviders, ProviderConfig | undefined>, 
    selectedModel?: Models
  ): Promise<ModelAvailabilityResult> {
    const availableModels: Models[] = [];

    // Check each provider and add models if properly configured
    for (const provider of Object.values(AIProviders)) {
      const config = providerConfigs[provider];
      if (this.isProviderConfigured(provider, config)) {
        const providerModels = ModelRegistry.getModelsForProvider(provider);
        availableModels.push(...providerModels);
      }
    }

    // Only return current model if it's in the available models list
    const currentModel = selectedModel && availableModels.includes(selectedModel) 
      ? selectedModel 
      : null;

    return {
      availableModels,
      currentModel
    };
  }

  /**
   * Validate model and provider combination
   */
  validateModelProviderCombination(model: Models, provider: AIProviders): ValidationResult {
    if (!ModelRegistry.isModelValid(model)) {
      return {
        isValid: false,
        error: `Unknown model: ${model}`
      };
    }

    const expectedProvider = ModelRegistry.getProvider(model);
    if (expectedProvider !== provider) {
      return {
        isValid: false,
        error: `Model ${model} is not compatible with provider ${provider}. Model requires ${expectedProvider} provider.`
      };
    }

    return { isValid: true };
  }

  /**
   * Get provider configuration status
   */
  getProviderStatus(provider: AIProviders, config: ProviderConfig | undefined): {
    status: 'configured' | 'not-configured';
    message: string;
  } {
    if (this.isProviderConfigured(provider, config)) {
      return {
        status: 'configured',
        message: 'Provider configured (credentials not tested)'
      };
    }

    return {
      status: 'not-configured',
      message: 'Provider not configured'
    };
  }

  /**
   * Get the best default model for a provider
   */
  getDefaultModelForProvider(provider: AIProviders): Models | null {
    return ModelRegistry.getDefaultModelForProvider(provider) || null;
  }

  /**
   * Check if provider has all required configuration
   */
  private isProviderConfigured(provider: AIProviders, config: ProviderConfig | undefined): boolean {
    if (!config) {
      return false;
    }

    switch (provider) {
      case AIProviders.AwsBedrock:
        return !!(
          config.accessKeyId && 
          config.secretAccessKey && 
          config.region &&
          config.accessKeyId.trim() !== '' && 
          config.secretAccessKey.trim() !== ''
        );

      case AIProviders.OpenAI:
        return !!(config.apiKey && config.apiKey.trim() !== '');

      case AIProviders.AzureOpenAI:
        return !!(
          config.apiKey && 
          config.azureEndpoint &&
          config.apiKey.trim() !== '' && 
          config.azureEndpoint.trim() !== ''
        );

      case AIProviders.GoogleAI:
        return !!(config.apiKey && config.apiKey.trim() !== '');

      case AIProviders.VertexAI:
        return !!(
          config.vertexProjectId && 
          config.vertexRegion &&
          config.vertexProjectId.trim() !== ''
        );

      case AIProviders.OpenRouter:
        return !!(config.openRouterApiKey && config.openRouterApiKey.trim() !== '');

      default:
        return false;
    }
  }

  /**
   * Get all models organized by provider with their display names
   */
  getModelsWithMetadata(): Record<AIProviders, Array<{ id: Models; displayName: string }>> {
    const modelsByProvider = ModelRegistry.getModelsByProvider();
    const result: Record<AIProviders, Array<{ id: Models; displayName: string }>> = {
      [AIProviders.AwsBedrock]: [],
      [AIProviders.OpenAI]: [],
      [AIProviders.AzureOpenAI]: [],
      [AIProviders.GoogleAI]: [],
      [AIProviders.VertexAI]: [],
      [AIProviders.OpenRouter]: []
    };

    for (const [provider, models] of Object.entries(modelsByProvider)) {
      result[provider as AIProviders] = models.map(model => ({
        id: model,
        displayName: ModelRegistry.getDisplayName(model)
      }));
    }

    return result;
  }

  /**
   * Validate that a selected model is available for the current configuration
   */
  async validateSelectedModel(
    selectedModel: Models,
    selectedProvider: AIProviders,
    providerConfigs: Record<AIProviders, ProviderConfig | undefined>
  ): Promise<ValidationResult> {
    // First check if model and provider are compatible
    const compatibilityCheck = this.validateModelProviderCombination(selectedModel, selectedProvider);
    if (!compatibilityCheck.isValid) {
      return compatibilityCheck;
    }

    // Then check if provider is configured
    const config = providerConfigs[selectedProvider];
    if (!this.isProviderConfigured(selectedProvider, config)) {
      return {
        isValid: false,
        error: `Provider ${selectedProvider} is not configured with valid credentials`
      };
    }

    // Finally check if model is in the available models list
    const availability = await this.getAvailableModels(providerConfigs, selectedModel);
    if (!availability.availableModels.includes(selectedModel)) {
      return {
        isValid: false,
        error: `Model ${selectedModel} is not available with current configuration`
      };
    }

    return { isValid: true };
  }
}
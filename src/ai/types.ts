export enum AIProviders {
  AwsBedrock = "aws-bedrock",
  OpenAI = "openai",
  AzureOpenAI = "azure-openai",
  GoogleAI = "google-ai",
  VertexAI = "vertex-ai",
  OpenRouter = "openrouter",
}

export enum Models {
  Claude_Sonnet_4 = "claude-3-5-sonnet-20241022-v2",
  Claude_3_7_Sonnet = "claude-3-5-sonnet-20241022",
  <PERSON><PERSON><PERSON>_<PERSON><PERSON> = "claude-3-haiku-20240307",
  <PERSON>_3_Opus = "claude-3-opus-20240229",
  GPT_4o = "gpt-4o",
  GPT_4o_Mini = "gpt-4o-mini",
  GPT_4_1 = "gpt-4.1",
  O1_Preview = "o1-preview",
  O1_Mini = "o1-mini",
  Azure_GPT_4o = "azure-gpt-4o",
  Azure_GPT_4o_Mini = "azure-gpt-4o-mini",
  Azure_GPT_4_Turbo = "azure-gpt-4-turbo",
  Azure_GPT_35_Turbo = "azure-gpt-3.5-turbo",
  Gemini_2_5_Flash = "gemini-2.5-flash",
  Gemini_2_5_Flash_Preview = "gemini-2.5-flash-preview-05-20",
  Gemini_2_5_Pro_Preview = "gemini-2.5-pro-preview-06-05",
  Gemini_2_0_Flash_Exp = "gemini-2.0-flash",
  Gemini_1_5_Pro = "gemini-1.5-pro",
  Gemini_1_5_Flash = "gemini-1.5-flash",
  // Vertex AI Models (Claude and Gemini) - Matching Cline model IDs
  Vertex_Claude_Sonnet_4 = "claude-sonnet-4@20250514",
  Vertex_Claude_3_5_Sonnet_V2 = "claude-3-5-sonnet-v2@20241022",
  Vertex_Claude_3_5_Sonnet = "claude-3-5-sonnet@20240620", 
  Vertex_Claude_3_5_Haiku = "claude-3-5-haiku@20241022",
  Vertex_Claude_3_Opus = "claude-3-opus@20240229",
  Vertex_Claude_3_Haiku = "claude-3-haiku@20240307",
  Vertex_Gemini_2_0_Flash = "gemini-2.0-flash-exp",
  Vertex_Gemini_1_5_Pro = "gemini-1.5-pro-002",
  Vertex_Gemini_1_5_Flash = "gemini-1.5-flash-002",
  // OpenRouter Models (Popular models from various providers)
  OpenRouter_Claude_3_5_Sonnet = "anthropic/claude-3.5-sonnet",
  OpenRouter_Claude_3_5_Haiku = "anthropic/claude-3.5-haiku",
  OpenRouter_Claude_3_Opus = "anthropic/claude-3-opus",
  OpenRouter_GPT_4o = "openai/gpt-4o",
  OpenRouter_GPT_4o_Mini = "openai/gpt-4o-mini",
  OpenRouter_O1_Preview = "openai/o1-preview",
  OpenRouter_O1_Mini = "openai/o1-mini",
  OpenRouter_Gemini_2_0_Flash = "google/gemini-2.0-flash-exp",
  OpenRouter_Gemini_1_5_Pro = "google/gemini-pro-1.5",
  OpenRouter_Llama_3_1_405B = "meta-llama/llama-3.1-405b-instruct",
  OpenRouter_Llama_3_1_70B = "meta-llama/llama-3.1-70b-instruct",
  OpenRouter_Qwen_2_5_72B = "qwen/qwen-2.5-72b-instruct",
  OpenRouter_DeepSeek_V3 = "deepseek/deepseek-chat",
}

export interface ModelParams {
  modelId: string;
  systemPrompt?: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  reasoning?: boolean;
  json?: boolean;
}

export interface ConverseResponse {
  text?: string;
  cost?: number;
  metadata?: any;
  isComplete?: boolean;
}

export interface ModelDetails {
  name: string;
  provider: AIProviders;
  cost: {
    onDemand: {
      inputTokens: number;
      outputTokens: number;
    };
  };
}

export interface ProviderConfig {
  provider: AIProviders;
  apiKey?: string;
  region?: string;
  accessKeyId?: string;
  secretAccessKey?: string;
  azureEndpoint?: string;
  azureApiVersion?: string;
  azureDeploymentName?: string;
  // Vertex AI specific
  vertexProjectId?: string;
  vertexRegion?: string;
  // OpenRouter specific
  openRouterApiKey?: string;
  openRouterProviderSorting?: string;
}

export interface CompletionSettings {
  enabled: boolean;
  triggerDelay: number; // milliseconds
  maxCompletionLength: number;
  enableMultiLineCompletions: boolean;
  modelPreference?: string;
  contextLines: number; // number of lines to include for context
}

export interface AutoCompactSettings {
  enabled: boolean;
  threshold: number; // 0.95 = 95% of context window
  maxContextTokens: number; // Default context window size
}

export interface StreamingTimeoutSettings {
  enabled: boolean;
  timeoutMinutes: number; // Timeout duration in minutes
}

export interface XyneSettings {
  selectedProvider: AIProviders;
  selectedModel: Models;
  providers: Record<AIProviders, ProviderConfig>;
  completion?: CompletionSettings;
  autoCompletionEnabled?: boolean;
  showAggregateCost?: boolean;
  showCostPerMessage?: boolean;
  autoCompact?: AutoCompactSettings;
  streamingTimeout?: StreamingTimeoutSettings;
}
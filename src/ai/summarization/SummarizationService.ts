import * as vscode from 'vscode';
import { AIService } from '../service';
import { Message } from '../../types/chat';
import { getLogger, Subsystem } from '../../logger';
import type { Message as BedrockMessage } from '@aws-sdk/client-bedrock-runtime';

const Logger = getLogger(Subsystem.AI);

export interface SummarizationProgress {
  stage: 'starting' | 'analyzing' | 'generating' | 'creating' | 'complete';
  message: string;
  progress?: number;
}

export interface SummarizationResult {
  summaryText: string;
  originalMessageCount: number;
  summaryMessageId: string;
  cost: number;
  tokensUsed: {
    input: number;
    output: number;
  };
}

export interface SummarizationOptions {
  sessionId: string;
  messages: Message[];
  onProgress?: (progress: SummarizationProgress) => void;
  signal?: AbortSignal;
}

export class SummarizationService {
  private activeOperations = new Map<string, AbortController>();

  constructor(private aiService: AIService) {}

  /**
   * Summarize a conversation following OpenCode's approach
   */
  async summarizeConversation(options: SummarizationOptions): Promise<SummarizationResult> {
    const { sessionId, messages, onProgress, signal } = options;

    if (messages.length === 0) {
      throw new Error('No messages to summarize');
    }

    // Create abort controller for this operation
    const abortController = new AbortController();
    const combinedSignal = this.createCombinedSignal(signal, abortController.signal);
    
    // Store operation for potential cancellation
    this.activeOperations.set(sessionId, abortController);

    try {
      Logger.info(`🔄 Starting conversation summarization for session: ${sessionId}`);
      
      onProgress?.({
        stage: 'starting',
        message: 'Starting summarization...',
        progress: 0
      });

      // Check if operation was cancelled
      this.checkCancellation(combinedSignal);

      onProgress?.({
        stage: 'analyzing',
        message: 'Analyzing conversation...',
        progress: 25
      });

      // Convert Xyne messages to Bedrock format for AI service
      const bedrockMessages = this.convertToBedrockMessages(messages);
      
      // Add summarization prompt
      const summarizationPrompt = this.createSummarizationPrompt();
      bedrockMessages.push({
        role: 'user',
        content: [{ text: summarizationPrompt }]
      });

      this.checkCancellation(combinedSignal);

      onProgress?.({
        stage: 'generating',
        message: 'Generating summary...',
        progress: 50
      });

      // Use AI service to generate summary
      const response = await this.aiService.converse(bedrockMessages, {
        maxTokens: 2000,
        temperature: 0.1, // Lower temperature for more focused summaries
      });

      this.checkCancellation(combinedSignal);

      if (!response.text || response.text.trim() === '') {
        throw new Error('Empty summary returned from AI service');
      }

      onProgress?.({
        stage: 'creating',
        message: 'Creating summary message...',
        progress: 75
      });

      // Generate unique ID for summary message
      const summaryMessageId = `summary_${Date.now()}`;

      onProgress?.({
        stage: 'complete',
        message: 'Summary complete',
        progress: 100
      });

      Logger.info(`✅ Conversation summarization completed for session: ${sessionId}`);

      return {
        summaryText: response.text.trim(),
        originalMessageCount: messages.length,
        summaryMessageId,
        cost: response.cost || 0,
        tokensUsed: {
          input: response.inputTokens || 0,
          output: response.outputTokens || 0
        }
      };

    } catch (error) {
      if (combinedSignal?.aborted) {
        Logger.info(`📊 Summarization cancelled for session: ${sessionId}`);
        throw new Error('Summarization cancelled by user');
      }
      
      Logger.error(error, `💥 Failed to summarize conversation for session: ${sessionId}`);
      throw error;
    } finally {
      this.activeOperations.delete(sessionId);
    }
  }

  /**
   * Cancel an active summarization operation
   */
  cancelSummarization(sessionId: string): boolean {
    const operation = this.activeOperations.get(sessionId);
    if (operation) {
      operation.abort();
      this.activeOperations.delete(sessionId);
      Logger.info(`🛑 Summarization cancelled for session: ${sessionId}`);
      return true;
    }
    return false;
  }

  /**
   * Check if a session has an active summarization operation
   */
  isSessionBeingSummarized(sessionId: string): boolean {
    return this.activeOperations.has(sessionId);
  }

  /**
   * Get all active summarization operations
   */
  getActiveOperations(): string[] {
    return Array.from(this.activeOperations.keys());
  }

  /**
   * Create OpenCode-style summarization prompt
   */
  private createSummarizationPrompt(): string {
    return `Provide a detailed but concise summary of our conversation above. Focus on information that would be helpful for continuing the conversation, including what we did, what we're doing, which files we're working on, and what we're going to do next.

The summary should:
- Capture the main objectives and goals discussed
- List key files, directories, or code components mentioned
- Summarize important decisions made or approaches taken
- Note any ongoing tasks or next steps
- Preserve context needed for future development work
- Be comprehensive enough that someone could continue the conversation seamlessly

Keep the summary focused and avoid unnecessary details while preserving all context critical for continuing productive development work.`;
  }

  /**
   * Convert Xyne Message format to Bedrock Message format
   */
  private convertToBedrockMessages(messages: Message[]): BedrockMessage[] {
    return messages
      .filter(msg => msg.type === 'user' || msg.type === 'assistant')
      .map(msg => ({
        role: msg.type === 'user' ? 'user' : 'assistant',
        content: [{ text: msg.content }]
      }));
  }

  /**
   * Create a combined AbortSignal from multiple signals
   */
  private createCombinedSignal(...signals: (AbortSignal | undefined)[]): AbortSignal {
    const controller = new AbortController();
    
    for (const signal of signals) {
      if (signal?.aborted) {
        controller.abort();
        break;
      }
      signal?.addEventListener('abort', () => controller.abort());
    }
    
    return controller.signal;
  }

  /**
   * Check if operation was cancelled and throw if so
   */
  private checkCancellation(signal?: AbortSignal): void {
    if (signal?.aborted) {
      throw new Error('Operation cancelled');
    }
  }

  /**
   * Estimate token count for auto-compact triggers
   * This is a rough estimation based on character count
   */
  static estimateTokenCount(messages: Message[]): number {
    const totalChars = messages.reduce((sum, msg) => sum + msg.content.length, 0);
    // Rough estimation: ~4 characters per token for English text
    return Math.ceil(totalChars / 4);
  }

  /**
   * Determine if conversation should be auto-compacted
   */
  static shouldAutoCompact(messages: Message[], maxTokens: number = 200000, threshold: number = 0.95): boolean {
    const estimatedTokens = this.estimateTokenCount(messages);
    console.log("The estimated tokens are ", estimatedTokens);
    return estimatedTokens >= (maxTokens * threshold);
  }
}
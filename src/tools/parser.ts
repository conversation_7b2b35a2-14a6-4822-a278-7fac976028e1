import { ToolCall } from './types';

export class ToolParser {
  static parseToolCalls(text: string): ToolCall[] {
    const toolCalls: ToolCall[] = [];

    // First try XML format (preferred for better LLM compatibility)
    const xmlToolCalls = this.parseXmlToolCalls(text);
    toolCalls.push(...xmlToolCalls);

    // Then try JSON format as fallback
    const jsonToolCalls = this.parseJsonToolCalls(text);
    toolCalls.push(...jsonToolCalls);

    return toolCalls;
  }

  private static parseXmlToolCalls(text: string): ToolCall[] {
    const toolCalls: ToolCall[] = [];

    // Parse XML-style tool calls like <read_file><path>...</path></read_file>
    const xmlToolRegex = /<(\w+)>([\s\S]*?)<\/\1>/g;
    let match;

    while ((match = xmlToolRegex.exec(text)) !== null) {
      const toolName = match[1];
      const toolContent = match[2];

      const parameters: Record<string, any> = {};

      // Parse parameters within the tool content
      const paramRegex = /<(\w+)>([\s\S]*?)<\/\1>/g;
      let paramMatch;

      while ((paramMatch = paramRegex.exec(toolContent)) !== null) {
        const paramName = paramMatch[1];
        const paramValue = paramMatch[2].trim();
        parameters[paramName] = paramValue;
      }

      toolCalls.push({
        id: `${toolName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: toolName,
        parameters
      });
    }

    return toolCalls;
  }

  private static parseJsonToolCalls(text: string): ToolCall[] {
    const toolCalls: ToolCall[] = [];

    // Look for JSON blocks that contain tool_calls
    const jsonBlockRegex = /```json\s*(\{[\s\S]*?\})\s*```/g;
    let match;

    while ((match = jsonBlockRegex.exec(text)) !== null) {
      try {
        const parsed = JSON.parse(match[1]);

        if (parsed.tool_calls && Array.isArray(parsed.tool_calls)) {
          for (const toolCall of parsed.tool_calls) {
            if (toolCall.id && toolCall.name && toolCall.parameters) {
              toolCalls.push({
                id: toolCall.id,
                name: toolCall.name,
                parameters: toolCall.parameters
              });
            }
          }
        }
      } catch (error) {
        // Invalid JSON, skip this block
        continue;
      }
    }

    return toolCalls;
  }

  static containsToolCalls(text: string): boolean {
    return this.parseToolCalls(text).length > 0;
  }

  static stripToolCallsFromText(text: string): string {
    // Remove XML-style tool call blocks
    let cleanText = text.replace(/<(read_file|write_to_file)>([\s\S]*?)<\/\1>/g, '');

    // Remove JSON code blocks that contain tool_calls
    cleanText = cleanText.replace(/```json\s*(\{[\s\S]*?\})\s*```/g, (match, jsonContent) => {
      try {
        const parsed = JSON.parse(jsonContent);
        if (parsed.tool_calls && Array.isArray(parsed.tool_calls)) {
          return ''; // Remove tool call blocks
        }
        return match; // Keep other JSON blocks
      } catch (error) {
        return match; // Keep invalid JSON blocks
      }
    });

    return cleanText.trim();
  }
}

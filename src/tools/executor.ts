import * as vscode from 'vscode';
import * as path from 'path';
import { Tool<PERSON>all, ToolResult } from './types';
import { getLogger, Subsystem } from '../logger';
import { FileAnalyzer } from './parsers/FileAnalyzer';
import { getSupportedExtensions } from './parsers/LanguageParser';
import { McpToolIntegration } from '../mcp/McpToolIntegration';
import { ShadowGitManager } from '../utils/shadowGit';

const Logger = getLogger(Subsystem.Extension);

// Configuration constants
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_SEARCH_RESULTS = 1000;
const DEFAULT_LINE_LIMIT = 2000; // Default max lines to read
const MAX_LINE_LENGTH = 2000; // Max characters per line

export class ToolExecutor {
  private currentWorkspaceOverride?: string;

  constructor(
    private context: vscode.ExtensionContext,
    private shadowGitManager: ShadowGitManager | null,
    private mcpToolIntegration?: McpToolIntegration,
    private onFileChange?: (filePath: string, originalContent: string, newContent: string, toolCallId: string) => void
  ) {}

  // Method to update the shadow Git manager for conversation-specific operations
  updateShadowGitManager(manager: ShadowGitManager): void {
    this.shadowGitManager = manager;
    Logger.info(`🔄 Updated ToolExecutor with new shadow Git manager for conversation: ${manager.getConversationId()}`);
  }

  private generateDiffSummary(originalContent: string, newContent: string, fileExists: boolean): string {
    if (!fileExists) {
      return `Created new file with ${newContent.split('\n').length} lines`;
    }

    const originalLines = originalContent.split('\n');
    const newLines = newContent.split('\n');

    let addedLines = 0;
    let removedLines = 0;
    let changedLines = 0;

    // Simple line-by-line comparison
    const maxLines = Math.max(originalLines.length, newLines.length);

    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[i] || '';
      const newLine = newLines[i] || '';

      if (i >= originalLines.length) {
        addedLines++;
      } else if (i >= newLines.length) {
        removedLines++;
      } else if (originalLine !== newLine) {
        changedLines++;
      }
    }

    const parts = [];
    if (addedLines > 0) {
      parts.push(`+${addedLines} lines`);
    }
    if (removedLines > 0) {
      parts.push(`-${removedLines} lines`);
    }
    if (changedLines > 0) {
      parts.push(`~${changedLines} lines`);
    }

    return parts.length > 0 ? parts.join(', ') : 'No changes detected';
  }

  async executeToolCall(toolCall: ToolCall, webview: vscode.Webview, chatManager?: any, conversationId?: string,workspaceOverride?: string | null): Promise<ToolResult> {
    try {
      // Store workspace override for this execution
      if(workspaceOverride !== null)
      {
        this.currentWorkspaceOverride = workspaceOverride;
      }
      
      Logger.info(`🔧 TOOL EXECUTOR: Executing tool: ${toolCall.name} with parameters: ${JSON.stringify(toolCall.parameters)}`);
      console.log(`🔧 CONSOLE [${new Date().toISOString()}]: Executing tool: ${toolCall.name} with parameters: ${JSON.stringify(toolCall.parameters)}`);

      switch (toolCall.name) {
        case 'read_file':
          return await this.executeReadFile(toolCall);
        case 'write_to_file':
          return await this.executeWriteFile(toolCall);
        case 'multi_edit':
          return await this.executeMultiEdit(toolCall);
        case 'list_files':
          return await this.executeListFiles(toolCall);
        case 'search_files':
          return await this.executeSearchFiles(toolCall);
        case 'get_file_summary':
          return await this.executeGetFileSummary(toolCall);
        case 'execute_command':
          Logger.info(`🔥 EXECUTE_COMMAND case matched - calling executeTerminalCommand`);
          console.log(`🔥 CONSOLE: EXECUTE_COMMAND case matched - calling executeTerminalCommand`);
          return await this.executeTerminalCommand(toolCall, webview, chatManager, conversationId);
        case 'hybrid_search':
          return await this.executeHybridSearch(toolCall);
        case 'attempt_completion':
          return await this.executeAttemptCompletion(toolCall);
        default:
          // Check if this is an MCP tool
          if (this.mcpToolIntegration && this.mcpToolIntegration.isMcpTool(toolCall.name)) {
            Logger.info(`🔧 MCP tool detected: ${toolCall.name}`);
            return await this.executeMcpTool(toolCall);
          }
          
          return {
            id: toolCall.id,
            result: null,
            error: `Unknown tool: ${toolCall.name}`
          };
      }
    } catch (error) {
      Logger.error(error, `Error executing tool ${toolCall.name}`);
      return {
        id: toolCall.id,
        result: null,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    } finally {
      // Clean up workspace override
      this.currentWorkspaceOverride = undefined;
    }
  }

  private async executeMcpTool(toolCall: ToolCall): Promise<ToolResult> {
    if (!this.mcpToolIntegration) {
      return {
        id: toolCall.id,
        result: null,
        error: 'MCP Tool Integration not available'
      };
    }

    try {
      Logger.info(`🔧 Executing MCP tool: ${toolCall.name}`);
      const mcpResult = await this.mcpToolIntegration.executeMcpTool(toolCall.name, toolCall.parameters);
      
      if (mcpResult.success) {
        return {
          id: toolCall.id,
          result: mcpResult.result,
          error: undefined
        };
      } else {
        return {
          id: toolCall.id,
          result: null,
          error: mcpResult.error || 'MCP tool execution failed'
        };
      }
    } catch (error) {
      Logger.error(error, `Error executing MCP tool: ${toolCall.name}`);
      return {
        id: toolCall.id,
        result: null,
        error: error instanceof Error ? error.message : 'Unknown MCP tool error'
      };
    }
  }

  private async executeTerminalCommand(toolCall: ToolCall, webview: vscode.Webview, chatManager?: any, conversationId?: string): Promise<ToolResult> {
    Logger.info(`🚀 executeTerminalCommand called with command: ${JSON.stringify(toolCall.parameters)}`);
    console.log(`🚀 CONSOLE: executeTerminalCommand called with command: ${JSON.stringify(toolCall.parameters)}`);
    const { command, workingDirectory } = toolCall.parameters;

    // Input validation
    if (!command || typeof command !== 'string') {
      Logger.error(`❌ Invalid command parameter: ${command}`);
      return {
        id: toolCall.id,
        result: null,
        error: 'Invalid command parameter - must be a non-empty string'
      };
    }

    const workspaceUri = this.getWorkspaceUri(this.currentWorkspaceOverride);
    if (!workspaceUri) {
      return {
        id: toolCall.id,
        result: null,
        error: 'No workspace folder available'
      };
    }

    // Determine working directory and create the display command
    let cwd = workspaceUri.fsPath;
    if (workingDirectory) {
      if (typeof workingDirectory !== 'string') {
        return {
          id: toolCall.id,
          result: null,
          error: 'Invalid workingDirectory parameter - must be a string'
        };
      }

      const resolvedWorkingDir = this.validateAndResolvePath(workingDirectory, workspaceUri);
      if (!resolvedWorkingDir) {
        return {
          id: toolCall.id,
          result: null,
          error: 'Invalid working directory - path must be within workspace'
        };
      }
      cwd = resolvedWorkingDir.fsPath;
    }

    try {
      Logger.info(`🖥️ Executing command: ${command} in ${cwd}`);

      // Create the display command (include cd if workingDirectory is specified)
      const displayCommand = workingDirectory ? `cd ${workingDirectory} && ${command}` : command;

      // PERSISTENCE: If chatManager and conversationId are provided, create a terminal message in the backend
      if (chatManager && conversationId) {
        const terminalMessage = {
          id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
          type: 'terminal',
          content: displayCommand,
          metadata: {},
          sessionId: `term_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };
        await chatManager.addMessage(conversationId, terminalMessage);
      }

      // Send createTerminalBubble message to webview for in-chat terminal
      Logger.info(`🚀 ABOUT TO SEND createTerminalBubble message for command: ${displayCommand}`);
      console.log(`🚀 CONSOLE: ABOUT TO SEND createTerminalBubble message for command: ${displayCommand}`);
      Logger.info(`🚀 Webview object: ${webview ? 'EXISTS' : 'NULL'}`);
      
      webview.postMessage({
        type: 'createTerminalBubble',
        command: displayCommand
      });

      Logger.info(`🎯 SENT createTerminalBubble message for command: ${displayCommand}`);
      console.log(`🎯 CONSOLE: SENT createTerminalBubble message for command: ${displayCommand}`);

      // Return success result
      return {
        id: toolCall.id,
        result: {
          command: displayCommand,
          terminalCreated: true,
          message: `Terminal bubble created and executing: ${displayCommand}`,
          workingDirectory: cwd
        }
      };

    } catch (error) {
      Logger.error(error, `Error executing command: ${command}`);
      return {
        id: toolCall.id,
        result: null,
        error: `Error executing command: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }


  /**
   * Validates and resolves a file path within the workspace using VS Code URI
   */
  private validateAndResolvePath(filePath: string, workspaceUri: vscode.Uri): vscode.Uri | null {
    try {
      // Handle relative paths
      let resolvedUri: vscode.Uri;
      if (vscode.Uri.file(filePath).scheme === 'file' && this.isAbsolutePath(filePath)) {
        resolvedUri = vscode.Uri.file(filePath);
      } else {
        // Join with workspace URI for relative paths
        resolvedUri = vscode.Uri.joinPath(workspaceUri, filePath);
      }

      // Ensure the resolved URI is within the workspace
      const workspacePath = workspaceUri.fsPath;
      const resolvedPath = resolvedUri.fsPath;

      if (!resolvedPath.startsWith(workspacePath)) {
        Logger.warn(`Path traversal attempt blocked: ${filePath}`);
        return null;
      }

      return resolvedUri;
    } catch (error) {
      Logger.error(error, `Failed to resolve path: ${filePath}`);
      return null;
    }
  }

  /**
   * Check if a path is absolute (cross-platform)
   */
  private isAbsolutePath(filePath: string): boolean {
    // Windows: C:\ or C:/ style paths
    if (/^[a-zA-Z]:[\\\/]/.test(filePath)) {
      return true;
    }
    // Unix/Linux: starts with /
    if (filePath.startsWith('/')) {
      return true;
    }
    return false;
  }

  /**
   * Gets the workspace root URI
   */
  private getWorkspaceUri(workspaceOverride?: string): vscode.Uri | null {
    if (workspaceOverride) {
      Logger.info(`🔄 Using workspace override: ${workspaceOverride}`);
      return vscode.Uri.file(workspaceOverride);
    }
    
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
      Logger.warn('No workspace folder available');
      return null;
    }
    Logger.info(`🏠 Using default workspace: ${workspaceFolder.uri.fsPath}`);
    return workspaceFolder.uri;
  }

  private async executeReadFile(toolCall: ToolCall): Promise<ToolResult> {
    const { path: filePath, startLine, lineCount } = toolCall.parameters;

    // Input validation
    if (!filePath || typeof filePath !== 'string') {
      return {
        id: toolCall.id,
        result: null,
        error: 'Invalid file path parameter - must be a non-empty string'
      };
    }

    const workspaceUri = this.getWorkspaceUri(this.currentWorkspaceOverride);
    if (!workspaceUri) {
      return {
        id: toolCall.id,
        result: null,
        error: 'No workspace folder available'
      };
    }

    const resolvedUri = this.validateAndResolvePath(filePath, workspaceUri);
    if (!resolvedUri) {
      return {
        id: toolCall.id,
        result: null,
        error: 'Invalid file path - path must be within workspace'
      };
    }

    try {
      // Check if file exists and get stats
      const stat = await vscode.workspace.fs.stat(resolvedUri);

      if (stat.size > MAX_FILE_SIZE) {
        return {
          id: toolCall.id,
          result: null,
          error: `File too large (${stat.size} bytes). Maximum size is ${MAX_FILE_SIZE} bytes`
        };
      }

      // Read file using VS Code API
      const content = await vscode.workspace.fs.readFile(resolvedUri);
      const contentStr = Buffer.from(content).toString('utf8');
      
      // Apply smart chunking logic with optional section reading
      const chunkedResult = this.applySmartChunking(contentStr, filePath, stat.size, startLine, lineCount);
      
      Logger.info(`📖 Successfully read file: ${filePath} (${contentStr.length} characters, ${chunkedResult.truncated ? 'truncated' : 'complete'})`);

      return {
        id: toolCall.id,
        result: {
          path: filePath,
          content: chunkedResult.content,
          size: stat.size,
          lastModified: stat.mtime,
          truncated: chunkedResult.truncated,
          totalLines: chunkedResult.totalLines,
          displayedLines: chunkedResult.displayedLines,
          startLine: chunkedResult.startLine,
          endLine: chunkedResult.endLine,
          suggestions: chunkedResult.suggestions
        }
      };
    } catch (error) {
      const errorMessage = error instanceof vscode.FileSystemError
        ? this.getFileSystemErrorMessage(error)
        : (error instanceof Error ? error.message : 'Unknown error');

      return {
        id: toolCall.id,
        result: null,
        error: `Failed to read file '${filePath}': ${errorMessage}`
      };
    }
  }

  private async executeWriteFile(toolCall: ToolCall): Promise<ToolResult> {
    const { path: filePath, content } = toolCall.parameters;

    // Input validation
    if (!filePath || typeof filePath !== 'string') {
      return {
        id: toolCall.id,
        result: null,
        error: 'Invalid file path parameter - must be a non-empty string'
      };
    }

    if (content === undefined || content === null) {
      return {
        id: toolCall.id,
        result: null,
        error: 'Invalid content parameter - must be provided'
      };
    }

    try {
      Logger.info(`📝 Executing write_file tool call: ${toolCall.id}`);
      Logger.info(`📁 File path: ${filePath}`);
      Logger.info(`📊 Content length: ${typeof content === 'string' ? content.length : 'binary'}`);
      
      // Resolve the file path relative to workspace
      const workspaceUri = this.getWorkspaceUri(this.currentWorkspaceOverride);
      if (!workspaceUri) {
        Logger.error('❌ No workspace folder available');
        return {
          id: toolCall.id,
          result: null,
          error: 'No workspace folder available'
        };
      }

      const resolvedUri = vscode.Uri.joinPath(workspaceUri, filePath);
      Logger.info(`🔗 Resolved URI: ${resolvedUri.fsPath}`);

      // Read original content for comparison and shadow Git tracking
      let originalContent = '';
      try {
        const originalBuffer = await vscode.workspace.fs.readFile(resolvedUri);
        originalContent = Buffer.from(originalBuffer).toString('utf8');
        Logger.info(`📖 Original content read: ${originalContent.length} characters`);
      } catch (error) {
        Logger.info(`📄 File does not exist, will create new file: ${filePath}`);
        originalContent = '';
      }

      // Strip the EOF marker if present and convert content to buffer
      let cleanContent = content;
      if (typeof content === 'string') {
        cleanContent = content.replace(/<<<EOF_FILE>>>\s*$/, '').trim();
      }
      
      const contentBuffer = typeof cleanContent === 'string' 
        ? Buffer.from(cleanContent, 'utf8') 
        : Buffer.from(cleanContent);
      
      Logger.info(`📝 Writing content to file: ${filePath}`);
      
      // Ensure parent directory exists
      const parentDir = vscode.Uri.joinPath(resolvedUri, '..');
      try {
        await vscode.workspace.fs.createDirectory(parentDir);
        Logger.info(`📁 Created parent directory: ${parentDir.fsPath}`);
      } catch (error) {
        // Directory might already exist, which is fine
        Logger.info(`📁 Parent directory exists or creation failed: ${parentDir.fsPath}`);
      }
      
      // Write the file
      await vscode.workspace.fs.writeFile(resolvedUri, contentBuffer);
      Logger.info(`✅ File written successfully: ${filePath}`);

      // Skip Shadow Git sync if using workspace override (test mode)
      if (!this.currentWorkspaceOverride) {
        // Use ShadowGitManager to track and commit the file change
        const relativePath = vscode.workspace.asRelativePath(resolvedUri);
        const changeId = toolCall.id;
        
        Logger.info(`🔄 Syncing file to shadow Git: ${relativePath} (changeId: ${changeId})`);
        if (this.shadowGitManager) {
          await this.shadowGitManager.syncFile(
            resolvedUri.fsPath,
            relativePath,
            originalContent,
            cleanContent,
            changeId
          );
          Logger.info(`✅ File synced to shadow Git: ${relativePath}`);
        } else {
          Logger.warn(`⚠️ Shadow Git manager not available, skipping sync for: ${filePath}`);
        }
      } else {
        Logger.info(`🧪 Test mode: Skipping Shadow Git sync for: ${filePath}`);
      }

      // Generate diff summary
      const diffSummary = this.generateDiffSummary(originalContent, cleanContent, true);
      Logger.info(`📊 Diff summary: ${diffSummary}`);

      // Call the file change callback if provided
      if (this.onFileChange) {
        Logger.info(`📞 Calling file change callback for: ${filePath}`);
        this.onFileChange(filePath, originalContent, cleanContent, toolCall.id);
      }

      Logger.info(`🎉 write_file tool execution completed: ${filePath}`);

      return {
        id: toolCall.id,
        result: {
          path: filePath,
          bytesWritten: contentBuffer.length,
          originalContent: originalContent,
          newContent: cleanContent,
          diffSummary: diffSummary,
          staged: true // Indicate this was tracked via ShadowGit
        }
      };

    } catch (error) {
      Logger.error(error, `Failed to execute write_file tool: ${filePath}`);
      return {
        id: toolCall.id,
        result: null,
        error: `Failed to write file: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private async executeMultiEdit(toolCall: ToolCall): Promise<ToolResult> {
    let { path: filePath, edits } = toolCall.parameters;

    // Input validation
    if (!filePath || typeof filePath !== 'string') {
      return {
        id: toolCall.id,
        result: null,
        error: 'Invalid file path parameter - must be a non-empty string'
      };
    }

    // Auto-parse JSON string to array if needed
    if (typeof edits === 'string') {
      try {
        edits = JSON.parse(edits);
        Logger.info(`🔄 Auto-parsed edits JSON string`);
      } catch (e) {
        return {
          id: toolCall.id,
          result: null,
          error: 'Invalid JSON in edits parameter. Expected valid JSON array.'
        };
      }
    }

    if (!edits || !Array.isArray(edits) || edits.length === 0) {
      return {
        id: toolCall.id,
        result: null,
        error: 'Invalid edits parameter - must be a non-empty array'
      };
    }

    // Validate each edit
    for (let i = 0; i < edits.length; i++) {
      const edit = edits[i];
      if (!edit.find || typeof edit.find !== 'string') {
        return {
          id: toolCall.id,
          result: null,
          error: `Invalid find parameter in edit ${i + 1} - must be a non-empty string`
        };
      }
      if (edit.replace === undefined || edit.replace === null) {
        return {
          id: toolCall.id,
          result: null,
          error: `Invalid replace parameter in edit ${i + 1} - must be provided`
        };
      }
    }

    try {
      Logger.info(`🔄 Executing multi_edit tool call: ${toolCall.id}`);
      Logger.info(`📁 File path: ${filePath}`);
      Logger.info(`📊 Number of edits: ${edits.length}`);
      
      // Resolve the file path relative to workspace
      const workspaceUri = this.getWorkspaceUri(this.currentWorkspaceOverride);
      if (!workspaceUri) {
        Logger.error('❌ No workspace folder available');
        return {
          id: toolCall.id,
          result: null,
          error: 'No workspace folder available'
        };
      }

      const resolvedUri = vscode.Uri.joinPath(workspaceUri, filePath);
      Logger.info(`🔗 Resolved URI: ${resolvedUri.fsPath}`);

      // Read the file content
      const fileBuffer = await vscode.workspace.fs.readFile(resolvedUri);
      const originalContent = Buffer.from(fileBuffer).toString('utf8');
      Logger.info(`📖 Original content read: ${originalContent.length} characters`);

      // Apply edits sequentially
      let currentContent = originalContent;
      let totalReplacements = 0;
      const editResults = [];

      for (let i = 0; i < edits.length; i++) {
        const edit = edits[i];
        const { find, replace, replaceAll = false, wordBoundary = false, caseSensitive = true } = edit;
        
        Logger.info(`🔄 Applying edit ${i + 1}: "${find}" → "${replace}" (replaceAll: ${replaceAll}, wordBoundary: ${wordBoundary})`);
        
        // Safety check: Prevent dangerous replacements that could corrupt code
        if (!wordBoundary && find.length <= 3 && /^[a-zA-Z]+$/.test(find)) {
          Logger.warn(`⚠️ Dangerous replacement detected: "${find}" without wordBoundary. This could corrupt code. Enabling wordBoundary automatically.`);
          edit.wordBoundary = true;
        }
        
        // Build replacement regex with proper escaping and options
        let escapedFind = find.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // Escape regex special chars
        
        // Add word boundaries if requested or auto-enabled
        if (wordBoundary || edit.wordBoundary) {
          escapedFind = `\\b${escapedFind}\\b`;
        }
        
        // Build regex flags
        let flags = caseSensitive ? '' : 'i';
        if (replaceAll) {
          flags += 'g';
        }
        
        const regex = new RegExp(escapedFind, flags);
        
        // Count occurrences before replacement
        const beforeMatches = currentContent.match(new RegExp(escapedFind, 'gi')) || [];
        const replacementCount = beforeMatches.length;
        
        // Store content before this edit for corruption checking
        const beforeEdit = currentContent;
        
        // Perform the replacement
        currentContent = currentContent.replace(regex, replace);
        
        // Post-replacement corruption detection
        const corruptionPatterns = [
          /[a-z][A-Z][a-z][A-Z]/g, // merged camelCase
          /\}\{/g, // merged braces
          /\)\(/g, // merged parentheses
          /"[^"]*[a-z][A-Z]/g, // corrupted strings
          /`[^`]*[a-z][A-Z]/g // corrupted template literals
        ];
        
        // Check for keyword corruption separately to exclude JSX attributes
        const keywordMatches = currentContent.match(/\b(import|export|const|let|var|function|class|interface|type)[A-Z][a-z]/g) || [];
        const legitimateKeywordMatches = keywordMatches.filter(match => {
          const legitimatePatterns = ['classNa', 'classTy', 'classList', 'classTyp'];
          return !legitimatePatterns.some(pattern => pattern.startsWith(match));
        });
        const beforeKeywordMatches = beforeEdit.match(/\b(import|export|const|let|var|function|class|interface|type)[A-Z][a-z]/g) || [];
        const beforeLegitimateKeywordMatches = beforeKeywordMatches.filter(match => {
          const legitimatePatterns = ['classNa', 'classTy', 'classList', 'classTyp'];
          return !legitimatePatterns.some(pattern => pattern.startsWith(match));
        });
        
        const keywordCorruption = legitimateKeywordMatches.length > beforeLegitimateKeywordMatches.length;
        
        const corruption = keywordCorruption || corruptionPatterns.some(pattern => {
          const matches = currentContent.match(pattern);
          return matches && matches.length > (beforeEdit.match(pattern) || []).length;
        });
        
        if (corruption) {
          Logger.error(`❌ Code corruption detected after edit ${i + 1}. Rolling back this edit.`);
          currentContent = beforeEdit;
          editResults.push({
            find,
            replace,
            replaceAll,
            wordBoundary: wordBoundary || edit.wordBoundary,
            caseSensitive,
            replacements: 0,
            error: 'Edit rolled back due to corruption detection'
          });
          continue;
        }
        
        totalReplacements += replaceAll ? replacementCount : Math.min(replacementCount, 1);
        
        editResults.push({
          find,
          replace,
          replaceAll,
          wordBoundary: wordBoundary || edit.wordBoundary,
          caseSensitive,
          replacements: replaceAll ? replacementCount : Math.min(replacementCount, 1)
        });
        
        Logger.info(`✅ Edit ${i + 1} completed: ${replaceAll ? replacementCount : Math.min(replacementCount, 1)} replacement(s) made`);
      }

      Logger.info(`🔄 All edits applied: ${totalReplacements} total replacement(s) made`);

      // Final corruption check - exclude legitimate JSX attributes like className
      const corruptionMatches = currentContent.match(/\b(import|export|const|let|var|function|class)[A-Z][a-z]/g) || [];
      const finalCorruption = corruptionMatches.filter(match => {
        // Exclude legitimate JSX/HTML attributes by checking if match is start of legitimate pattern
        const legitimatePatterns = ['classNa', 'classTy', 'classList', 'classTyp'];
        return !legitimatePatterns.some(pattern => pattern.startsWith(match));
      });
      if (finalCorruption.length > 0) {
        Logger.error(`❌ Final corruption detected in multi_edit: ${finalCorruption.join(', ')}`);
        return {
          id: toolCall.id,
          result: null,
          error: `Code corruption detected: merged identifiers like ${finalCorruption.slice(0, 3).join(', ')}. Use wordBoundary=true for variable/function renaming.`
        };
      }

      if (totalReplacements === 0) {
        Logger.warn(`⚠️ No replacements made in file: ${filePath}`);
        return {
          id: toolCall.id,
          result: {
            path: filePath,
            totalReplacements: 0,
            editResults,
            originalContent: originalContent,
            newContent: currentContent,
            diffSummary: 'No replacements made - no patterns found',
            staged: false
          }
        };
      }

      // Write the updated content back to the file
      const contentBuffer = Buffer.from(currentContent, 'utf8');
      await vscode.workspace.fs.writeFile(resolvedUri, contentBuffer);
      Logger.info(`✅ Updated content written to file: ${filePath}`);

      // Skip Shadow Git sync if using workspace override (test mode)
      if (!this.currentWorkspaceOverride) {
        // Use ShadowGitManager to track and commit the file change
        const relativePath = vscode.workspace.asRelativePath(resolvedUri);
        const changeId = toolCall.id;
        
        Logger.info(`🔄 Syncing file to shadow Git: ${relativePath} (changeId: ${changeId})`);
        if (this.shadowGitManager) {
          await this.shadowGitManager.syncFile(
            resolvedUri.fsPath,
            relativePath,
            originalContent,
            currentContent,
            changeId
          );
          Logger.info(`✅ File synced to shadow Git: ${relativePath}`);
        } else {
          Logger.warn(`⚠️ Shadow Git manager not available, skipping sync for: ${filePath}`);
        }
      } else {
        Logger.info(`🧪 Test mode: Skipping Shadow Git sync for: ${filePath}`);
      }

      // Generate diff summary
      const diffSummary = this.generateDiffSummary(originalContent, currentContent, true);
      Logger.info(`📊 Diff summary: ${diffSummary}`);

      // Call the file change callback if provided
      if (this.onFileChange) {
        Logger.info(`📞 Calling file change callback for: ${filePath}`);
        this.onFileChange(filePath, originalContent, currentContent, toolCall.id);
      }

      Logger.info(`🎉 multi_edit tool execution completed: ${filePath}`);

      return {
        id: toolCall.id,
        result: {
          path: filePath,
          totalReplacements: totalReplacements,
          editResults,
          originalContent: originalContent,
          newContent: currentContent,
          diffSummary: diffSummary,
          staged: true // Indicate this was tracked via ShadowGit
        }
      };

    } catch (error) {
      Logger.error(error, `Failed to execute multi_edit tool: ${filePath}`);
      return {
        id: toolCall.id,
        result: null,
        error: `Failed to apply multiple edits to file: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private async executeListFiles(toolCall: ToolCall): Promise<ToolResult> {
    const { path: dirPath = '.', recursive = false, pattern = "**" } = toolCall.parameters;

    const workspaceUri = this.getWorkspaceUri(this.currentWorkspaceOverride);
    if (!workspaceUri) {
      return {
        id: toolCall.id,
        result: null,
        error: 'No workspace folder available'
      };
    }

    try {
      // Convert pattern to VS Code glob pattern
      let globPattern = pattern;
      if (recursive) {
        globPattern = `**/${pattern}`;
      }

      // If dirPath is not root, prepend it
      if (dirPath !== '.' && dirPath !== '/') {
        globPattern = path.join(dirPath, globPattern);
      }

      // Use VS Code's findFiles API
      const files = await vscode.workspace.findFiles(
        globPattern,
        '{**/node_modules/**,**/.git/**,**/.vscode/**,**/dist/**,**/build/**}',
        MAX_SEARCH_RESULTS
      );

      console.log(`📂 Found ${files.length} files matching pattern '${globPattern}'`);

      const filteredFiles = files.filter(file => {
        const relativePath = vscode.workspace.asRelativePath(file);
        const shouldExclude = this.shouldExcludeFile(relativePath);
        return !shouldExclude;
      });

      const fileList = filteredFiles.map(file => ({
        path: vscode.workspace.asRelativePath(file),
        type: 'file' as const
      }));

      return {
        id: toolCall.id,
        result: {
          files: fileList
        }
      };

    } catch (error) {
      return {
        id: toolCall.id,
        result: null,
        error: `Failed to list files: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private async executeSearchFiles(toolCall: ToolCall): Promise<ToolResult> {
    const {
      pattern,
      filePattern = '**/*.{js,ts,jsx,tsx,py,java,c,cpp,h,hpp,cs,rb,go,rs,php,html,css,json,xml,md,txt,purs}',
      caseSensitive = true,
      maxResults = 100,
      searchInFilenames = false
    } = toolCall.parameters;

    if (!pattern || typeof pattern !== 'string') {
      return {
        id: toolCall.id,
        result: null,
        error: 'Invalid search pattern parameter - must be a non-empty string'
      };
    }

    const workspaceUri = this.getWorkspaceUri(this.currentWorkspaceOverride);
    if (!workspaceUri) {
      return {
        id: toolCall.id,
        result: null,
        error: 'No workspace folder available'
      };
    }

    try {
      // Validate regex pattern
      let searchRegex: RegExp;
      try {
        searchRegex = new RegExp(pattern, caseSensitive ? 'g' : 'gi');
        Logger.info(`🔍 Using regex pattern: /${pattern}/${caseSensitive ? 'g' : 'gi'}`);
      } catch (error) {
        return {
          id: toolCall.id,
          result: null,
          error: 'Invalid regex pattern'
        };
      }

      // Use the filePattern parameter to find the right files, not the search pattern
      const files = await vscode.workspace.findFiles(
        filePattern,
        '{**/node_modules/**,**/.git/**,**/.vscode/**,**/dist/**,**/build/**}',
        MAX_SEARCH_RESULTS
      );

      Logger.info(`🔍 Found ${files.length} files with pattern '${filePattern}' before filtering`);
      Logger.info(`🔍 Sample files found: ${files.slice(0, 5).map(f => vscode.workspace.asRelativePath(f)).join(', ')}`);

      // Additional filtering to ensure excluded directories are not included
      const filteredFiles = files.filter(file => {
        const relativePath = vscode.workspace.asRelativePath(file);
        const shouldExclude = this.shouldExcludeFile(relativePath);
        return !shouldExclude;
      });

      Logger.info(`🔍 ${filteredFiles.length} files after filtering`);
      Logger.info(`🔍 Sample filtered files: ${filteredFiles.slice(0, 5).map(f => vscode.workspace.asRelativePath(f)).join(', ')}`);

      // If searching in filenames, filter by filename pattern
      if (searchInFilenames) {
        const filenameRegex = new RegExp(pattern, caseSensitive ? 'g' : 'gi');
        const filenameMatches = filteredFiles.filter(file => {
          const fileName = this.getBaseName(file.fsPath);
          const relativePath = vscode.workspace.asRelativePath(file);
          return filenameRegex.test(fileName) || filenameRegex.test(relativePath);
        });

        return {
          id: toolCall.id,
          result: {
            pattern,
            searchType: 'filename',
            results: filenameMatches.map(file => ({
              file: vscode.workspace.asRelativePath(file),
              matches: [{ text: `Filename match: ${this.getBaseName(file.fsPath)}`, line: 0, column: 0 }]
            })),
            totalMatches: filenameMatches.length,
            truncated: false
          }
        };
      }

      const searchResults: Array<{
        file: string;
        matches: Array<{ text: string; line: number; column: number; }>;
      }> = [];

      let totalMatches = 0;

      for (const file of filteredFiles) {
        if (totalMatches >= maxResults) {
          break;
        }

        try {
          const stat = await vscode.workspace.fs.stat(file);
          if (stat.size > MAX_FILE_SIZE) {
            Logger.warn(`Skipping large file: ${file.fsPath} (${stat.size} bytes)`);
            continue;
          }

          const content = await vscode.workspace.fs.readFile(file);
          const text = Buffer.from(content).toString('utf8');
          const lines = text.split('\n');

          const fileMatches: Array<{ text: string; line: number; column: number }> = [];

          for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            let match;
            searchRegex.lastIndex = 0; // Reset regex state

            while ((match = searchRegex.exec(line)) !== null) {
              if (totalMatches >= maxResults) {
                break;
              }

              Logger.debug(`📍 Found match in ${vscode.workspace.asRelativePath(file)}:${i + 1} - "${line.trim()}"`);

              fileMatches.push({
                text: line.trim(),
                line: i + 1,
                column: match.index + 1
              });
              totalMatches++;

              // Prevent infinite loop on zero-length matches
              if (match.index === searchRegex.lastIndex) {
                searchRegex.lastIndex++;
              }
            }

            if (totalMatches >= maxResults) {
              break;
            }
          }

          if (fileMatches.length > 0) {
            searchResults.push({
              file: vscode.workspace.asRelativePath(file),
              matches: fileMatches
            });
          }
        } catch (error) {
          Logger.warn(`Failed to search file ${file.fsPath}: ${error}`);
          continue;
        }
      }

      Logger.info(`🔍 Found ${totalMatches} matches across ${searchResults.length} files for pattern: '${pattern}'`);

      return {
        id: toolCall.id,
        result: {
          pattern,
          searchType: 'content',
          results: searchResults,
          totalMatches,
          truncated: totalMatches >= maxResults,
          filesSearched: filteredFiles.length
        }
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        id: toolCall.id,
        result: null,
        error: `Search failed: ${errorMessage}`
      };
    }
  }

  /**
   * Get the base name of a file path (cross-platform)
   */
  private getBaseName(filePath: string): string {
    const lastSlash = Math.max(filePath.lastIndexOf('/'), filePath.lastIndexOf('\\'));
    return lastSlash >= 0 ? filePath.substring(lastSlash + 1) : filePath;
  }

  /**
   * Check if a file path should be excluded from operations
   */
  private shouldExcludeFile(filePath: string): boolean {
    const normalizedPath = filePath.toLowerCase().replace(/\\/g, '/');

    const excludePatterns = [
      '/node_modules/',
      '\\node_modules\\',
      '/.git/',
      '\\.git\\',
      '/.vscode/',
      '\\.vscode\\',
      '/dist/',
      '\\dist\\',
      '/build/',
      '\\build\\',
      '/target/',
      '\\target\\',
      '/bin/',
      '\\bin\\',
      '/obj/',
      '\\obj\\',
      '/__pycache__/',
      '\\__pycache__\\',
      '/vendor/',
      '\\vendor\\',
      '.min.js',
      '.min.css',
      '.pyc',
      '.class',
      '.o',
      '.exe',
      '.dll',
      '.so',
      '.dylib',
      '.spago',
      '/spago/',
      '\\spago\\',
      'output/',
      '\\output\\',

    ];

    return excludePatterns.some(pattern => normalizedPath.includes(pattern)) ||
           normalizedPath.startsWith('node_modules/') ||
           normalizedPath.startsWith('node_modules\\');
  }

  private async executeGetFileSummary(toolCall: ToolCall): Promise<ToolResult> {
    const { path: filePath } = toolCall.parameters;

    // Input validation
    if (!filePath || typeof filePath !== 'string') {
      return {
        id: toolCall.id,
        result: null,
        error: 'Invalid file path parameter - must be a non-empty string'
      };
    }

    // Check if file extension is supported
    const ext = path.extname(filePath).toLowerCase().slice(1);
    const supportedExtensions = getSupportedExtensions();

    if (!supportedExtensions.includes(ext)) {
      return {
        id: toolCall.id,
        result: null,
        error: `Unsupported file type: ${ext}. Supported types: ${supportedExtensions.join(', ')}`
      };
    }

    const workspaceUri = this.getWorkspaceUri(this.currentWorkspaceOverride);
    if (!workspaceUri) {
      return {
        id: toolCall.id,
        result: null,
        error: 'No workspace folder available'
      };
    }

    const resolvedUri = this.validateAndResolvePath(filePath, workspaceUri);
    if (!resolvedUri) {
      return {
        id: toolCall.id,
        result: null,
        error: 'Invalid file path - path must be within workspace'
      };
    }

    try {
      // Check file size
      const stat = await vscode.workspace.fs.stat(resolvedUri);
      if (stat.size > MAX_FILE_SIZE) {
        return {
          id: toolCall.id,
          result: null,
          error: `File too large (${stat.size} bytes). Maximum size is ${MAX_FILE_SIZE} bytes`
        };
      }

      // Read file content
      const content = await vscode.workspace.fs.readFile(resolvedUri);
      const textContent = new TextDecoder().decode(content);

      // Initialize analyzer and parse file
      const analyzer = new FileAnalyzer();
      await analyzer.initialize([filePath], this.context);

      const summary = await analyzer.analyzeFile(filePath, textContent);

      Logger.info(`📊 Successfully analyzed ${filePath}: ${summary.definitions.functions.length} functions, ${summary.definitions.classes.length} classes, ${summary.definitions.interfaces.length} interfaces`);

      return {
        id: toolCall.id,
        result: {
          ...summary,
          message: `Analyzed ${summary.definitions.functions.length} functions, ${summary.definitions.classes.length} classes, ${summary.definitions.interfaces.length} interfaces, ${summary.definitions.types.length} types in ${filePath}`
        }
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        id: toolCall.id,
        result: null,
        error: `Failed to analyze file '${filePath}': ${errorMessage}`
      };
    }
  }

  /**
   * Apply smart chunking logic to file content
   */
  private applySmartChunking(
    content: string, 
    _filePath: string, 
    fileSize: number,
    startLine?: number,
    lineCount?: number
  ): {
    content: string;
    truncated: boolean;
    totalLines: number;
    displayedLines: number;
    startLine: number;
    endLine: number;
    suggestions: string[];
  } {
    const lines = content.split('\n');
    const totalLines = lines.length;
    
    // Handle section reading if parameters provided
    if (startLine !== undefined) {
      const start = Math.max(1, Math.min(startLine, totalLines)) - 1; // Convert to 0-based
      const count = lineCount || DEFAULT_LINE_LIMIT;
      const end = Math.min(start + count, totalLines);
      
      let processedLines = lines.slice(start, end);
      
      // Truncate long lines
      processedLines = processedLines.map(line => 
        line.length > MAX_LINE_LENGTH 
          ? line.substring(0, MAX_LINE_LENGTH) + '... [line truncated]'
          : line
      );
      
      const sectionContent = processedLines.join('\n');
      const actualStart = start + 1; // Convert back to 1-based
      const actualEnd = end;
      
      const suggestions = [];
      if (actualEnd < totalLines) {
        suggestions.push(`Showing lines ${actualStart}-${actualEnd} of ${totalLines}. Use read_file with startLine=${actualEnd + 1} for more.`);
      }
      if (actualStart > 1) {
        suggestions.push(`Previous section available: read_file with startLine=${Math.max(1, actualStart - count)} lineCount=${count}.`);
      }
      
      return {
        content: sectionContent,
        truncated: actualEnd < totalLines || actualStart > 1,
        totalLines,
        displayedLines: processedLines.length,
        startLine: actualStart,
        endLine: actualEnd,
        suggestions
      };
    }
    
    // Default behavior: show from beginning with chunking
    const effectiveLineCount = lineCount || DEFAULT_LINE_LIMIT;
    
    // If file is small enough, return as-is
    if (lines.length <= effectiveLineCount && fileSize < MAX_FILE_SIZE) {
      return {
        content: content,
        truncated: false,
        totalLines,
        displayedLines: totalLines,
        startLine: 1,
        endLine: totalLines,
        suggestions: []
      };
    }
    
    // Apply line limit and truncation
    let processedLines = lines.slice(0, effectiveLineCount);
    
    // Truncate long lines
    processedLines = processedLines.map(line => 
      line.length > MAX_LINE_LENGTH 
        ? line.substring(0, MAX_LINE_LENGTH) + '... [line truncated]'
        : line
    );
    
    const truncatedContent = processedLines.join('\n');
    const displayedLines = processedLines.length;
    
    // Generate helpful suggestions
    const suggestions = [];
    if (totalLines > effectiveLineCount) {
      suggestions.push(`File has ${totalLines} lines, showing first ${displayedLines}. Use read_file with startLine=${displayedLines + 1} for more.`);
    }
    if (fileSize > 1024 * 1024) {
      suggestions.push(`Large file (${Math.round(fileSize / 1024 / 1024 * 10) / 10}MB). Consider using search_files to find specific content.`);
    }
    
    return {
      content: truncatedContent,
      truncated: true,
      totalLines,
      displayedLines,
      startLine: 1,
      endLine: displayedLines,
      suggestions
    };
  }

  private getFileSystemErrorMessage(error: vscode.FileSystemError): string {
    switch (error.code) {
      case 'FileNotFound':
        return 'File not found';
      case 'FileExists':
        return 'File already exists';
      case 'NoPermissions':
        return 'Permission denied';
      case 'Unavailable':
        return 'File is unavailable';
      default:
        return error.message;
    }
  }

  private async executeHybridSearch(toolCall: ToolCall): Promise<ToolResult> {
    try {
      const { query } = toolCall.parameters;

      if (!query || typeof query !== 'string') {
        return {
          id: toolCall.id,
          result: null,
          error: 'Invalid query parameter - must be a non-empty string'
        };
      }

      const workspaceUri = this.getWorkspaceUri(this.currentWorkspaceOverride);
      if (!workspaceUri) {
        return {
          id: toolCall.id,
          result: null,
          error: 'No workspace folder available'
        };
      }

      // Import the hybrid search function
      const { queryHybrid } = await import('../embedding/hybrid-search.js');
      
      // Get the extension path from the context
      const extensionPath = this.context.extensionPath;
      
      // Execute the hybrid query (will automatically choose BM25 or Qwen)
      const results = await queryHybrid(query, workspaceUri.fsPath, extensionPath);

      return {
        id: toolCall.id,
        result: {
          query,
          results,
          count: results.length
        }
      };

    } catch (error) {
      Logger.error(error, `Error performing hybrid search: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
        id: toolCall.id,
        result: null,
        error: `Error performing hybrid search: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private async executeAttemptCompletion(toolCall: ToolCall): Promise<ToolResult> {
    const { result, command } = toolCall.parameters;

    // Input validation
    if (!result || typeof result !== 'string') {
      return {
        id: toolCall.id,
        result: null,
        error: 'Invalid result parameter - must be a non-empty string'
      };
    }

    try {
      Logger.info(`🎯 Executing attempt_completion: ${result.substring(0, 100)}...`);
      
      // Validate task completion
      const validation = await this.validateTaskCompletion(result);
      
      const completionResult: any = {
        summary: result,
        status: validation.isComplete ? 'completed' : 'incomplete',
        timestamp: new Date().toISOString(),
        validation: validation
      };

      if (command && typeof command === 'string') {
        Logger.info(`🎯 Optional demonstration command provided: ${command}`);
        completionResult.demonstrationCommand = command;
      }

      if (!validation.isComplete) {
        Logger.warn(`⚠️ Task completion attempted but validation failed: ${validation.issues.join(', ')}`);
        return {
          id: toolCall.id,
          result: null,
          error: `Task not complete: ${validation.issues.join('; ')}`
        };
      }

      Logger.info(`✅ Task completion validated successfully`);

      return {
        id: toolCall.id,
        result: completionResult
      };

    } catch (error) {
      Logger.error(error, `Error in attempt_completion: ${toolCall.id}`);
      return {
        id: toolCall.id,
        result: null,
        error: `Failed to process completion: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Validate task completion - checks for common patterns that indicate incomplete work
   */
  private async validateTaskCompletion(summary: string): Promise<{
    isComplete: boolean;
    issues: string[];
    suggestions: string[];
  }> {
    const issues: string[] = [];
    const suggestions: string[] = [];

    // Check for incomplete indicators in the summary
    const incompletePatterns = [
      /\b(need|needs|should|must|todo|fix|implement|add|create|update)\b/i,
      /\?\s*$/, // Ends with question
      /\b(next|then|after|still|remaining|pending)\b/i,
      /\b(not\s+(?:working|implemented|complete|finished|done))\b/i,
      /\b(error|issue|problem|bug|fail|broken)\b/i
    ];

    for (const pattern of incompletePatterns) {
      if (pattern.test(summary)) {
        issues.push(`Summary contains incomplete indicators: ${pattern.source}`);
        break;
      }
    }

    // Check for positive completion indicators
    const completionPatterns = [
      /\b(completed|finished|done|implemented|created|added|updated|fixed)\b/i,
      /\b(successfully|working|functional|ready)\b/i,
      /\b(all\s+(?:tests|requirements|tasks|files)\s+(?:pass|complete|created))\b/i
    ];

    const hasCompletionIndicators = completionPatterns.some(pattern => pattern.test(summary));
    if (!hasCompletionIndicators) {
      issues.push('Summary lacks clear completion indicators');
      suggestions.push('Include explicit confirmation that the task is complete (e.g., "Successfully implemented...", "All requirements met")');
    }

    // Summary length check
    if (summary.length < 20) {
      issues.push('Summary too brief - provide more detail about what was accomplished');
      suggestions.push('Expand summary to include specific details about the implementation');
    }

    // Check for vague language
    const vaguePatterns = [
      /\b(some|might|maybe|probably|seems|appears)\b/i,
      /\b(try|attempt|hope)\b/i
    ];

    for (const pattern of vaguePatterns) {
      if (pattern.test(summary)) {
        issues.push('Summary contains uncertain language');
        suggestions.push('Use definitive language to confirm completion');
        break;
      }
    }

    const isComplete = issues.length === 0;

    Logger.info(`🔍 Task completion validation: ${isComplete ? 'PASSED' : 'FAILED'} (${issues.length} issues found)`);
    if (issues.length > 0) {
      Logger.info(`🔍 Validation issues: ${issues.join('; ')}`);
    }

    return {
      isComplete,
      issues,
      suggestions
    };
  }
}
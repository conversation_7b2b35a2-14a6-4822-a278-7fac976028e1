export interface FileSummary {
  path: string;
  language: 'javascript' | 'typescript' | 'haskell' | 'purescript';
  size: number;
  linesOfCode: number;
  definitions: {
    functions: FunctionDefinition[];
    classes: ClassDefinition[];
    interfaces: InterfaceDefinition[];    // TypeScript only
    types: TypeDefinition[];             // TypeScript only
    modules: ModuleDefinition[];         // Haskell/PureScript only
    dataTypes: DataTypeDefinition[];     // Haskell/PureScript only
    instances: InstanceDefinition[];     // Haskell/PureScript only
    constants: VariableDefinition[];
  };
  imports: ImportStatement[];
  exports: ExportStatement[];
  complexity: {
    functionsCount: number;
    classesCount: number;
    interfacesCount: number;
    typesCount: number;
    modulesCount: number;
    instancesCount: number;
    maxNestingDepth: number;
  };
}

export interface FunctionDefinition {
  name: string;
  line: number;
  type: 'function' | 'method' | 'arrow' | 'async' | 'signature' | 'abstract';
  isExported: boolean;
  typeSignature?: string;
  parameters?: string[];
}

export interface ClassDefinition {
  name: string;
  line: number;
  isExported: boolean;
  isAbstract?: boolean;
  methods: string[];
}

export interface InterfaceDefinition {
  name: string;
  line: number;
  isExported: boolean;
  properties: string[];
}

export interface TypeDefinition {
  name: string;
  line: number;
  isExported: boolean;
  kind: 'alias' | 'union' | 'intersection';
}

export interface ModuleDefinition {
  name: string;
  line: number;
  isExported: boolean;
}

export interface DataTypeDefinition {
  name: string;
  line: number;
  constructors: string[];
  isExported: boolean;
}

export interface VariableDefinition {
  name: string;
  line: number;
  type: 'const' | 'let' | 'var';
  isExported: boolean;
}

export interface ImportStatement {
  line: number;
  module: string;
  imports: string[];
  isTypeOnly?: boolean;
}

export interface ExportStatement {
  line: number;
  exports: string[];
  isTypeOnly?: boolean;
}

export interface InstanceDefinition {
  name: string;
  line: number;
  className: string;
  type: string;
  isExported: boolean;
  isDerived?: boolean;
}

export interface ParsedCapture {
  name: string;
  line: number;
  text: string;
  type: string;
  isExported: boolean;
}
/*
- function declarations and signatures
- data type declarations
- type class declarations
- instance declarations
- module declarations
- import statements
*/
export const haskellQuery = `
(
  (haddock)* @doc
  .
  (function
    name: (variable) @name.definition.function) @definition.function
)

(
  (haddock)* @doc
  .
  (bind
    name: (variable) @name.definition.function) @definition.function
)

(
  (haddock)* @doc
  .
  (signature
    (variable) @name.definition.function) @definition.signature
)

(
  (haddock)* @doc
  .
  (signature
    (binding_list
      (variable) @name.definition.function)) @definition.signature
)

(
  (haddock)* @doc
  .
  (data_type
    name: (name) @name.definition.data) @definition.data
)

(
  (haddock)* @doc
  .
  (newtype
    name: (name) @name.definition.data) @definition.data
)

(
  (haddock)* @doc
  .
  (type
    name: (name) @name.definition.type) @definition.type
)

(
  (haddock)* @doc
  .
  (class
    name: (name) @name.definition.class) @definition.class
)

(
  (haddock)* @doc
  .
  (instance
    (name) @name.definition.instance) @definition.instance
)

(header
  (module
    (module_id) @name.definition.module)) @definition.module

(import
  (module
    (module_id) @import.module)) @import
`;
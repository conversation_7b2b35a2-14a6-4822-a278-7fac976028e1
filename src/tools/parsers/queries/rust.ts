/*
Rust Tree-sitter query to capture:
- struct definitions
- enum definitions
- impl blocks
- trait definitions
- module declarations
- const and static declarations
- macro invocations (including scoped macros)
*/
export const rustQuery = `
(struct_item
  name: (type_identifier) @name.definition.struct) @definition.struct

(enum_item
  name: (type_identifier) @name.definition.enum) @definition.enum

(trait_item
  name: (type_identifier) @name.definition.trait) @definition.trait

(impl_item
  type: (type_identifier) @name.definition.impl) @definition.impl

(mod_item
  name: (identifier) @name.definition.module) @definition.module

(const_item
  name: (identifier) @name.definition.constant) @definition.constant

(static_item
  name: (identifier) @name.definition.static) @definition.static

(macro_invocation
  macro: (identifier) @name.definition.macro) @definition.macro

(macro_invocation
  macro: (scoped_identifier) @name.definition.macro) @definition.macro
`; 
/*
- function signatures and declarations
- data type declarations
- newtype declarations
- type alias declarations
- class declarations
- instance declarations
- module declarations
- import statements
*/
export const purescriptQuery = `
(signature
  name: (variable) @name.definition.function) @definition.signature

(function
  name: (variable) @name.definition.function) @definition.function

(data
  name: (type) @name.definition.data) @definition.data

(newtype
  name: (type) @name.definition.data) @definition.newtype

(type_alias
  name: (type) @name.definition.type) @definition.type

(class_declaration
  (class_head
    (class_name
      (type) @name.definition.class))) @definition.class

(class_instance
  (instance_head
    (class_name
      (type) @name.definition.instance))) @definition.instance

(derive_declaration
  (instance_name) @name.definition.instance) @definition.derive

(qualified_module
  (module) @name.definition.module) @definition.module

(import
  module: (qualified_module
    (module) @import.module)) @import
`;
import Parser from 'web-tree-sitter';
import * as path from 'path';
import * as vscode from 'vscode';
import { javascriptQuery } from './queries/javascript';
import { typescriptQuery } from './queries/typescript';
import { haskellQuery } from './queries/haskell';
import { purescriptQuery } from './queries/purescript';

export interface LanguageParser {
  [key: string]: {
    parser: Parser;
    query: Parser.Query;
  };
}

let isParserInitialized = false;

async function initializeParser(context?: vscode.ExtensionContext) {
  if (!isParserInitialized) {
    // For VS Code extension environment, we need to provide the WASM file path
    if (context) {
      const wasmPath = vscode.Uri.joinPath(context.extensionUri, 'dist', 'tree-sitter.wasm');
      const wasmBuffer = await vscode.workspace.fs.readFile(wasmPath);
      await Parser.init({
        wasmBinary: wasmBuffer
      });
    } else {
      await Parser.init();
    }
    isParserInitialized = true;
  }
}

async function loadLanguage(langName: string, wasmPath: string, context?: vscode.ExtensionContext) {
  if (context) {
    // Load WASM file from extension directory
    const fullWasmPath = vscode.Uri.joinPath(context.extensionUri, 'dist', wasmPath.replace('/', ''));
    const wasmBuffer = await vscode.workspace.fs.readFile(fullWasmPath);
    return await Parser.Language.load(wasmBuffer);
  } else {
    return await Parser.Language.load(wasmPath);
  }
}

export async function loadRequiredLanguageParsers(filesToParse: string[], context?: vscode.ExtensionContext): Promise<LanguageParser> {
  await initializeParser(context);
  const extensionsToLoad = new Set(filesToParse.map(file =>
    path.extname(file).toLowerCase().slice(1)
  ));

  const parsers: LanguageParser = {};

  for (const ext of extensionsToLoad) {
    let language: Parser.Language;
    let queryString: string;

    switch (ext) {
      case 'js':
      case 'jsx':
        language = await loadLanguage('javascript', 'tree-sitter-javascript.wasm', context);
        queryString = javascriptQuery;
        break;
      case 'ts':
      case 'tsx':
        language = await loadLanguage('typescript', 'tree-sitter-typescript.wasm', context);
        queryString = typescriptQuery;
        break;
      case 'hs':
        language = await loadLanguage('haskell', 'tree-sitter-haskell.wasm', context);
        queryString = haskellQuery;
        break;
      case 'purs':
        language = await loadLanguage('purescript', 'tree-sitter-purescript.wasm', context);
        queryString = purescriptQuery;
        break;
      default:
        throw new Error(`Unsupported language: ${ext}`);
    }

    const parser = new Parser();
    parser.setLanguage(language);
    const query = language.query(queryString);

    parsers[ext] = { parser, query };
  }

  return parsers;
}

export function getLanguageFromExtension(ext: string): 'javascript' | 'typescript' | 'haskell' | 'purescript' {
  switch (ext) {
    case 'js':
    case 'jsx':
      return 'javascript';
    case 'ts':
    case 'tsx':
      return 'typescript';
    case 'hs':
      return 'haskell';
    case 'purs':
      return 'purescript';
    default:
      throw new Error(`Unsupported extension: ${ext}`);
  }
}

export function getSupportedExtensions(): string[] {
  return ['js', 'jsx', 'ts', 'tsx', 'hs', 'purs'];
}
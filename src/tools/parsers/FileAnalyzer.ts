import * as path from 'path';
import * as vscode from 'vscode';
import {
  FileSummary,
  FunctionDefinition,
  ClassDefinition,
  InterfaceDefinition,
  TypeDefinition,
  ModuleDefinition,
  DataTypeDefinition,
  InstanceDefinition,
  VariableDefinition,
  ImportStatement,
  ExportStatement,
  ParsedCapture
} from './types';
import { LanguageParser, loadRequiredLanguageParsers, getLanguageFromExtension } from './LanguageParser';

export class FileAnalyzer {
  private languageParsers: LanguageParser = {};

  async initialize(filesToParse: string[], context?: vscode.ExtensionContext): Promise<void> {
    this.languageParsers = await loadRequiredLanguageParsers(filesToParse, context);
  }

  async analyzeFile(filePath: string, content: string): Promise<FileSummary> {
    const ext = path.extname(filePath).toLowerCase().slice(1);
    const language = getLanguageFromExtension(ext);

    const { parser, query } = this.languageParsers[ext];
    if (!parser || !query) {
      throw new Error(`No parser available for ${ext}`);
    }

    try {
      const tree = parser.parse(content);
      const captures = query.captures(tree.rootNode);

      return this.processCaptures(filePath, content, captures, language);
    } catch (error) {
      throw new Error(`Failed to parse ${filePath}: ${error.message}`);
    }
  }

  private processCaptures(
    filePath: string,
    content: string,
    captures: any[],
    language: 'javascript' | 'typescript' | 'haskell' | 'purescript'
  ): FileSummary {
    const lines = content.split('\n');
    const functions: FunctionDefinition[] = [];
    const classes: ClassDefinition[] = [];
    const interfaces: InterfaceDefinition[] = [];
    const types: TypeDefinition[] = [];
    const modules: ModuleDefinition[] = [];
    const dataTypes: DataTypeDefinition[] = [];
    const instances: InstanceDefinition[] = [];
    const constants: VariableDefinition[] = [];
    const imports: ImportStatement[] = [];
    const exports: ExportStatement[] = [];

    // Sort captures by line number
    captures.sort((a, b) => a.node.startPosition.row - b.node.startPosition.row);

    for (const capture of captures) {
      const { node, name } = capture;
      const line = node.startPosition.row + 1; // Convert to 1-based
      const text = lines[node.startPosition.row]?.trim() || '';
      const nodeText = node.text;

      // Check if this is an exported definition
      const isExported = this.isExported(text, language);

      if (name.includes('name.definition')) {
        if (name.includes('function') || name.includes('signature')) {
          functions.push({
            name: nodeText,
            line,
            type: this.getFunctionType(name, text),
            isExported,
            typeSignature: language === 'haskell' ? text : undefined
          });
        } else if (name.includes('class') && !name.includes('definition.class')) {
          // This is a type class in Haskell/PureScript, not a regular class
          if (language === 'haskell' || language === 'purescript') {
            dataTypes.push({
              name: nodeText,
              line,
              constructors: [],
              isExported
            });
          } else {
            classes.push({
              name: nodeText,
              line,
              isExported,
              methods: []
            });
          }
        } else if (name.includes('interface')) {
          interfaces.push({
            name: nodeText,
            line,
            isExported,
            properties: []
          });
        } else if (name.includes('type')) {
          types.push({
            name: nodeText,
            line,
            isExported,
            kind: 'alias'
          });
        } else if (name.includes('module')) {
          modules.push({
            name: nodeText,
            line,
            isExported: true // Modules are inherently exported
          });
        } else if (name.includes('data')) {
          dataTypes.push({
            name: nodeText,
            line,
            constructors: [],
            isExported
          });
        } else if (name.includes('variable')) {
          constants.push({
            name: nodeText,
            line,
            type: this.getVariableType(text),
            isExported
          });
        } else if (name.includes('instance')) {
          // PureScript/Haskell instance declarations
          instances.push({
            name: nodeText,
            line,
            className: '', // Would need more complex parsing
            type: '',
            isExported,
            isDerived: name.includes('derive')
          });
        } else if (name.includes('method')) {
          // Add to the last class if we're in a class context
          if (classes.length > 0) {
            classes[classes.length - 1].methods.push(nodeText);
          }
        }
      } else if (name.includes('import')) {
        if (name.includes('import.source') || name.includes('import.module')) {
          const moduleName = nodeText.replace(/['"]/g, '');
          imports.push({
            line,
            module: moduleName,
            imports: [], // Would need more complex parsing to extract specific imports
            isTypeOnly: text.includes('type ')
          });
        }
      } else if (name.includes('export')) {
        exports.push({
          line,
          exports: [nodeText],
          isTypeOnly: text.includes('type ')
        });
      }
    }

    return {
      path: filePath,
      language,
      size: content.length,
      linesOfCode: lines.length,
      definitions: {
        functions,
        classes,
        interfaces,
        types,
        modules,
        dataTypes,
        instances,
        constants
      },
      imports,
      exports,
      complexity: {
        functionsCount: functions.length,
        classesCount: classes.length,
        interfacesCount: interfaces.length,
        typesCount: types.length,
        modulesCount: modules.length,
        instancesCount: instances.length,
        maxNestingDepth: 0 // Would need deeper analysis
      }
    };
  }

  private isExported(text: string, language: 'javascript' | 'typescript' | 'haskell' | 'purescript'): boolean {
    if (language === 'haskell' || language === 'purescript') {
      // In Haskell/PureScript, top-level definitions are exported by default unless module has explicit export list
      return !text.startsWith('  ') && !text.startsWith('\t');
    } else {
      return text.includes('export ') || text.startsWith('export ');
    }
  }

  private getFunctionType(captureName: string, text: string): 'function' | 'method' | 'arrow' | 'async' | 'signature' | 'abstract' {
    if (captureName.includes('signature')) {
      return 'signature';
    }
    if (captureName.includes('method')) {
      return text.includes('abstract') ? 'abstract' : 'method';
    }
    if (text.includes('async ')) {
      return 'async';
    }
    if (text.includes('=>')) {
      return 'arrow';
    }
    return 'function';
  }

  private getVariableType(text: string): 'const' | 'let' | 'var' {
    if (text.startsWith('const ')) {return 'const';}
    if (text.startsWith('let ')) {return 'let';}
    if (text.startsWith('var ')) {return 'var';}
    return 'const'; // Default fallback
  }
}
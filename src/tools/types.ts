export interface ToolDefinition {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, {
      type: string;
      description: string;
      required?: boolean;
    }>;
    required: string[];
  };
}

export interface ToolCall {
  id: string;
  name: string;
  parameters: Record<string, any>;
}

export interface ToolResult {
  id: string;
  result: any;
  error?: string;
}

export enum ToolApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  DENIED = 'denied'
}

export interface ToolApprovalRequest {
  id: string;
  toolName: string;
  parameters: Record<string, any>;
  description: string;
  status: ToolApprovalStatus;
}

import { vi } from 'vitest';

const _mockData: { [key: string]: any } = {};

export const workspace = {
    fs: {
        createDirectory: vi.fn().mockResolvedValue(undefined),
        writeFile: vi.fn((uri, content) => {
            _mockData[uri.fsPath] = content;
            return Promise.resolve();
        }),
        readFile: vi.fn((uri) => {
            if (_mockData[uri.fsPath]) {
                return Promise.resolve(_mockData[uri.fsPath]);
            }
            const error = new Error('File not found') as any;
            error.code = 'FileNotFound';
            return Promise.reject(error);
        }),
        delete: vi.fn((uri) => {
            delete _mockData[uri.fsPath];
            return Promise.resolve();
        }),
        stat: vi.fn((uri) => {
            if (_mockData[uri.fsPath]) {
                return Promise.resolve({ type: 1, ctime: 0, mtime: 0, size: 0 });
            }
            const error = new Error('File not found') as any;
            error.code = 'FileNotFound';
            return Promise.reject(error);
        }),
    },
};

export const Uri = {
    joinPath: vi.fn((base: any, ...segments: string[]) => ({
        fsPath: `${base.fsPath}/${segments.join('/')}`,
        path: `${base.path}/${segments.join('/')}`,
        scheme: base.scheme,
    })),
    parse: vi.fn((path: string) => ({
        fsPath: path,
        path,
        scheme: 'file',
    })),
};

export const window = {
    showErrorMessage: vi.fn().mockResolvedValue(undefined),
    showWarningMessage: vi.fn().mockResolvedValue(undefined),
    showInformationMessage: vi.fn().mockResolvedValue(undefined),
};

export class FileSystemError extends Error {
    code: string;
    constructor(message: string, code: string = 'Unknown') {
        super(message);
        this.code = code;
    }
}

export class EventEmitter<T> {
    private listeners: Array<(data: T) => void> = [];

    constructor() {
        this.listeners = [];
    }

    event(listener: (data: T) => void) {
        this.listeners.push(listener);
        return {
            dispose: () => {
                const index = this.listeners.indexOf(listener);
                if (index > -1) {
                    this.listeners.splice(index, 1);
                }
            },
        };
    }

    fire(e: T) {
        this.listeners.forEach((listener) => listener(e));
    }

    dispose() {
        this.listeners.length = 0;
    }
}

export interface ExtensionContext {
    globalStorageUri: {
        fsPath: string;
    };
}

export enum Subsystem {
  AI = "AI",
  Extension = "Extension",
  Settings = "Settings",
  Chat = "Chat",
}

export interface Logger {
  error(error: any, message?: string): void;
  info(message: string): void;
  warn(message: string): void;
  debug(message: string): void;
}

class SimpleLogger implements Logger {
  constructor(private subsystem: Subsystem) {}

  error(error: any, message?: string): void {
    console.error(`[${this.subsystem}] ${message || "Error"}:`, error);
  }

  info(message: string): void {
    console.info(`[${this.subsystem}] ${message}`);
  }

  warn(message: string): void {
    console.warn(`[${this.subsystem}] ${message}`);
  }

  debug(message: string): void {
    console.debug(`[${this.subsystem}] ${message}`);
  }
}

export function getLogger(subsystem: Subsystem): Logger {
  return new SimpleLogger(subsystem);
}
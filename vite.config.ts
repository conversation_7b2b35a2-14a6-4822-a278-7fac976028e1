import { defineConfig } from 'vite';

export default defineConfig({
  build: {
    lib: {
      entry: {
        extension: 'src/extension.ts',
        'ingest-lancedb': 'src/embedding/ingest-lancedb.ts',
        'ingest-hybrid-script': 'src/embedding/ingest-hybrid-script.ts'
      },
      name: 'extension',
      formats: ['cjs']
    },
    rollupOptions: {
      external: ['vscode', 'child_process', 'fs', 'path', 'node-llama-cpp', '@lancedb/lancedb', '@lancedb/lancedb/embedding', 'os', 'crypto', 'url', 'stream', 'buffer', 'util', 'events', 'https', 'http', 'net', 'tls', 'zlib']
    },
    outDir: 'dist',
    sourcemap: true,
    ssr: true,
    emptyOutDir: false,
  },
  resolve: {
    extensions: ['.ts', '.js']
  }
});

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'cursor-primary-bg': 'var(--cursor-primary-bg)',
        'cursor-secondary-bg': 'var(--cursor-secondary-bg)',
        'cursor-text-primary': 'var(--cursor-text-primary)',
        'cursor-text-secondary': 'var(--cursor-text-secondary)',
        'cursor-border-subtle': 'var(--cursor-border-subtle)',
        'cursor-ghost-text': 'var(--cursor-ghost-text)',
        'cursor-input-bg': 'var(--cursor-input-bg)',
        'cursor-accent-blue': 'var(--cursor-accent-blue)',
        'cursor-button-bg': 'var(--cursor-button-bg)',
        'cursor-button-hover': 'var(--cursor-button-hover)',
      },
      boxShadow: {
        'cursor-sm': 'var(--cursor-shadow-sm)',
        'cursor-md': 'var(--cursor-shadow-md)',
        'cursor-lg': 'var(--cursor-shadow-lg)',
        'cursor-xl': 'var(--cursor-shadow-xl)',
      },
      backgroundImage: {
        'cursor-gradient-primary': 'var(--cursor-gradient-primary)',
        'cursor-gradient-secondary': 'var(--cursor-gradient-secondary)',
      },
      fontFamily: {
        'mono': ['var(--vscode-editor-font-family)', 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', 'Consolas', 'Courier New', 'monospace'],
      },
      animation: {
        'loading-bounce': 'loading-bounce 1.4s ease-in-out infinite both',
      },
      transitionTimingFunction: {
        'cursor': 'cubic-bezier(0.4, 0, 0.2, 1)',
      },
    },
  },
  plugins: [],
}

import { useEffect, useState } from 'react';
import ChatView from './components/ChatView';
import SettingsView from './components/SettingsView';
import './index.css';

// Extend window interface for global page context
declare global {
  interface Window {
    __WEBVIEW_PAGE__?: 'chat' | 'settings';
  }
}

function App() {
  const [currentPage, setCurrentPage] = useState<'chat' | 'settings'>('chat');

  useEffect(() => {
    // Determine initial page from multiple sources
    const urlParams = new URLSearchParams(window.location.search);
    const pageParam = urlParams.get('page');
    const bodyPage = document.body.getAttribute('data-page');
    const globalPage = window.__WEBVIEW_PAGE__;

    console.log('App initializing - URL param:', pageParam, 'Body page:', bodyPage, 'Global page:', globalPage);

    // Priority: global page context > URL param > body attribute
    if (globalPage === 'settings' || pageParam === 'settings' || bodyPage === 'settings') {
      console.log('Setting initial page to settings');
      setCurrentPage('settings');
    } else {
      console.log('Setting initial page to chat');
      setCurrentPage('chat');
    }

    // Listen for messages from parent webview to change pages
    const handleMessage = (event: MessageEvent) => {
      // Only handle setPage messages, ignore all other message types
      if (event.data && event.data.type === 'setPage') {
        console.log('Received setPage message:', event.data.page);
        setCurrentPage(event.data.page);
      }
      // Explicitly ignore chat-related messages - these should only be handled by ChatView
      // This prevents double processing of messageChunk, messageStart, messageComplete, etc.
    };

    window.addEventListener('message', handleMessage);

    // Use MutationObserver to watch for body attribute changes instead of polling
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-page') {
          const newPage = document.body.getAttribute('data-page');
          console.log('Body data-page changed to:', newPage);

          if (newPage === 'settings') {
            setCurrentPage('settings');
          } else if (newPage === 'chat' || !newPage) {
            setCurrentPage('chat');
          }
        }
      });
    });

    // Start observing body for attribute changes
    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['data-page']
    });

    return () => {
      window.removeEventListener('message', handleMessage);
      observer.disconnect();
    };
  }, []); // Remove currentPage dependency to prevent re-running

  // Debug logging only when page actually changes
  useEffect(() => {
    console.log('Page changed to:', currentPage);
  }, [currentPage]);

  return (
    <div className="font-mono text-cursor-text-primary bg-cursor-primary-bg min-h-screen flex flex-col">
      {currentPage === 'settings' ? <SettingsView /> : <ChatView />}
    </div>
  );
}

export default App;

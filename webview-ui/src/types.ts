export interface AttachedFile {
  name: string;
  size: number;
  type: string;
  content: string;
  path?: string;
}

export interface ToolCall {
  name: string;
  params: Record<string, any>;
}

export interface ToolResult {
  id: string;
  result: any;
  error?: string;
}

export interface ToolExecution {
  toolCall: ToolCall;
  toolResult: ToolResult;
}

// Interface for storing terminal execution history
export interface TerminalExecutionHistory {
  command: string;
  output: string;
  exitCode?: number;
  cwd?: string;
  executedAt: number;
  terminated: boolean;
}

export interface Message {
  id: number;
  content: string;
  sender?: 'user' | 'bot';
  type?: 'user' | 'bot' | 'terminal';
  timestamp: Date;
  sessionId?: string; // For terminal messages
  isHistorical?: boolean; // Flag to indicate if this message was loaded from history vs created in current session
  metadata?: {
    model?: string;
    tokens?: number;
    cost?: number;
    responseTime?: number;
    error?: string;
    // For diff messages
    type?: string;
    filePath?: string;
    originalContent?: string;
    newContent?: string;
    diffSummary?: string;
    staged?: boolean;
    changeId?: string;
    // For tool executions
    tool_executions?: ToolExecution[];
    // For terminal execution history
    terminalHistory?: TerminalExecutionHistory;
    // For file change review
    fileChangeReview?: {
      filePath: string;
      originalContent: string;
      newContent: string;
      diffSummary: string;
      language?: string;
      staged?: boolean;
      changeId?: string;
      reviewStatus?: 'pending' | 'accepted' | 'rejected' | 'auto_approve';
    };
    // For streaming timeout
    isStreamingTimeout?: boolean;
    timeoutDurationMs?: number;
  };
  attachedFiles?: AttachedFile[]; // For file attachments
}

export enum ChatMode {
  Ask = "ask",
  Agent = "agent"
}

export type ProviderStatus = 'connected' | 'disconnected' | 'connecting' | 'error' | 'not-configured' | 'configured';

export interface ProviderStatusInfo {
  status: ProviderStatus;
  message?: string;
  lastChecked?: Date;
}

export interface CurrentProviderInfo {
  provider: string;
  model: string;
  status: ProviderStatusInfo;
}

export interface ChatSession {
  id: string;
  name: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
  provider?: string;
  model?: string;
  customInstructions?: string;
}

export interface ChatHistoryState {
  sessions: ChatSession[];
  currentSessionId: string | null;
  totalSessions: number;
}

// Extend the Window interface to include vscode
declare global {
  interface Window {
    vscode?: {
      postMessage: (message: any) => void;
    };
    __vscodeApi?: {
      postMessage: (message: any) => void;
    };
  }
}
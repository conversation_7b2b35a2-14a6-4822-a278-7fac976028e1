// monaco-config.ts
// This file configures the Monaco Editor's worker setup for VS Code webviews
// Import this file before any Monaco usage

import * as monaco from 'monaco-editor';

// Configuration object for Monaco behavior
interface MonacoConfig {
  useFallbackDiffRenderer: boolean;
  diffFailureCount: number;
}

// Export configuration object for external usage
export const monacoConfig: MonacoConfig = {
  useFallbackDiffRenderer: false,
  diffFailureCount: 0
};



// Define the worker environment - simpler version that works with vite
console.log('Monaco-Config: Setting up MonacoEnvironment');

// A function to create a worker with error handling
const createWorker = (label: string, baseCode: string): Worker => {
  console.log(`Monaco-Config: Creating worker for ${label}`);
  try {
    const blob = new Blob([baseCode], { type: 'application/javascript' });
    const url = URL.createObjectURL(blob);
    const worker = new Worker(url, { name: label });
    
    // Setup error handling
    worker.onerror = (event) => {
      console.error(`Monaco-Config: Worker '${label}' error:`, event.message);
      monacoConfig.diffFailureCount++; // Track failures across workers
    };
    
    // Clean up URL when worker terminates
    worker.addEventListener('error', () => {
      URL.revokeObjectURL(url); // Clean up the URL
    });
    
    return worker;
  } catch (error) {
    console.error(`Monaco-Config: Failed to create worker for '${label}':`, error);
    monacoConfig.diffFailureCount++;
    
    // Return a mock worker that does nothing but won't crash
    const mockWorker = {
      postMessage: () => {},
      terminate: () => {},
      addEventListener: () => {},
      removeEventListener: () => {},
      dispatchEvent: () => false,
      onmessage: null,
      onmessageerror: null,
      onerror: null
    };
    return mockWorker as unknown as Worker;
  }
};

// Define the Monaco environment that works with VS Code webviews
self.MonacoEnvironment = {
  getWorker(moduleId: string, label: string) {
    console.log(`Monaco-Config: Worker requested for ${label}`);
    
    // Basic worker code that handles most editor features
    let baseCode = `
      self.MonacoEnvironment = { baseUrl: '' };
      console.log('${label} worker initialized');
      
      self.onmessage = function(e) {
        const msg = e.data;
        console.log('${label} worker received message:', msg?.type);
        
        if (msg?.type === 'initialize') {
          self.postMessage({ type: 'initialized' });
        }
      };
    `;
    
    // Special case for diff editor
    if (label === 'diff') {
      console.log('Monaco-Config: Creating specialized diff worker');
      baseCode = `
        self.MonacoEnvironment = { baseUrl: '' };
        console.log('Diff worker initialized');
        
        function computeImprovedDiff(original, modified) {
          try {
            // Split into lines for line-level diff
            const originalLines = original.split('\\n');
            const modifiedLines = modified.split('\\n');
            
            // Very basic diffing algorithm to detect changes
            const changes = [];
            let originalLine = 1;
            let modifiedLine = 1;
            
            // Maximum number of lines to look ahead for changes
            const MAX_LOOK_AHEAD = 100;
            
            while (originalLine <= originalLines.length || modifiedLine <= modifiedLines.length) {
              // End of either file
              if (originalLine > originalLines.length) {
                // All remaining modified lines are additions
                changes.push({
                  originalStartLineNumber: originalLine,
                  originalEndLineNumber: originalLine,
                  modifiedStartLineNumber: modifiedLine,
                  modifiedEndLineNumber: modifiedLines.length
                });
                break;
              }
              
              if (modifiedLine > modifiedLines.length) {
                // All remaining original lines are deletions
                changes.push({
                  originalStartLineNumber: originalLine,
                  originalEndLineNumber: originalLines.length,
                  modifiedStartLineNumber: modifiedLine,
                  modifiedEndLineNumber: modifiedLine
                });
                break;
              }
              
              // Current lines are the same
              if (originalLines[originalLine-1] === modifiedLines[modifiedLine-1]) {
                originalLine++;
                modifiedLine++;
                continue;
              }
              
              // Look for next match
              let matchFound = false;
              let bestMatchOriginalDelta = 0;
              let bestMatchModifiedDelta = 0;
              
              // Try to find next matching lines
              for (let i = 1; i <= MAX_LOOK_AHEAD && originalLine + i <= originalLines.length; i++) {
                for (let j = 1; j <= MAX_LOOK_AHEAD && modifiedLine + j <= modifiedLines.length; j++) {
                  if (originalLines[originalLine + i - 1] === modifiedLines[modifiedLine + j - 1]) {
                    // Found a match
                    matchFound = true;
                    bestMatchOriginalDelta = i;
                    bestMatchModifiedDelta = j;
                    break;
                  }
                }
                if (matchFound) break;
              }
              
              if (matchFound) {
                // Create a change entry for the non-matching section
                changes.push({
                  originalStartLineNumber: originalLine,
                  originalEndLineNumber: originalLine + bestMatchOriginalDelta - 1,
                  modifiedStartLineNumber: modifiedLine,
                  modifiedEndLineNumber: modifiedLine + bestMatchModifiedDelta - 1
                });
                
                // Move past the changed section
                originalLine += bestMatchOriginalDelta;
                modifiedLine += bestMatchModifiedDelta;
              } else {
                // No match found within the look-ahead window
                // Mark all remaining content as changed
                changes.push({
                  originalStartLineNumber: originalLine,
                  originalEndLineNumber: originalLines.length,
                  modifiedStartLineNumber: modifiedLine,
                  modifiedEndLineNumber: modifiedLines.length
                });
                break;
              }
            }
            
            return {
              quitEarly: false,
              changes: changes.length > 0 ? changes : [{
                originalStartLineNumber: 1,
                originalEndLineNumber: originalLines.length,
                modifiedStartLineNumber: 1,
                modifiedEndLineNumber: modifiedLines.length
              }]
            };
          } catch (err) {
            console.error('Error in diff calculation:', err);
            // Fallback to marking everything as changed
            return {
              quitEarly: false,
              changes: [{
                originalStartLineNumber: 1,
                originalEndLineNumber: original.split('\\n').length,
                modifiedStartLineNumber: 1,
                modifiedEndLineNumber: modified.split('\\n').length
              }]
            };
          }
        }
        
        self.onmessage = function(e) {
          const msg = e.data;
          console.log('Diff worker received message:', msg?.type);
          
          if (msg?.type === 'initialize') {
            self.postMessage({ type: 'initialized' });
          } else if (msg?.type === 'computeDiff') {
            try {
              const result = computeImprovedDiff(msg.original || '', msg.modified || '');
              self.postMessage({
                type: 'computeDiff',
                requestId: msg.requestId,
                result: result
              });
            } catch (err) {
              console.error('Error in diff worker:', err);
              self.postMessage({
                type: 'computeDiff',
                requestId: msg.requestId,
                error: { message: err.message || 'Unknown error in diff worker' }
              });
            }
          }
        };
      `;
    }
    
    return createWorker(label, baseCode);
  }
};

// Define themes for light and dark modes
const defineMonacoThemes = () => {
  console.log('Monaco-Config: Defining editor themes');
  
  try {
    monaco.editor.defineTheme('vscode-dark-custom', {
      base: 'vs-dark',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#d4d4d4',
        'editor.lineHighlightBackground': '#2a2d2e',
        'diffEditor.insertedTextBackground': '#37415180',
        'diffEditor.removedTextBackground': '#5c313680',
        'diffEditor.insertedLineBackground': '#37415140',
        'diffEditor.removedLineBackground': '#5c313640',
      }
    });

    monaco.editor.defineTheme('vscode-light-custom', {
      base: 'vs',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#ffffff',
        'editor.foreground': '#333333',
        'diffEditor.insertedTextBackground': '#c0dcc0',
        'diffEditor.removedTextBackground': '#f0c8c8',
        'diffEditor.insertedLineBackground': '#c0dcc080',
        'diffEditor.removedLineBackground': '#f0c8c880',
      }
    });

    // Apply the appropriate theme
    const isDark = document.body.classList.contains('vscode-dark') ||
                   window.matchMedia('(prefers-color-scheme: dark)').matches;
    const themeName = isDark ? 'vscode-dark-custom' : 'vscode-light-custom';
    
    console.log('Monaco-Config: Setting theme to', themeName);
    monaco.editor.setTheme(themeName);
  } catch (error) {
    console.error('Monaco-Config: Error defining themes:', error);
  }
};

// Initialize themes
defineMonacoThemes();

// Export monaco
export { monaco };

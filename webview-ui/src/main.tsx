import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
// Import Monaco config first to set up workers
import './monaco-config'
import './index.css'
import App from './App.tsx'
import { setupKeyboardShortcuts } from './utils/keyboardShortcuts'

// Theme detection for VSCode
function detectAndApplyTheme() {
  // Check for VSCode theme kind
  const body = document.body;

  // Remove existing theme classes
  body.classList.remove('vscode-light', 'vscode-dark', 'vscode-high-contrast');

  // Try to detect theme from VSCode API or CSS variables
  const computedStyle = getComputedStyle(document.documentElement);
  const editorBg = computedStyle.getPropertyValue('--vscode-editor-background').trim();

  // If VSCode variables are available, use them to detect theme
  if (editorBg) {
    // Convert hex/rgb to brightness to determine theme
    let brightness = 0;
    if (editorBg.startsWith('#')) {
      const hex = editorBg.slice(1);
      const r = parseInt(hex.substr(0, 2), 16);
      const g = parseInt(hex.substr(2, 2), 16);
      const b = parseInt(hex.substr(4, 2), 16);
      brightness = (r * 299 + g * 587 + b * 114) / 1000;
    } else if (editorBg.startsWith('rgb')) {
      const matches = editorBg.match(/\d+/g);
      if (matches && matches.length >= 3) {
        const r = parseInt(matches[0]);
        const g = parseInt(matches[1]);
        const b = parseInt(matches[2]);
        brightness = (r * 299 + g * 587 + b * 114) / 1000;
      }
    }

    if (brightness > 128) {
      body.classList.add('vscode-light');
    } else {
      body.classList.add('vscode-dark');
    }
  } else {
    // Fallback: check for common VSCode theme indicators
    const isDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    body.classList.add(isDark ? 'vscode-dark' : 'vscode-light');
  }
}

// Apply theme on load
detectAndApplyTheme();

// Listen for theme changes
if (window.matchMedia) {
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', detectAndApplyTheme);
}

// Also listen for VSCode theme changes via mutation observer
const observer = new MutationObserver(() => {
  detectAndApplyTheme();
});

observer.observe(document.documentElement, {
  attributes: true,
  attributeFilter: ['style', 'class']
});

// Set up unified keyboard shortcuts for all input fields
setupKeyboardShortcuts();

createRoot(document.getElementById('root')!).render(
    <App />
)

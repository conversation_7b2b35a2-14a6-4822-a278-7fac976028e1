import { ModelDisplayInfo, ModelDropdownGroup } from './modelTypes';

/**
 * Utility functions for model operations in the frontend.
 * Provides a clean API without backend type dependencies.
 */

/**
 * Get display name for a model by ID
 */
export const getModelDisplayName = (modelId: string, availableModels: ModelDisplayInfo[]): string => {
  const model = availableModels.find(m => m.id === modelId);
  return model?.displayName || modelId;
};

/**
 * Get models for a specific provider
 */
export const getModelsForProvider = (providerId: string, availableModels: ModelDisplayInfo[]): ModelDisplayInfo[] => {
  return availableModels.filter(model => model.provider === providerId);
};

/**
 * Group models by provider for dropdown display
 */
export const groupModelsByProvider = (availableModels: ModelDisplayInfo[]): ModelDropdownGroup[] => {
  const groups = new Map<string, ModelDropdownGroup>();

  for (const model of availableModels) {
    const providerId = model.provider;
    
    if (!groups.has(providerId)) {
      groups.set(providerId, {
        providerId,
        providerName: getProviderDisplayName(providerId),
        models: []
      });
    }

    groups.get(providerId)!.models.push(model);
  }

  // Sort groups by provider name and filter out empty groups
  return Array.from(groups.values())
    .filter(group => group.models.length > 0)
    .sort((a, b) => a.providerName.localeCompare(b.providerName));
};

/**
 * Find a model by ID
 */
export const findModelById = (modelId: string, availableModels: ModelDisplayInfo[]): ModelDisplayInfo | undefined => {
  return availableModels.find(model => model.id === modelId);
};

/**
 * Check if a model is currently selected
 */
export const isModelSelected = (modelId: string, currentModel: ModelDisplayInfo | null): boolean => {
  return currentModel?.id === modelId;
};

/**
 * Get provider display name - centralized mapping
 */
export const getProviderDisplayName = (providerId: string): string => {
  const providerNames: Record<string, string> = {
    'aws-bedrock': 'AWS Bedrock',
    'openai': 'OpenAI',
    'azure-openai': 'Azure OpenAI',
    'google-ai': 'Google AI',
    'vertex-ai': 'Vertex AI',
    'openrouter': 'OpenRouter'
  };

  return providerNames[providerId] || providerId;
};

/**
 * Sort models within a provider group (defaults first, then alphabetical)
 */
export const sortModelsInGroup = (models: ModelDisplayInfo[]): ModelDisplayInfo[] => {
  return [...models].sort((a, b) => {
    // Default models first
    if (a.isDefault && !b.isDefault) return -1;
    if (!a.isDefault && b.isDefault) return 1;
    
    // Then alphabetical by display name
    return a.displayName.localeCompare(b.displayName);
  });
};

/**
 * Transform backend model data to frontend format
 */
export const transformModelData = (backendData: any[]): ModelDisplayInfo[] => {
  return backendData.map(item => ({
    id: item.id || item.model,
    displayName: item.displayName || item.name,
    provider: item.provider,
    family: item.family || 'unknown',
    isDefault: item.isDefault || false
  }));
};

/**
 * Get all unique providers from available models
 */
export const getUniqueProviders = (availableModels: ModelDisplayInfo[]): string[] => {
  const providers = new Set(availableModels.map(model => model.provider));
  return Array.from(providers).sort();
};

/**
 * Filter models by search term
 */
export const filterModelsBySearch = (models: ModelDisplayInfo[], searchTerm: string): ModelDisplayInfo[] => {
  if (!searchTerm.trim()) return models;
  
  const term = searchTerm.toLowerCase();
  return models.filter(model => 
    model.displayName.toLowerCase().includes(term) ||
    model.provider.toLowerCase().includes(term) ||
    model.family.toLowerCase().includes(term)
  );
};
/**
 * Unified keyboard shortcut handler for webview input fields
 * Handles standard editing shortcuts (Cmd+A, Cmd+C, Cmd+X, Cmd+Z) consistently across all input fields
 */

export function setupKeyboardShortcuts() {
  // Remove any existing keyboard shortcut listeners to prevent duplicates
  const existingHandlers = document.querySelectorAll('[data-keyboard-shortcuts="enabled"]');
  existingHandlers.forEach(el => el.removeAttribute('data-keyboard-shortcuts'));

  // Add unified keyboard shortcut handler
  document.addEventListener('keydown', function(e) {
    const target = e.target as HTMLElement;
    const isInputField = target && (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.contentEditable === 'true' ||
      target.getAttribute('contenteditable') === 'true'
    );

    if (isInputField) {
      const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
      const cmdOrCtrl = isMac ? e.metaKey : e.ctrlKey;

      // Handle standard editing shortcuts by executing them directly
      if (cmdOrCtrl) {
        const key = e.key.toLowerCase();

        switch (key) {
          case 'a':
            e.preventDefault();
            e.stopPropagation();
            // Select all text in the input field
            if ('select' in target && typeof target.select === 'function') {
              target.select();
            } else if ('setSelectionRange' in target && typeof target.setSelectionRange === 'function') {
              const inputTarget = target as HTMLInputElement | HTMLTextAreaElement;
              inputTarget.setSelectionRange(0, inputTarget.value?.length || 0);
            } else if (target.contentEditable === 'true') {
              // For contentEditable elements, select all content
              const selection = window.getSelection();
              if (selection) {
                const range = document.createRange();
                range.selectNodeContents(target);
                selection.removeAllRanges();
                selection.addRange(range);
              }
            }
            return false;

          case 'v':
            // Let the browser handle paste naturally - don't intercept
            // This prevents double pasting issues
            return true;

          case 'c':
            e.preventDefault();
            e.stopPropagation();
            // Focus the input field first
            target.focus();
            // Try execCommand first as it works better in webviews
            try {
              const success = document.execCommand('copy');
              if (!success) {
                // Fallback to clipboard API
                let selectedText = '';
                if ('value' in target && 'selectionStart' in target && 'selectionEnd' in target) {
                  const inputTarget = target as HTMLInputElement | HTMLTextAreaElement;
                  selectedText = inputTarget.value.substring(inputTarget.selectionStart || 0, inputTarget.selectionEnd || 0);
                } else if (target.contentEditable === 'true') {
                  const selection = window.getSelection();
                  selectedText = selection ? selection.toString() : '';
                }

                if (selectedText && navigator.clipboard && navigator.clipboard.writeText) {
                  navigator.clipboard.writeText(selectedText).catch(err => {
                    console.log('Clipboard API copy also failed:', err);
                  });
                }
              }
            } catch (err) {
              console.log('execCommand copy threw error:', err);
            }
            return false;

          case 'x':
            e.preventDefault();
            e.stopPropagation();
            // Focus the input field first
            target.focus();
            // Try execCommand first as it works better in webviews
            try {
              const success = document.execCommand('cut');
              if (success) {
                // Trigger input event to notify React
                setTimeout(() => {
                  target.dispatchEvent(new Event('input', { bubbles: true }));
                }, 0);
              } else {
                // Fallback to manual cut
                if ('value' in target && 'selectionStart' in target && 'selectionEnd' in target) {
                  const inputTarget = target as HTMLInputElement | HTMLTextAreaElement;
                  const start = inputTarget.selectionStart || 0;
                  const end = inputTarget.selectionEnd || 0;
                  const selectedTextToCut = inputTarget.value.substring(start, end);
                  if (selectedTextToCut && navigator.clipboard && navigator.clipboard.writeText) {
                    navigator.clipboard.writeText(selectedTextToCut).then(() => {
                      const value = inputTarget.value || '';
                      inputTarget.value = value.substring(0, start) + value.substring(end);
                      inputTarget.setSelectionRange(start, start);
                      // Trigger input event to notify React
                      inputTarget.dispatchEvent(new Event('input', { bubbles: true }));
                    }).catch(err => {
                      console.log('Manual cut also failed:', err);
                    });
                  }
                } else if (target.contentEditable === 'true') {
                  const selection = window.getSelection();
                  if (selection && selection.rangeCount > 0) {
                    const selectedText = selection.toString();
                    if (selectedText && navigator.clipboard && navigator.clipboard.writeText) {
                      navigator.clipboard.writeText(selectedText).then(() => {
                        selection.deleteFromDocument();
                        // Trigger input event
                        target.dispatchEvent(new Event('input', { bubbles: true }));
                      }).catch(err => {
                        console.log('Manual cut for contentEditable also failed:', err);
                      });
                    }
                  }
                }
              }
            } catch (err) {
              console.log('execCommand cut threw error:', err);
            }
            return false;

          case 'z':
            e.preventDefault();
            e.stopPropagation();
            if (e.shiftKey) {
              // Redo
              document.execCommand('redo');
            } else {
              // Undo
              document.execCommand('undo');
            }
            return false;
        }
      }

      // Allow arrow keys with modifiers for text selection
      if (cmdOrCtrl && ['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(e.key)) {
        return true;
      }

      // Allow Backspace and Delete
      if (['Backspace', 'Delete'].includes(e.key)) {
        return true;
      }
    }
  }, true); // Use capture phase to intercept before other handlers

  // Mark that keyboard shortcuts are enabled
  document.documentElement.setAttribute('data-keyboard-shortcuts', 'enabled');
  console.log('Unified keyboard shortcuts enabled for all input fields');
}

// Auto-setup when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', setupKeyboardShortcuts);
} else {
  setupKeyboardShortcuts();
}
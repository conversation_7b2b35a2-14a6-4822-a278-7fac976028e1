/**
 * Shared types for model-related functionality in the frontend.
 * These types break the coupling with backend types while maintaining type safety.
 */

export interface ModelDisplayInfo {
  id: string;
  displayName: string;
  provider: string;
  family: string;
  isDefault?: boolean;
}

export interface ProviderDisplayInfo {
  id: string;
  displayName: string;
  models: ModelDisplayInfo[];
}

export interface ModelSelectionState {
  availableModels: ModelDisplayInfo[];
  currentModel: ModelDisplayInfo | null;
  isLoading: boolean;
  error?: string;
}

export interface ModelDropdownGroup {
  providerId: string;
  providerName: string;
  models: ModelDisplayInfo[];
}

export type ModelChangeHandler = (modelId: string) => void;
import { useEffect, useCallback } from 'react';

export interface KeyboardShortcut {
  key: string;
  metaKey?: boolean;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  description: string;
  action: () => void;
  preventDefault?: boolean;
  enabled?: boolean;
}

interface UseKeyboardShortcutsProps {
  shortcuts: KeyboardShortcut[];
  enabled?: boolean;
}

// Note: This hook is no longer used for keyboard shortcuts
// Keyboard shortcuts are now handled at the extension level in package.json and extension.ts
// This hook is kept for potential future use with webview-specific shortcuts
export const useKeyboardShortcuts = ({ shortcuts, enabled = true }: UseKeyboardShortcutsProps) => {
  // Disabled - keyboard shortcuts are handled by VS Code extension level
  console.log('Keyboard shortcuts are handled at extension level, not webview level');
};

// Predefined common shortcuts (VS Code webview compatible)
export const createCommonShortcuts = () => ({
  // Navigation
  ESCAPE: { key: 'Escape', description: 'Close modals/dialogs' },
  ENTER: { key: 'Enter', description: 'Confirm/Submit' },
  
  // Search (Alt-based to avoid VS Code conflicts)
  SEARCH: { key: 'f', altKey: true, description: 'Search conversations' },
  
  // Chat actions (Alt-based)
  NEW_CHAT: { key: 'n', altKey: true, description: 'New chat' },
  CHAT_HISTORY: { key: 'h', altKey: true, description: 'Open chat history' },
  REGENERATE: { key: 'r', altKey: true, description: 'Regenerate response' },
  
  // File operations
  ATTACH_FILE: { key: 'u', altKey: true, description: 'Attach file' },
  CLEAR_FILES: { key: 'u', ctrlKey: true, shiftKey: true, description: 'Clear attached files' },
  
  // Settings
  OPEN_SETTINGS: { key: 's', altKey: true, description: 'Open settings' },
  
  // Custom instructions
  CUSTOM_INSTRUCTIONS: { key: 'i', altKey: true, description: 'Edit custom instructions' },
  
  // Provider actions
  REFRESH_PROVIDER: { key: 'r', ctrlKey: true, shiftKey: true, description: 'Refresh AI provider' },
  
  // Copy actions
  COPY_LAST_RESPONSE: { key: 'c', altKey: true, description: 'Copy last AI response' },
  COPY_LAST_RESPONSE_ALT: { key: 'c', ctrlKey: true, shiftKey: true, description: 'Copy last AI response (alternative)' },
  
  // Focus actions
  FOCUS_INPUT: { key: '/', description: 'Focus on chat input' },
  FOCUS_INPUT_ALT: { key: 't', altKey: true, description: 'Focus on message input' },
  
  // Navigation actions
  JUMP_TO_BOTTOM: { key: 'j', altKey: true, description: 'Jump to bottom' },
  
  // Help
  SHOW_SHORTCUTS: { key: '?', description: 'Show keyboard shortcuts' },
  SHOW_HELP: { key: 'F1', description: 'Show help' },
  
  // Quick actions
  STOP_GENERATION: { key: 'Escape', description: 'Stop AI generation' },
});

export default useKeyboardShortcuts;
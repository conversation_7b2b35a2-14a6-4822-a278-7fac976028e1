import { useState, useEffect, useCallback } from 'react';
import { ModelDisplayInfo, ModelSelectionState } from '../utils/modelTypes';
import { transformModelData } from '../utils/modelUtils';

/**
 * Custom hook for managing model data and state.
 * Provides a clean interface for components to interact with model data.
 */
export const useModelData = () => {
  const [modelState, setModelState] = useState<ModelSelectionState>({
    availableModels: [],
    currentModel: null,
    isLoading: true,
    error: undefined
  });

  // Load available models from backend
  useEffect(() => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      setModelState(prev => ({ ...prev, isLoading: true }));
      vscodeApi.postMessage({
        type: 'getAvailableModels'
      });
    }

    const handleMessage = (event: MessageEvent) => {
      const message = event.data;

      if (message.type === 'availableModels') {
        try {
          const availableModels = transformModelData(message.models || []);
          const currentModel = message.currentModel 
            ? availableModels.find(m => m.id === message.currentModel) || null
            : null;

          setModelState({
            availableModels,
            currentModel,
            isLoading: false,
            error: undefined
          });
        } catch (error) {
          setModelState(prev => ({
            ...prev,
            isLoading: false,
            error: 'Failed to load model data'
          }));
        }
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  // Get display name for a model
  const getDisplayName = useCallback((modelId: string): string => {
    const model = modelState.availableModels.find(m => m.id === modelId);
    return model?.displayName || modelId;
  }, [modelState.availableModels]);

  // Get models for a specific provider
  const getModelsForProvider = useCallback((providerId: string): ModelDisplayInfo[] => {
    return modelState.availableModels.filter(model => model.provider === providerId);
  }, [modelState.availableModels]);

  // Check if a model is selected
  const isModelSelected = useCallback((modelId: string): boolean => {
    return modelState.currentModel?.id === modelId;
  }, [modelState.currentModel]);

  // Handle model change
  const changeModel = useCallback((modelId: string) => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'changeModel',
        model: modelId
      });
    }
  }, []);

  // Refresh model data
  const refreshModels = useCallback(() => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      setModelState(prev => ({ ...prev, isLoading: true }));
      vscodeApi.postMessage({
        type: 'getAvailableModels'
      });
    }
  }, []);

  return {
    // State
    availableModels: modelState.availableModels,
    currentModel: modelState.currentModel,
    isLoading: modelState.isLoading,
    error: modelState.error,

    // Actions
    getDisplayName,
    getModelsForProvider,
    isModelSelected,
    changeModel,
    refreshModels
  };
};
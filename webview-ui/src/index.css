@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-size: 14px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Global overflow prevention */
  * {
    max-width: 100%;
    box-sizing: border-box;
  }

  /* Prevent text overflow for all elements */
  p, div, span, pre, code {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'SF Pro Display', Roboto, 'Helvetica Neue', sans-serif;
    overflow: hidden;
    overflow-x: hidden;
    font-weight: 400;
    max-width: 100vw;
    box-sizing: border-box;
  }

  /* Modern CSS Variables - VS Code Theme Integration */
  :root {
    /* Typography */
    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'SF Pro Display', Roboto, 'Helvetica Neue', sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  }

  /* VS Code Theme Variables */
  body {
    --bg-primary: var(--vscode-editor-background, #1e1e1e);
    --bg-secondary: var(--vscode-sideBar-background, #252526);
    --bg-tertiary: var(--vscode-input-background, #3c3c3c);
    --bg-hover: var(--vscode-list-hoverBackground, #2a2d2e);
    --bg-active: var(--vscode-list-activeSelectionBackground, #094771);

    --text-primary: var(--vscode-editor-foreground, #cccccc);
    --text-secondary: var(--vscode-descriptionForeground, #cccccc99);
    --text-muted: var(--vscode-disabledForeground, #cccccc80);
    --text-accent: var(--vscode-textLink-foreground, #3794ff);

    --border-primary: var(--vscode-panel-border, #444444);
    --border-secondary: var(--vscode-input-border, #3c3c3c);
    --border-focus: var(--vscode-focusBorder, #007acc);

    --surface-primary: var(--vscode-editor-background, #1e1e1e);
    --surface-secondary: var(--vscode-sideBar-background, #252526);
    --surface-elevated: var(--vscode-dropdown-background, #3c3c3c);

    background: var(--bg-primary);
    color: var(--text-primary);
  }

  /* Light theme overrides */
  body.vscode-light {
    --bg-primary: var(--vscode-editor-background, #ffffff);
    --bg-secondary: var(--vscode-sideBar-background, #f3f3f3);
    --bg-tertiary: var(--vscode-input-background, #ffffff);
    --bg-hover: var(--vscode-list-hoverBackground, #f0f0f0);
    --bg-active: var(--vscode-list-activeSelectionBackground, #0060c0);

    --text-primary: var(--vscode-editor-foreground, #333333);
    --text-secondary: var(--vscode-descriptionForeground, #717171);
    --text-muted: var(--vscode-disabledForeground, #999999);

    --border-primary: var(--vscode-panel-border, #e5e5e5);
    --border-secondary: var(--vscode-input-border, #cecece);

    --surface-primary: var(--vscode-editor-background, #ffffff);
    --surface-secondary: var(--vscode-sideBar-background, #f3f3f3);
    --surface-elevated: var(--vscode-dropdown-background, #ffffff);
  }

  /* High contrast theme */
  body.vscode-high-contrast {
    --border-primary: var(--vscode-contrastBorder, #ffffff);
    --border-secondary: var(--vscode-contrastBorder, #ffffff);
  }
}

@layer components {
  /* Modern Chat Layout */
  .chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
    max-width: 100vw;
    background: var(--surface-primary);
    font-family: var(--font-sans);
    overflow: hidden;
    padding-bottom: 0;
    transition: padding-bottom 0.3s ease;
  }

  /* Sleek Header */
  .chat-header {
    display: flex;
    align-items: center;
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border-primary);
    background: var(--surface-primary);
    backdrop-filter: blur(8px);
    flex-shrink: 0;
  }

  /* New Chat Header */
  .chat-header-new {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-2) var(--space-4);
    border-bottom: 1px solid var(--border-primary);
    background: var(--surface-primary);
    min-height: 48px;
    position: relative;
    z-index: 200;
  }

  .chat-header-left {
    display: flex;
    align-items: center;
    gap: var(--space-3);
  }

  .chat-header-left h2 {
    font-size: 0.875rem;
    font-weight: 700;
    margin: 0;
    color: var(--text-primary);
    letter-spacing: -0.025em;
  }

  /* Aggregate Cost Display */
  .aggregate-cost-display {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    background: var(--surface-secondary);
    border: 1px solid var(--border-secondary);
    border-radius: var(--radius-sm);
    padding: 2px var(--space-2);
    font-size: 0.75rem;
  }

  .aggregate-cost-display .cost-label {
    color: var(--text-muted);
    font-weight: 500;
  }

  .aggregate-cost-display .cost-value {
    color: var(--text-accent);
    font-weight: 600;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  }

  /* Summarization Progress Display */
  .summarization-progress-display {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
    border: 1px solid #f59e0b;
    border-radius: var(--radius-sm);
    padding: var(--space-2) var(--space-3);
    font-size: 0.75rem;
    width: 100%;
    margin: var(--space-2) 0;
  }

  .progress-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    width: 100%;
  }

  .progress-icon {
    font-size: 0.875rem;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .progress-text {
    color: #92400e;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
  }

  .progress-bar {
    width: 40px;
    height: 4px;
    background: rgba(146, 64, 14, 0.2);
    border-radius: 2px;
    overflow: hidden;
    flex-shrink: 0;
  }

  .progress-fill {
    height: 100%;
    background: #92400e;
    border-radius: 2px;
    transition: width 0.3s ease;
  }

  .chat-header-right {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    position: relative;
  }

  .header-icon-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.15s ease;
    padding: 0;
  }

  .header-icon-button:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
  }

  .header-icon-button:active {
    background: var(--bg-active);
    transform: scale(0.95);
  }

  /* Dropdown Menu */
  .dropdown-container {
    position: relative;
    z-index: 1000;
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--space-1);
    background: var(--surface-elevated);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1001;
    min-width: 160px;
    overflow: hidden;
  }

  .dropdown-item {
    display: block;
    width: 100%;
    padding: var(--space-2) var(--space-3);
    background: transparent;
    border: none;
    color: var(--text-primary);
    font-size: 0.875rem;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.15s ease;
  }

  .dropdown-item:hover {
    background: var(--bg-hover);
  }

  /* History Dropdown Styles */
  .history-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--space-1);
    background: var(--surface-elevated);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    min-width: 200px;
    box-shadow: var(--shadow-lg);
    z-index: 9999;
    max-height: 450px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .history-dropdown-header {
    flex-shrink: 0;
    padding: var(--space-2) var(--space-3);
    border-bottom: 1px solid var(--border-primary);
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .history-dropdown-content {
    flex-grow: 1;
    overflow-y: auto;
  }

  .history-empty {
    padding: var(--space-4);
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.875rem;
  }

  .history-conversation-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-2) var(--space-3);
    cursor: pointer;
    transition: background-color 0.1s ease;
  }

  .history-conversation-item:hover {
    background: var(--bg-hover);
  }

  .history-conversation-item.active {
    background-color: var(--bg-active);
  }

  .history-conversation-item.active .history-conversation-title,
  .history-conversation-item.active .history-conversation-meta span {
    color: var(--vscode-list-activeSelectionForeground, #ffffff);
  }

  .history-conversation-details {
    flex-grow: 1;
    min-width: 0;
    margin-right: var(--space-2);
  }

  .history-conversation-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .history-conversation-meta span {
    font-size: 0.75rem;
    color: var(--text-secondary);
  }

  .history-conversation-actions {
    display: flex;
    align-items: center;
    opacity: 0;
    transition: opacity 0.15s ease-in-out;
    flex-shrink: 0;
  }

  .history-conversation-item:hover .history-conversation-actions,
  .history-conversation-item.active .history-conversation-actions {
    opacity: 1;
  }

  .history-action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.1s ease;
  }

  .history-action-button:hover {
    color: var(--text-primary);
  }

  .history-action-button.rename-button:hover {
    background: var(--vscode-button-background, #0e639c);
    color: var(--vscode-button-foreground, #ffffff);
  }

  .history-action-button.delete-button:hover {
    background: var(--vscode-button-secondaryBackground, #c92c2c);
    color: var(--vscode-button-foreground, #ffffff);
  }

  .history-action-button:active {
    transform: scale(0.95);
  }

  .history-dropdown-footer {
    flex-shrink: 0;
    border-top: 1px solid var(--border-primary);
    background: var(--surface-primary);
  }

  .history-view-all {
    display: block;
    width: 100%;
    padding: var(--space-2);
    background: transparent;
    border: none;
    color: var(--text-accent);
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    border-radius: var(--radius-sm);
    transition: background-color 0.15s ease;
  }

  .history-view-all:hover {
    background: var(--bg-hover);
  }

  /* Input positioning */
  .input-top-container {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border-primary);
    background: var(--surface-primary);
    position: relative;
    z-index: 100;
  }

  .input-top-container .sleek-input-wrapper {
    margin: 0;
  }

  .chat-header-content h2 {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
    letter-spacing: -0.025em;
  }

  .chat-header-content p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: var(--space-1) 0 0 0;
    font-weight: 400;
  }

  /* Modern Error Banner */
  .validation-error-banner {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border: 1px solid #fecaca;
    border-left: 4px solid #ef4444;
    margin: var(--space-4) var(--space-6);
    border-radius: var(--radius-lg);
    overflow: hidden;
    animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-sm);
  }

  .vscode-dark .validation-error-banner {
    background: linear-gradient(135deg, #2d1b1b 0%, #3d1a1a 100%);
    border-color: #7f1d1d;
  }

  .validation-error-content {
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
    padding: var(--space-4);
  }

  .validation-error-content svg {
    color: #ef4444;
    flex-shrink: 0;
    margin-top: 2px;
  }

  .validation-error-text {
    flex: 1;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #7f1d1d;
    font-weight: 400;
  }

  .vscode-dark .validation-error-text {
    color: #fca5a5;
  }

  .validation-error-text strong {
    font-weight: 600;
    color: #991b1b;
  }

  .vscode-dark .validation-error-text strong {
    color: #f87171;
  }

  /* Messages Container */
  .messages-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0;
    background: var(--surface-primary);
    scroll-behavior: smooth;
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 100%;
  }

  .messages-container::-webkit-scrollbar {
    width: 6px;
  }

  .messages-container::-webkit-scrollbar-track {
    background: transparent;
  }

  .messages-container::-webkit-scrollbar-thumb {
    background: var(--border-secondary);
    border-radius: 3px;
  }

  .messages-container::-webkit-scrollbar-thumb:hover {
    background: var(--border-primary);
  }

  /* Modern Message Layout */
  .message-container {
    /* padding: var(--space-2) var(--space-6); */
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  .message-container.user {
    background: rgba(255, 255, 255, 0.02);
    padding: 2px var(--space-6);
    border: 1px solid var(--border-secondary);
    cursor: pointer;
    transition: all 0.15s ease;
  }

  .message-container.user:hover {
    background: rgba(255, 255, 255, 0.04);
    cursor: pointer;
  }

  .message-container.ai {
    background: var(--surface-primary);
  }

  /* Avatar Styling - Hidden for clean look */
  .message-avatar {
    display: none;
  }

  .avatar-user {
    display: none;
  }

  .avatar-ai {
    display: none;
  }

  /* Message Content */
  .message-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    max-width: 100%;
    flex: 1;
    min-width: 0;
    width: 100%;
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
  }

  .message-bubble {
    display: flex;
    width: 100%;
    position: relative;
    transition: all 0.2s ease-in-out;
    word-wrap: break-word; /* Ensure long words break */
    display: flex;
    flex-direction: column;
    overflow: hidden; /* To make sure children with border-radius work */
  }

  .message-bubble.message-user {
    background-color: transparent;
    color: var(--text-primary);
    border-radius: 0;
    padding: 0;
    align-self: flex-start;
    max-width: 100%;
  }

  .message-bubble.message-ai {
    background: transparent;
    color: var(--text-primary);
    border-radius: 0;
    padding: 0 !important;
    align-self: flex-start;
    max-width: 95%;
    position: relative;
    word-wrap: break-word;
  }

.message-actions {
  justify-content: flex-end;
  display: flex;
  flex-direction: row;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.message-bubble:hover .message-actions {
  opacity: 1;
}

.message-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.15s ease;
  padding: 0;
}

.message-action-button:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

  /* User Message Styling - Clean full width */
  .message.user {
    display: flex;
    justify-content: flex-start;
    margin-left: 0;
    max-width: 100%;
    gap: var(--space-2);
  }

  .message.user .message-content {
    font-size: 0.875rem;
    line-height: 1.6;
    color: var(--text-primary);
    font-weight: 400;
    width: 100%;
  }

  /* User Message Text */
  .user-message-text {
    font-size: 0.875rem;
    line-height: 1.6;
    color: var(--text-primary);
    font-weight: 400;
    white-space: pre-wrap;
    word-wrap: break-word;
    width: 100%;
  }

  /* AI Message Content */
  .ai-message-content {
    width: 100%;
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
    overflow-x: hidden;
  }

  .ai-message-content .w-md-editor-preview,
  .ai-message-content .wmde-markdown {
    padding: 0;
    margin: 0;
    background: transparent !important;
  }

  .assistant-response .wmde-markdown {
    padding: 0 !important;
  }

  .ai-message-content .w-md-editor-preview h1,
  .ai-message-content .wmde-markdown h1 {
    font-size: 1.5em;
  }

  .ai-message-content .w-md-editor-preview h2,
  .ai-message-content .wmde-markdown h2 {
    font-size: 1.125rem !important;
  }

  .ai-message-content .w-md-editor-preview h3,
  .ai-message-content .wmde-markdown h3 {
    font-size: 1rem !important;
  }

  .ai-message-content .w-md-editor-preview p,
  .ai-message-content .wmde-markdown p {
    margin: var(--space-3) 0 !important;
    color: var(--text-primary) !important;
    line-height: 1.6 !important;
  }

  .ai-message-content .w-md-editor-preview code,
  .ai-message-content .wmde-markdown code {
    background: var(--surface-elevated) !important;
    color: var(--text-accent) !important;
    padding: 2px 6px !important;
    border-radius: var(--radius-sm) !important;
    font-family: var(--font-mono) !important;
    font-size: 0.8125rem !important;
    font-weight: 500 !important;
    border: 1px solid var(--border-secondary) !important;
  }

  .ai-message-content .w-md-editor-preview pre,
  .ai-message-content .wmde-markdown pre {
    background: var(--surface-elevated) !important;
    border: 1px solid var(--border-secondary) !important;
    border-radius: var(--radius-md) !important;
    padding: var(--space-4) !important;
    overflow-x: auto !important;
    overflow-y: hidden !important;
    margin: var(--space-4) 0 !important;
    font-family: var(--font-mono) !important;
    font-size: 0.8125rem !important;
    line-height: 1.5 !important;
    max-width: 100% !important;
    width: 100% !important;
    box-sizing: border-box !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    word-break: break-word !important;
  }

  .ai-message-content .w-md-editor-preview pre code,
  .ai-message-content .wmde-markdown pre code {
    background: transparent !important;
    border: none !important;
    padding: 0 !important;
    color: var(--text-primary) !important;
  }

  .ai-message-content .w-md-editor-preview ul,
  .ai-message-content .wmde-markdown ul {
    padding-left: var(--space-5) !important;
    margin: var(--space-3) 0 !important;
  }

  .ai-message-content .w-md-editor-preview li,
  .ai-message-content .wmde-markdown li {
    margin-bottom: var(--space-1) !important;
    color: var(--text-primary) !important;
  }

  .ai-message-content .w-md-editor-preview blockquote,
  .ai-message-content .wmde-markdown blockquote {
    border-left: 3px solid var(--text-accent) !important;
    padding-left: var(--space-4) !important;
    margin: var(--space-4) 0 !important;
    font-style: italic !important;
    color: var(--text-secondary) !important;
    background: var(--surface-elevated) !important;
    border-radius: 0 var(--radius-md) var(--radius-md) 0 !important;
    padding: var(--space-3) var(--space-4) !important;
  }

  .ai-message-content .w-md-editor-preview table,
  .ai-message-content .wmde-markdown table {
    border-collapse: collapse !important;
    margin: var(--space-4) 0 !important;
    width: 100% !important;
    border: 1px solid var(--border-secondary) !important;
    border-radius: var(--radius-md) !important;
    overflow: hidden !important;
  }

  .ai-message-content .w-md-editor-preview th,
  .ai-message-content .wmde-markdown th,
  .ai-message-content .w-md-editor-preview td,
  .ai-message-content .wmde-markdown td {
    border: 1px solid var(--border-secondary) !important;
    padding: var(--space-2) var(--space-3) !important;
    text-align: left !important;
    font-size: 0.8125rem !important;
  }

  .ai-message-content .w-md-editor-preview th,
  .ai-message-content .wmde-markdown th {
    background: var(--surface-elevated) !important;
    font-weight: 600 !important;
    color: var(--text-primary) !important;
  }

  /* Hide Timestamp */
  .message-timestamp {
    display: none;
  }

  .message.user .message-timestamp {
    color: var(--text-secondary);
  }

  /* Input Area */
  .input-area {
    padding: var(--space-6);
    border-top: 1px solid var(--border-primary);
    background: var(--surface-primary);
    flex-shrink: 0;
  }

  /* Welcome Container */
  .welcome-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: var(--space-12) var(--space-6);
    min-height: 300px;
  }

  .welcome-content {
    text-align: center;
    max-width: 400px;
  }

  .welcome-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    margin: 0 auto var(--space-6);
    background: var(--surface-elevated);
    border-radius: 50%;
    color: var(--text-secondary);
    border: 1px solid var(--border-secondary);
  }

  .welcome-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 var(--space-3) 0;
    color: var(--text-primary);
    letter-spacing: -0.025em;
  }

  .welcome-subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.6;
    font-weight: 400;
  }

  /* Processing Tools Animation - Enhanced UX */
  .processing-tools-container {
    display: flex;
    justify-content: center;
    padding: var(--space-4) var(--space-6);
    animation: slideInFromTop 0.3s ease-out;
  }

  .processing-tools-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    background: linear-gradient(135deg, var(--surface-elevated) 0%, rgba(59, 130, 246, 0.03) 100%);
    border: 1px solid rgba(59, 130, 246, 0.15);
    border-radius: var(--radius-xl);
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    box-shadow: 
      0 8px 25px rgba(0, 0, 0, 0.08), 
      0 3px 8px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    max-width: 420px;
    min-width: 300px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(8px);
    transition: all 0.2s ease;
  }

  .processing-tools-content:hover {
    transform: translateY(-1px);
    box-shadow: 
      0 12px 35px rgba(0, 0, 0, 0.12), 
      0 5px 12px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }

  .processing-tools-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.08), transparent);
    animation: shimmer 2.5s infinite;
    pointer-events: none;
  }

  .processing-info {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    position: relative;
    z-index: 1;
    flex: 1;
  }

  .processing-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    position: relative;
  }

  .processing-spinner svg {
    color: #3b82f6;
    animation: spin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    filter: drop-shadow(0 1px 3px rgba(59, 130, 246, 0.4));
    stroke-width: 2.5;
  }

  .processing-text {
    font-weight: 500;
    color: var(--text-primary);
    position: relative;
    letter-spacing: 0.01em;
    animation: textPulse 2s ease-in-out infinite;
  }

  @keyframes textPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.85; }
  }

  @keyframes slideInFromTop {
    0% {
      opacity: 0;
      transform: translateY(-20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .processing-text::after {
    content: '';
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #3b82f6;
    margin-left: 4px;
    animation: pulse 1.5s ease-in-out infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  @keyframes slideInFromTop {
    from {
      opacity: 0;
      transform: translateY(-8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.6;
      transform: scale(1.2);
    }
  }

  /* Cursor-like Loading Animation */
  .cursor-loading-dots {
    display: flex;
    gap: 4px;
    align-items: center;
  }

  .cursor-loading-dots span {
    width: 6px;
    height: 6px;
    background: var(--text-accent);
    border-radius: 50%;
    animation: cursor-loading 1.4s ease-in-out infinite;
  }

  .cursor-loading-dots span:nth-child(1) { animation-delay: 0s; }
  .cursor-loading-dots span:nth-child(2) { animation-delay: 0.2s; }
  .cursor-loading-dots span:nth-child(3) { animation-delay: 0.4s; }

  @keyframes cursor-loading {
    0%, 60%, 100% {
      opacity: 0.6;
      transform: scale(1);
    }
    30% {
      opacity: 1;
      transform: scale(1.2);
    }
  }

  /* Cursor-style Generating Message */
  .generating-message {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    padding: var(--space-2) var(--space-3);
    background: var(--surface-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-secondary);
    margin: var(--space-2) var(--space-4);
    animation: slideInFromTop 0.3s ease-out;
  }

  .generating-message .generating-text {
    display: flex;
    align-items: center;
    gap: var(--space-1);
  }

  .generating-dots {
    display: inline-flex;
    align-items: center;
    gap: 2px;
  }

  .generating-dots span {
    width: 4px;
    height: 4px;
    background: var(--text-muted);
    border-radius: 50%;
    animation: generating-pulse 1.4s ease-in-out infinite;
    opacity: 0.6;
  }

  .generating-dots span:nth-child(1) { animation-delay: 0s; }
  .generating-dots span:nth-child(2) { animation-delay: 0.2s; }
  .generating-dots span:nth-child(3) { animation-delay: 0.4s; }

  @keyframes generating-pulse {
    0%, 60%, 100% {
      opacity: 0.6;
      transform: scale(1);
    }
    30% {
      opacity: 1;
      transform: scale(1.2);
    }
  }

  /* Enhanced Streaming Cursor */
  .streaming-cursor-enhanced {
    display: inline-block;
    width: 2px;
    height: 1.2em;
    background: var(--text-accent);
    margin-left: 2px;
    animation: blink 1s infinite;
    vertical-align: text-bottom;
    border-radius: 1px;
  }

  /* Light theme adjustments */
  body.vscode-light .generating-message {
    background: var(--surface-secondary);
    border-color: var(--border-secondary);
    color: var(--text-secondary);
  }

  body.vscode-light .generating-dots span {
    background: var(--text-accent);
  }

  /* High contrast theme adjustments */
  body.vscode-high-contrast .generating-message {
    border-color: var(--border-primary);
  }

  body.vscode-high-contrast .generating-dots span {
    background: var(--text-primary);
  }

  /* Mobile responsiveness */
  @media (max-width: 768px) {
    .generating-message {
      margin: var(--space-1) var(--space-2);
      padding: var(--space-1) var(--space-2);
      font-size: 0.8125rem;
    }
    
    .generating-dots span {
      width: 3px;
      height: 3px;
    }
  }

  /* Streaming Cursor */
  .streaming-cursor {
    display: inline-block;
    width: 2px;
    height: 1.2em;
    background: var(--text-accent);
    margin-left: 2px;
    animation: blink 1s infinite;
    vertical-align: text-bottom;
  }

  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }

  /* Markdown Styling */
  .message-bubble .w-md-editor-preview {
    background: transparent !important;
    color: inherit !important;
    font-family: var(--font-sans) !important;
    font-size: 0.875rem !important;
    line-height: 1.6 !important;
  }

  /* Animations */
  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  .message-container {
    animation: fadeIn 0.3s ease-out;
  }

  /* Search highlighting effect */
  .message-container.search-highlighted {
    background: rgba(255, 235, 59, 0.2) !important;
    border: 2px solid rgba(255, 235, 59, 0.5) !important;
    border-radius: var(--radius-md) !important;
    transition: all 0.3s ease !important;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .chat-header {
      padding: var(--space-3) var(--space-4);
    }

    .message-container {
      padding: var(--space-3) var(--space-3);
      gap: var(--space-2);
    }

    .message-container.user,
    .message-container.ai {
      padding: var(--space-3) var(--space-3);
    }

    .input-area {
      padding: var(--space-4) var(--space-3);
    }

    .message-avatar {
      width: 28px;
      height: 28px;
    }

    /* Ensure proper text wrapping on mobile */
    .message-content {
      word-break: break-word;
      overflow-wrap: break-word;
      hyphens: auto;
    }

    .user-message-text {
      word-break: break-word;
      overflow-wrap: break-word;
    }

    /* Compact input wrapper on mobile */
    .sleek-input-wrapper {
      margin: var(--space-2) var(--space-2);
    }

    .sleek-bottom-controls {
      padding: var(--space-1) var(--space-2);
      flex-wrap: wrap;
      gap: var(--space-1);
    }

    .sleek-controls-left {
      gap: var(--space-2);
    }
  }

  /* Extra small screens */
  @media (max-width: 480px) {
    .chat-container {
      font-size: 0.8125rem;
    }

    .message-container {
      padding: var(--space-2) var(--space-2);
    }

    .sleek-input-wrapper {
      margin: var(--space-1) var(--space-1);
    }

    .sleek-bottom-controls {
      min-height: 32px;
    }

    .sleek-mode-dropdown,
    .sleek-model-dropdown {
      font-size: 0.6875rem;
      min-width: 60px;
    }

    .provider-status-compact .provider-model-name {
      max-width: 80px;
    }
  }

  /* Sleek Input Wrapper - Improved focus styling */
  .sleek-input-wrapper {
    position: relative;
    background: var(--surface-secondary);
    border: 1px solid var(--border-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all 0.2s ease;
    margin: var(--space-2) var(--space-4);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .sleek-input-wrapper:focus-within {
    border-color: var(--border-primary);
    box-shadow: 0 0 0 1px rgba(68, 68, 68, 0.2), 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* Main Input Container */
  .sleek-input-container {
    display: flex;
    align-items: flex-end;
    gap: var(--space-2);
    padding: var(--space-2);
    min-height: 40px;
    background: var(--surface-secondary);
  }

  .sleek-input-field-wrapper {
    flex: 1;
    position: relative;
  }

  .sleek-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    color: var(--text-muted);
    pointer-events: none;
    font-size: 0.875rem;
    line-height: 1.4;
    user-select: none;
    transition: color 0.2s ease-in-out;
  }

  .sleek-placeholder.generating {
    color: var(--text-accent);
  }

  .sleek-input-field {
    width: 100%;
    background: transparent;
    border: none;
    outline: none;
    color: var(--text-primary);
    font-size: 0.875rem;
    line-height: 1.4;
    resize: none;
    min-height: 20px;
    max-height: 120px;
    overflow-y: auto;
    overflow-x: hidden;
    word-wrap: break-word;
    word-break: break-word;
    white-space: pre-wrap;
    overflow-wrap: break-word;
  }

  .sleek-input-field:empty::before {
    content: '';
    display: inline-block;
  }

  .sleek-input-field::selection {
    background: var(--text-accent);
    color: var(--text-primary);
  }

  /* Send Button */
  .sleek-send-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: var(--text-accent);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
  }

  .sleek-send-button:hover:not(:disabled) {
    background: #2563eb;
    transform: translateY(-1px);
  }

  .sleek-send-button:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: var(--text-muted);
    transform: none;
  }

  .sleek-send-button:active:not(:disabled) {
    transform: translateY(0);
  }

  /* Stop Button (replaces send button during streaming) */
  .sleek-stop-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: #ef4444;
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
  }

  .sleek-stop-button:hover {
    background: #dc2626;
    transform: translateY(-1px);
  }

  .sleek-stop-button:active {
    transform: translateY(0);
    background: #b91c1c;
  }

  /* Bottom Controls - Horizontal Row */
  .sleek-bottom-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-1) var(--space-2);
    background: var(--surface-secondary);
    border-top: none;
    min-height: 28px;
    border-bottom-left-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
  }

  .sleek-controls-left {
    display: flex;
    align-items: center;
    gap: var(--space-3);
  }

  .sleek-controls-right {
    display: flex;
    align-items: center;
  }

  /* Mode and Model Dropdowns */
  .sleek-mode-container,
  .sleek-model-container {
    position: relative;
  }

  .sleek-mode-dropdown,
  .sleek-model-dropdown {
    appearance: none;
    background: transparent;
    border: 1px solid var(--border-secondary);
    border-radius: var(--radius-sm);
    padding: 4px var(--space-5) 4px var(--space-2);
    font-size: 0.75rem;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
    height: 24px;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 4px center;
    background-repeat: no-repeat;
    background-size: 10px;
  }

  .sleek-model-dropdown {
    border: none;
  }

  .sleek-mode-dropdown:hover,
  .sleek-model-dropdown:hover {
    background: var(--bg-hover);
    border-color: var(--border-primary);
  }

  .sleek-model-dropdown:hover {
    border: none;
  }

  .sleek-mode-dropdown:focus,
  .sleek-model-dropdown:focus {
    outline: none;
    border-color: var(--text-accent);
    box-shadow: 0 0 0 1px rgba(55, 148, 255, 0.1);
  }

  .sleek-model-dropdown:focus {
    border: none;
    box-shadow: none;
  }

  .sleek-model-loading,
  .sleek-model-empty {
    font-size: 0.75rem;
    color: var(--text-muted);
    padding: 4px var(--space-2);
    background: transparent;
    border: none;
    border-radius: var(--radius-sm);
    min-width: 80px;
    height: 24px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Mention Suggestions */
  .sleek-mention-suggestions {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    margin-bottom: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: var(--surface-elevated);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    color: var(--text-secondary);
    box-shadow: var(--shadow-lg);
  }

  /* History Navigation Indicator */
  .history-navigation-indicator {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    margin-bottom: var(--space-1);
    padding: var(--space-1) var(--space-3);
    background: rgba(55, 148, 255, 0.1);
    border: 1px solid var(--text-accent);
    border-radius: var(--radius-md);
    font-size: 0.6875rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-2);
    animation: slideDown 0.2s ease-out;
    backdrop-filter: blur(8px);
  }

  .history-position {
    color: var(--text-accent);
    font-weight: 600;
    font-family: var(--font-mono);
  }

  .history-hint {
    color: var(--text-secondary);
    font-weight: 400;
  }

  /* Light theme adjustments */
  body.vscode-light .history-navigation-indicator {
    background: rgba(55, 148, 255, 0.08);
    border-color: #3794ff;
  }

  body.vscode-light .history-position {
    color: #0066cc;
  }

  /* Adjust messages container for new layout */
  .chat-container:has(.input-top-container) .messages-container {
    flex: 1;
  }

  /* Ensure proper spacing for input at bottom */
  .chat-container:has(.sleek-input-wrapper:last-child) .messages-container {
    flex: 1;
    margin-bottom: 0;
  }
}

/* Light theme adjustments for message backgrounds */
body.vscode-light .message-container.user {
  background: rgba(0, 0, 0, 0.02);
  border-color: var(--border-secondary);
}

body.vscode-light .message-container.user:hover {
  background: rgba(0, 0, 0, 0.04);
}

/* Light theme input focus styling */
body.vscode-light .sleek-input-wrapper:focus-within {
  border-color: var(--border-primary);
  box-shadow: 0 0 0 1px rgba(206, 206, 206, 0.3), 0 1px 3px rgba(0, 0, 0, 0.05);
}

@layer utilities {
  /* Fix z-index issues for dropdowns and popovers */
  [data-radix-popper-content-wrapper] {
    z-index: 9999 !important;
  }

  [data-radix-select-content] {
    z-index: 9999 !important;
  }

  [data-radix-portal] {
    z-index: 9999 !important;
  }

  .select-content-portal {
    z-index: 9999 !important;
  }
}

/* Cursor-like Input Interface */
.cursor-input-wrapper {
  position: relative;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.cursor-input-wrapper:focus-within {
  border-color: var(--accent);
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
}

/* Controls Bar */
.cursor-controls-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-primary);
  min-height: 40px;
}

.cursor-dropdown-container {
  position: relative;
}

.cursor-dropdown {
  appearance: none;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 6px;
  padding: 4px 24px 4px 8px;
  font-size: 12px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 6px center;
  background-repeat: no-repeat;
  background-size: 12px;
}

.cursor-dropdown:hover {
  background: var(--bg-primary);
  border-color: var(--border-primary);
}

.cursor-dropdown:focus {
  outline: none;
  border-color: var(--accent);
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
}

.cursor-dropdown.cursor-loading,
.cursor-dropdown.cursor-empty {
  color: var(--text-muted);
  cursor: not-allowed;
  background-image: none;
}

.cursor-dropdown optgroup {
  font-weight: 600;
  color: var(--text-secondary);
  background: var(--bg-secondary);
}

.cursor-dropdown option {
  background: var(--bg-secondary);
  color: var(--text-primary);
  padding: 4px 8px;
}

.cursor-model-selector {
  margin-left: auto;
}

/* Input Container */
.cursor-input-container {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  padding: 12px;
  min-height: 48px;
}

.cursor-input-field-wrapper {
  flex: 1;
  position: relative;
}

.cursor-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  color: var(--text-muted);
  pointer-events: none;
  font-size: 14px;
  line-height: 1.4;
  user-select: none;
}

.cursor-input-field {
  width: 100%;
  background: transparent;
  border: none;
  outline: none;
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.4;
  resize: none;
  min-height: 20px;
  max-height: 120px;
  overflow-y: auto;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
  overflow-wrap: break-word;
}

.cursor-input-field:empty::before {
  content: '';
  display: inline-block;
}

.cursor-input-field::selection {
  background: var(--accent);
  color: var(--text-primary);
}

/* Send Button */
.cursor-send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--accent);
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.cursor-send-button:hover:not(:disabled) {
  background: var(--accent-hover);
  transform: translateY(-1px);
}

.cursor-send-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: var(--text-muted);
  transform: none;
}

.cursor-send-button:active:not(:disabled) {
  transform: translateY(0);
}

/* Mention Suggestions */
.cursor-mention-suggestions {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 6px;
  font-size: 11px;
  color: var(--text-secondary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Enhanced Chat Header */
.chat-header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 16px 0;
}

.chat-header-content h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.chat-header-content p {
  font-size: 13px;
  color: var(--text-secondary);
  margin: 0;
}

/* Dark theme adjustments */
body.vscode-dark .cursor-dropdown {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%9ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
}

/* Light theme adjustments */
body.vscode-light .cursor-input-wrapper {
  background: #ffffff;
  border-color: #e5e7eb;
}

body.vscode-light .cursor-controls-bar {
  background: #f9fafb;
  border-bottom-color: #e5e7eb;
}

body.vscode-light .cursor-dropdown {
  background: #ffffff;
  border-color: #d1d5db;
  color: #374151;
}

body.vscode-light .cursor-dropdown:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

body.vscode-light .cursor-placeholder {
  color: #9ca3af;
}

body.vscode-light .cursor-input-field {
  color: #374151;
}

body.vscode-light .cursor-mention-suggestions {
  background: #ffffff;
  border-color: #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* High contrast theme adjustments */
body.vscode-high-contrast .cursor-input-wrapper {
  border-width: 2px;
}

body.vscode-high-contrast .cursor-dropdown {
  border-width: 2px;
}

body.vscode-high-contrast .cursor-send-button {
  border: 2px solid var(--accent);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .cursor-controls-bar {
    flex-wrap: wrap;
    gap: 6px;
    padding: 6px 8px;
  }

  .cursor-dropdown {
    font-size: 11px;
    padding: 3px 20px 3px 6px;
    min-width: 70px;
  }

  .cursor-input-container {
    padding: 8px;
  }

  .cursor-send-button {
    width: 28px;
    height: 28px;
  }
}

/* Chat History Styles */
.chat-history-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.2s ease-out;
}

.chat-history-background{
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.chat-history-panel {
  background: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-lg);
  animation: slideUp 0.2s ease-out;
  z-index: 2;
}

.chat-history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  background: var(--surface-secondary);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.chat-history-title {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--text-primary);
}

.chat-history-title h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.chat-history-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.15s ease;
}

.chat-history-close:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.chat-history-search {
  position: relative;
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--border-secondary);
}

.search-icon {
  position: absolute;
  left: calc(var(--space-4) + var(--space-2));
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: var(--space-2) var(--space-2) var(--space-2) var(--space-8);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: border-color 0.15s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(55, 148, 255, 0.1);
}

.search-input::placeholder {
  color: var(--text-muted);
}

.new-session-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--vscode-textCodeBlock-background);
  color: white;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  border-bottom: 1px solid var(--border-secondary);
}

.new-session-button:hover {
  background: var(--vscode-button-secondaryBackground);
  /* transform: translateY(-1px); */
}

.chat-history-content {
  flex: 1;
  overflow-y: auto;
  min-height: 200px;
  max-height: 400px;
}

.chat-history-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  color: var(--text-secondary);
  gap: var(--space-3);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-secondary);
  border-top: 2px solid var(--text-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.chat-history-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  color: var(--text-secondary);
  text-align: center;
  gap: var(--space-2);
}

.chat-history-empty p {
  margin: 0;
  font-weight: 500;
  color: var(--text-primary);
}

.chat-history-empty span {
  font-size: 0.8125rem;
  color: var(--text-muted);
}

.sessions-list {
  padding: var(--space-2);
}

.session-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.15s ease;
  margin-bottom: var(--space-1);
}

.session-item:hover {
  background: var(--bg-hover);
  border-color: var(--border-secondary);
}

.session-item.active {
  background: rgba(55, 148, 255, 0.1);
  border-color: var(--text-accent);
}

.session-content {
  flex: 1;
  min-width: 0;
}

.session-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-1);
}

.session-name {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.session-meta {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  color: var(--text-muted);
  font-size: 0.75rem;
}

.session-preview {
  margin: 0 0 var(--space-2) 0;
  font-size: 0.8125rem;
  color: var(--text-secondary);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.session-stats {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: 0.75rem;
  color: var(--text-muted);
}

.message-count {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.session-model {
  padding: 2px var(--space-1);
  background: var(--surface-elevated);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.session-delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: var(--text-muted);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.15s ease;
  opacity: 0;
}

.session-item:hover .session-delete {
  opacity: 1;
}

.session-delete:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Light theme adjustments */
body.vscode-light .chat-history-overlay {
  background: rgba(0, 0, 0, 0.3);
}

body.vscode-light .chat-history-panel {
  background: #ffffff;
  border-color: #e5e7eb;
}

body.vscode-light .chat-history-header {
  background: #f9fafb;
  border-bottom-color: #e5e7eb;
}

body.vscode-light .search-input {
  background: #ffffff;
  border-color: #d1d5db;
}

body.vscode-light .session-item:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

body.vscode-light .session-model {
  background: #f3f4f6;
  border-color: #d1d5db;
}

/* Responsive design */
@media (max-width: 768px) {
  .chat-history-panel {
    width: 95%;
    max-height: 85vh;
  }

  .session-name {
    max-width: 150px;
  }

  .session-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }
}

/* Conversation Search Styles */
.search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 10001;
  animation: fadeIn 0.2s ease-out;
  padding-top: 10vh;
}

.search-panel {
  background: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 700px;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-lg);
  animation: slideUp 0.2s ease-out;
}

.search-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  background: var(--surface-secondary);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.search-title {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--text-primary);
}

.search-title h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.search-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.15s ease;
}

.search-close:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.search-input-container {
  position: relative;
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-secondary);
}

.search-input-icon {
  position: absolute;
  left: calc(var(--space-4) + var(--space-3));
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  pointer-events: none;
}

.search-input-field {
  width: 100%;
  padding: var(--space-3) var(--space-10) var(--space-3) var(--space-10);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: border-color 0.15s ease;
}

.search-input-field:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(55, 148, 255, 0.1);
}

.search-input-field::placeholder {
  color: var(--text-muted);
}

.search-clear {
  position: absolute;
  right: calc(var(--space-4) + var(--space-2));
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  color: var(--text-muted);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.15s ease;
}

.search-clear:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.recent-searches {
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--border-secondary);
}

.recent-searches-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-2);
  color: var(--text-secondary);
  font-size: 0.75rem;
  font-weight: 500;
}

.recent-searches-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-1);
}

.recent-search-item {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  background: var(--surface-elevated);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.15s ease;
}

.recent-search-item:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
  border-color: var(--border-focus);
}

.search-results {
  flex: 1;
  overflow-y: auto;
  min-height: 200px;
  max-height: 400px;
}

.search-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  color: var(--text-secondary);
  gap: var(--space-3);
}

.search-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  color: var(--text-secondary);
  text-align: center;
  gap: var(--space-2);
}

.search-empty p {
  margin: 0;
  font-weight: 500;
  color: var(--text-primary);
}

.search-empty span {
  font-size: 0.8125rem;
  color: var(--text-muted);
}

.search-results-list {
  padding: var(--space-2);
}

.search-results-header {
  padding: var(--space-2) var(--space-2) var(--space-3);
  color: var(--text-secondary);
  font-size: 0.75rem;
  font-weight: 500;
  border-bottom: 1px solid var(--border-secondary);
  margin-bottom: var(--space-2);
}

.search-result-item {
  padding: var(--space-3);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.15s ease;
  margin-bottom: var(--space-1);
}

.search-result-item:hover,
.search-result-item.selected {
  background: var(--bg-hover);
  border-color: var(--border-secondary);
}

.search-result-item.selected {
  background: rgba(55, 148, 255, 0.1);
  border-color: var(--text-accent);
}

.search-result-content {
  width: 100%;
}

.search-result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-2);
}

.search-result-session {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  flex: 1;
  min-width: 0;
}

.session-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.search-result-meta {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  color: var(--text-muted);
  font-size: 0.75rem;
}

.search-result-snippet {
  margin-bottom: var(--space-2);
  font-size: 0.8125rem;
  line-height: 1.4;
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.snippet-context {
  color: var(--text-secondary);
}

.search-highlight {
  background: rgba(255, 235, 59, 0.3);
  color: var(--text-primary);
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 500;
}

.search-result-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.75rem;
  color: var(--text-muted);
}

.result-type {
  font-weight: 500;
}

.search-footer {
  padding: var(--space-2) var(--space-4);
  border-top: 1px solid var(--border-secondary);
  background: var(--surface-secondary);
  text-align: center;
  font-size: 0.75rem;
  color: var(--text-muted);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

/* Light theme adjustments */
body.vscode-light .search-overlay {
  background: rgba(0, 0, 0, 0.4);
}

body.vscode-light .search-panel {
  background: #ffffff;
  border-color: #e5e7eb;
}

body.vscode-light .search-header {
  background: #f9fafb;
  border-bottom-color: #e5e7eb;
}

body.vscode-light .search-input-field {
  background: #ffffff;
  border-color: #d1d5db;
}

body.vscode-light .search-result-item:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

body.vscode-light .recent-search-item {
  background: #f3f4f6;
  border-color: #d1d5db;
}

body.vscode-light .recent-search-item:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

body.vscode-light .search-highlight {
  background: rgba(255, 235, 59, 0.4);
  color: #1f2937;
}

/* Responsive design */
@media (max-width: 768px) {
  .search-panel {
    width: 95%;
    max-height: 80vh;
  }

  .search-result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }

  .search-result-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }
}

/* Settings Styles */
.settings-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.settings-container {
  height: 100vh;
  width: 100%;
  max-width: 100vw;
  display: flex;
  background: var(--surface-primary);
  font-family: var(--font-sans);
  overflow: hidden;
}

/* Sidebar */
.settings-sidebar {
  width: 280px;
  flex-shrink: 0;
  background: var(--surface-secondary);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.settings-sidebar-header {
  padding: var(--space-6) var(--space-4);
  border-bottom: 1px solid var(--border-secondary);
}

.settings-sidebar-header h1 {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

.settings-sidebar-nav {
  flex: 1;
  padding: var(--space-4) 0;
}

.settings-sidebar-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  text-align: left;
  cursor: pointer;
  transition: all 0.15s ease;
  border-left: 3px solid transparent;
}

.settings-sidebar-item:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.settings-sidebar-item.active {
  background: rgba(55, 148, 255, 0.1);
  color: var(--text-accent);
  border-left-color: var(--text-accent);
}

.sidebar-item-icon {
  font-size: 1rem;
  flex-shrink: 0;
}

.sidebar-item-label {
  flex: 1;
}

/* Main content area */
.settings-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
}

.settings-content {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
}


.settings-message {
  padding: var(--space-3) var(--space-4);
  margin-bottom: var(--space-6);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
}

.settings-message.success {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.settings-message.error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: #ef4444;
}


.settings-section {
  background: var(--surface-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  margin-bottom: var(--space-6);
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section h2 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.section-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0 0 var(--space-4) 0;
  line-height: 1.4;
}

.provider-options {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.provider-option {
  display: flex;
  align-items: center;
  padding: var(--space-4);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.15s ease;
  background: var(--surface-primary);
}

.provider-option:hover {
  background: var(--bg-hover);
  border-color: var(--border-focus);
}

.provider-option input[type="radio"] {
  margin: 0 var(--space-3) 0 0;
  accent-color: var(--text-accent);
}

.provider-info {
  flex: 1;
}

.provider-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  margin-bottom: var(--space-1);
}

.provider-description {
  color: var(--text-secondary);
  font-size: 0.75rem;
  line-height: 1.3;
}

.model-selection {
  margin-bottom: var(--space-4);
}

.model-select {
  width: 100%;
  max-width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.15s ease;
  box-sizing: border-box;
  min-width: 0;
}

.model-select:hover {
  border-color: var(--border-focus);
}

.model-select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(55, 148, 255, 0.1);
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-field label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-input,
.form-select {
  width: 100%;
  max-width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.15s ease;
  box-sizing: border-box;
  min-width: 0;
}

.form-input:hover,
.form-select:hover {
  border-color: var(--border-focus);
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(55, 148, 255, 0.1);
}

.form-input::placeholder {
  color: var(--text-muted);
}

.field-help {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.3;
}

.field-help a {
  color: var(--text-accent);
}

/* Diff Viewer Styles */
.diff-viewer-container {
  margin: var(--space-3) 0;
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  overflow: hidden;
  background: var(--bg-secondary);
}

.diff-header {
  padding: var(--space-3) var(--space-4);
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-3);
}

.diff-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.diff-summary {
  font-size: 0.75rem;
  color: var(--text-secondary);
  background: var(--bg-accent);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
}

.field-help a:hover {
  text-decoration: underline;
}

.settings-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
  margin-top: var(--space-8);
  padding-top: var(--space-6);
  border-top: 1px solid var(--border-primary);
}

.btn {
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.btn-primary {
  background: #0078d4;
  color: white;
  border: 1px solid #0078d4;
}

.btn-primary:hover:not(:disabled) {
  background: #106ebe;
  border-color: #106ebe;
  transform: translateY(-1px);
}

.btn-primary:active:not(:disabled) {
  transform: translateY(0);
  background: #005a9e;
  border-color: #005a9e;
}

.btn-secondary {
  background: var(--surface-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-secondary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-hover);
  border-color: var(--border-focus);
}

.btn-danger {
  background: #d83b01;
  color: white;
  border: 1px solid #d83b01;
}

.btn-danger:hover:not(:disabled) {
  background: #c2370a;
  border-color: #c2370a;
  transform: translateY(-1px);
}

.btn-danger:active:not(:disabled) {
  transform: translateY(0);
  background: #a83100;
  border-color: #a83100;
}

.btn-danger-subtle {
  background: transparent;
  color: #d83b01;
  border: 1px solid var(--border-secondary);
}

.btn-danger-subtle:hover:not(:disabled) {
  background: rgba(216, 59, 1, 0.08);
  border-color: #d83b01;
  color: #c2370a;
}

.btn-danger-subtle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  color: var(--text-muted);
  background: transparent;
  border-color: var(--border-secondary);
}

.btn-disabled {
  background: var(--surface-secondary);
  color: var(--text-muted);
  border: 1px solid var(--border-secondary);
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-disabled:hover {
  background: var(--surface-secondary);
  color: var(--text-muted);
  border: 1px solid var(--border-secondary);
  transform: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Toggle labels for checkboxes */
.toggle-label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
}

.toggle-checkbox {
  accent-color: var(--text-accent);
}

.toggle-text {
  font-weight: 500;
  color: var(--text-primary);
}

/* Responsive Design for Settings */
@media (max-width: 768px) {
  .settings-container {
    flex-direction: column;
  }

  .settings-sidebar {
    width: 100%;
    height: auto;
    max-height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--border-primary);
  }

  .settings-sidebar-nav {
    padding: var(--space-2) 0;
  }

  .settings-sidebar-item {
    padding: var(--space-2) var(--space-4);
    font-size: 0.8125rem;
  }

  .sidebar-item-icon {
    font-size: 0.875rem;
  }

  .settings-content {
    padding: var(--space-4);
  }

  .settings-section {
    padding: var(--space-4);
  }

  .settings-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

/* Light theme adjustments for settings */
body.vscode-light .settings-message.success {
  background: rgba(34, 197, 94, 0.05);
  color: #16a34a;
}

body.vscode-light .settings-message.error {
  background: rgba(239, 68, 68, 0.05);
  color: #dc2626;
}

body.vscode-light .btn-primary {
  background: #0078d4;
  border-color: #0078d4;
}

body.vscode-light .btn-primary:hover:not(:disabled) {
  background: #106ebe;
  border-color: #106ebe;
}

body.vscode-light .btn-danger-subtle {
  color: #d83b01;
}

body.vscode-light .btn-danger-subtle:hover:not(:disabled) {
  background: rgba(216, 59, 1, 0.08);
  border-color: #d83b01;
  color: #c2370a;
}

/* High contrast theme adjustments */
body.vscode-high-contrast .settings-section {
  border: 2px solid var(--border-primary);
}

body.vscode-high-contrast .provider-option,
body.vscode-high-contrast .form-input,
body.vscode-high-contrast .form-select,
body.vscode-high-contrast .model-select {
  border: 2px solid var(--border-primary);
}

body.vscode-high-contrast .btn {
  border: 2px solid var(--border-primary);
}


/* Edit Message Styling */
.edit-message-container {
  width: 100%;
}

.edit-message-textarea {
  width: 100%;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  color: var(--text-primary);
  font-family: var(--font-sans);
  font-size: 0.875rem;
  line-height: 1.4;
  resize: vertical;
  min-height: 60px;
  transition: border-color 0.15s ease;
}

.edit-message-textarea:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(55, 148, 255, 0.1);
}

.edit-message-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-2);
}

.edit-action-button {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  border: none;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.edit-action-button.save {
  background: #0078d4;
  color: white;
  border: 1px solid #0078d4;
}

.edit-action-button.save:hover:not(:disabled) {
  background: #106ebe;
  border-color: #106ebe;
  transform: translateY(-1px);
}

.edit-action-button.save:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--text-muted);
  border-color: var(--text-muted);
  transform: none;
}

.edit-action-button.cancel {
  background: var(--surface-elevated);
  color: var(--text-secondary);
  border: 1px solid var(--border-secondary);
}

.edit-action-button.cancel:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
  border-color: var(--border-focus);
}

/* Light theme adjustments */

body.vscode-light .edit-message-textarea {
  background: #ffffff;
  border-color: #d1d5db;
  color: #374151;
}

body.vscode-light .edit-action-button.save {
  background: #0078d4;
  border-color: #0078d4;
}

body.vscode-light .edit-action-button.cancel {
  background: #ffffff;
  color: #6b7280;
  border-color: #d1d5db;
}

body.vscode-light .edit-action-button.cancel:hover {
  background: #f9fafb;
  color: #374151;
  border-color: #9ca3af;
}

/* High contrast theme adjustments */

body.vscode-high-contrast .edit-message-textarea {
  border: 2px solid var(--border-primary);
}

body.vscode-high-contrast .edit-action-button {
  border: 2px solid var(--border-primary);
}

/* Provider Status Styling */
.provider-status-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  padding: var(--space-2);
  background: var(--surface-elevated);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  min-width: 160px;
}

.provider-status-main {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.status-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  position: relative;
}

.status-dot.connected {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.status-dot.configured {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.status-dot.connecting {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  animation: pulse-status 2s infinite;
}

.status-dot.error {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.status-dot.disconnected,
.status-dot.not-configured {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.status-icon {
  width: 12px;
  height: 12px;
}

.provider-status-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1px;
  min-width: 0;
}

.provider-status-model {
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.provider-status-provider {
  color: var(--text-secondary);
  font-size: 0.6875rem;
  line-height: 1.2;
}

.provider-status-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-2);
  padding-top: var(--space-1);
  border-top: 1px solid var(--border-secondary);
  margin-top: var(--space-1);
}

.status-message-text {
  color: var(--text-secondary);
  font-size: 0.6875rem;
  flex: 1;
}

.status-actions {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.status-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px var(--space-1);
  background: transparent;
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  color: var(--text-secondary);
  font-size: 0.6875rem;
  cursor: pointer;
  transition: all 0.15s ease;
  min-width: 20px;
  height: 20px;
}

.status-action-button:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
  border-color: var(--border-focus);
}

.status-action-button.retry {
  font-weight: 500;
}

.status-action-button.settings {
  padding: 4px;
}

@keyframes pulse-status {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Compact provider status for input area */
.provider-status-compact {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: 2px var(--space-2);
  background: transparent;
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  font-size: 0.6875rem;
  cursor: pointer;
  transition: all 0.15s ease;
}

.provider-status-compact:hover {
  background: var(--bg-hover);
  border-color: var(--border-focus);
}

.provider-status-compact .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.provider-status-compact .status-dot.connected {
  background: #22c55e;
}

.provider-status-compact .status-dot.configured {
  background: #3b82f6;
}

.provider-status-compact .status-dot.connecting {
  background: #f59e0b;
  animation: pulse-status 2s infinite;
}

.provider-status-compact .status-dot.error {
  background: #ef4444;
}

.provider-status-compact .status-dot.disconnected,
.provider-status-compact .status-dot.not-configured {
  background: #6b7280;
}

.provider-status-compact .status-dot .status-icon {
  display: none; /* Hide icon in compact view, just show colored dot */
}

.provider-status-compact .provider-model-name {
  color: var(--text-primary);
  font-weight: 500;
  white-space: nowrap;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Model Dropdown Styles */
.provider-model-selector {
  position: relative;
}

.provider-status-compact .dropdown-arrow {
  margin-left: var(--space-1);
  transition: transform 0.2s ease;
  opacity: 0.6;
}

.provider-status-compact .dropdown-arrow.open {
  transform: rotate(180deg);
}

.model-dropdown-menu {
  position: fixed;
  background: var(--vscode-dropdown-background, var(--surface-primary));
  border: 1px solid var(--vscode-dropdown-border, var(--border-secondary));
  border-radius: var(--radius-sm);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 10000;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 2px;
  min-width: 200px;
  max-width: calc(100vw - 20px);
  @media (max-width: 640px) {
    position: fixed;
    left: 10px !important;
    right: 10px !important;
    width: auto !important;
    max-width: none;
  }
}

.model-group {
  border-bottom: 1px solid var(--border-tertiary);
}

.model-group:last-child {
  border-bottom: none;
}

.model-group-label {
  padding: var(--space-1) var(--space-2);
  font-size: 0.625rem;
  font-weight: 600;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.025em;
  background: var(--surface-tertiary);
  border-bottom: 1px solid var(--border-tertiary);
}

.model-dropdown-item {
  padding: var(--space-1) var(--space-2);
  font-size: 0.6875rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.15s ease;
  border-left: 2px solid transparent;
}

.model-dropdown-item:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.model-dropdown-item.active {
  background: var(--bg-selected);
  border-left-color: var(--text-accent);
  color: var(--text-accent);
  font-weight: 500;
}

.model-dropdown-item.loading,
.model-dropdown-item.empty {
  color: var(--text-muted);
  cursor: default;
  font-style: italic;
}

.model-dropdown-item.loading:hover,
.model-dropdown-item.empty:hover {
  background: transparent;
}

.model-dropdown-item.settings-item {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  border-top: 1px solid var(--border-tertiary);
  margin-top: 2px;
  padding-top: var(--space-1);
  color: var(--text-muted);
  font-weight: 500;
}

.model-dropdown-item.settings-item:hover {
  background: var(--bg-hover);
  color: var(--text-accent);
}

/* Light theme adjustments */
body.vscode-light .provider-status-container {
  background: #ffffff;
  border-color: #e5e7eb;
}

body.vscode-light .status-dot.connected {
  background: rgba(34, 197, 94, 0.08);
  color: #16a34a;
}

body.vscode-light .status-dot.connecting {
  background: rgba(245, 158, 11, 0.08);
  color: #d97706;
}

body.vscode-light .status-dot.error {
  background: rgba(239, 68, 68, 0.08);
  color: #dc2626;
}

body.vscode-light .status-dot.disconnected,
body.vscode-light .status-dot.not-configured {
  background: rgba(107, 114, 128, 0.08);
  color: #6b7280;
}

body.vscode-light .status-action-button {
  border-color: #d1d5db;
  color: #6b7280;
}

body.vscode-light .status-action-button:hover {
  background: #f9fafb;
  color: #374151;
  border-color: #9ca3af;
}

/* High contrast theme adjustments */
body.vscode-high-contrast .provider-status-container {
  border: 2px solid var(--border-primary);
}

body.vscode-high-contrast .status-action-button {
  border: 2px solid var(--border-primary);
}

body.vscode-high-contrast .provider-status-message {
  border-top: 2px solid var(--border-primary);
}

/* Provider Status Banner */
.provider-status-banner {
  margin: var(--space-2) var(--space-4);
  animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Code Block Styling */
.code-block-container {
  margin: var(--space-4) 0;
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  background: var(--surface-elevated);
  overflow: hidden;
  font-family: var(--font-mono);
  font-size: 0.8125rem;
  line-height: 1.5;
  max-width: 100%;
  width: 100%;
  box-sizing: border-box;
}

.code-block-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-2) var(--space-3);
  background: var(--surface-secondary);
  border-bottom: 1px solid var(--border-secondary);
  min-height: 40px;
}

.code-block-info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  flex: 1;
  min-width: 0;
}

.language-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px var(--space-2);
  background: var(--text-accent);
  color: white;
  border-radius: var(--radius-sm);
  font-size: 0.6875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.filename-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px var(--space-2);
  background: var(--surface-elevated);
  color: var(--text-primary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  font-size: 0.6875rem;
  font-weight: 500;
  font-family: var(--font-mono);
}

.code-stats {
  color: var(--text-muted);
  font-size: 0.6875rem;
  font-weight: 400;
}


.code-block-content {
  position: relative;
  background: var(--surface-primary);
}

.code-block-pre {
  margin: 0;
  padding: var(--space-4);
  overflow-x: auto;
  overflow-y: hidden;
  background: transparent;
  font-family: var(--font-mono);
  font-size: 0.8125rem;
  line-height: 1.5;
  color: var(--text-primary);
  max-width: 100%;
  width: 100%;
  box-sizing: border-box;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-word;
}

.code-block-pre::-webkit-scrollbar {
  height: 8px;
}

.code-block-pre::-webkit-scrollbar-track {
  background: var(--surface-elevated);
}

.code-block-pre::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 4px;
}

.code-block-pre::-webkit-scrollbar-thumb:hover {
  background: var(--border-primary);
}

/* Code table for line numbers */
.code-table {
  width: 100%;
  border-collapse: collapse;
  font-family: var(--font-mono);
  font-size: inherit;
  line-height: inherit;
}

.code-line {
  border: none;
}

.line-number {
  padding: 0 var(--space-3) 0 0;
  text-align: right;
  vertical-align: top;
  color: var(--text-muted);
  font-size: 0.75rem;
  user-select: none;
  min-width: 40px;
  border-right: 1px solid var(--border-secondary);
  background: var(--surface-elevated);
  position: sticky;
  left: 0;
}

.line-content {
  padding: 0 0 0 var(--space-3);
  vertical-align: top;
  white-space: pre;
  word-wrap: break-word;
  width: 100%;
}

/* Expand/Collapse Section */
.code-expand-section {
  padding: var(--space-2) var(--space-4);
  border-top: 1px solid var(--border-secondary);
  background: var(--surface-secondary);
  text-align: center;
}

.code-expand-button {
  background: transparent;
  border: none;
  color: var(--text-accent);
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  transition: all 0.15s ease;
}

.code-expand-button:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

/* Language-specific styling */
.language-javascript .line-content,
.language-js .line-content,
.language-typescript .line-content,
.language-ts .line-content {
  color: #f7df1e; /* JavaScript yellow */
}

.language-python .line-content,
.language-py .line-content {
  color: #3776ab; /* Python blue */
}

.language-bash .line-content,
.language-sh .line-content,
.language-shell .line-content {
  color: #4eaa25; /* Terminal green */
}

.language-json .line-content {
  color: #ff6b35; /* JSON orange */
}

.language-css .line-content,
.language-scss .line-content {
  color: #1572b6; /* CSS blue */
}

.language-html .line-content {
  color: #e34f26; /* HTML orange */
}

/* Light theme adjustments */
body.vscode-light .code-block-container {
  background: #ffffff;
  border-color: #e5e7eb;
}

body.vscode-light .code-block-header {
  background: #f9fafb;
  border-bottom-color: #e5e7eb;
}

body.vscode-light .filename-badge {
  background: #ffffff;
  border-color: #d1d5db;
}

body.vscode-light .line-number {
  background: #f9fafb;
  border-right-color: #e5e7eb;
  color: #6b7280;
}

body.vscode-light .code-expand-section {
  background: #f9fafb;
  border-top-color: #e5e7eb;
}


/* High contrast theme adjustments */
body.vscode-high-contrast .code-block-container {
  border: 2px solid var(--border-primary);
}

body.vscode-high-contrast .code-block-header {
  border-bottom: 2px solid var(--border-primary);
}

body.vscode-high-contrast .filename-badge {
  border: 2px solid var(--border-primary);
}

body.vscode-high-contrast .line-number {
  border-right: 2px solid var(--border-primary);
}


/* Responsive Design */
@media (max-width: 768px) {
  .code-block-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
    padding: var(--space-3);
  }


  .code-block-pre {
    padding: var(--space-3);
    font-size: 0.75rem;
  }

  .line-number {
    min-width: 30px;
    padding-right: var(--space-2);
  }

  .line-content {
    padding-left: var(--space-2);
  }
}
/* Stop Button Styles */
.stop-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: 0.8125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 4px 12px rgba(239, 68, 68, 0.25),
    0 2px 4px rgba(239, 68, 68, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.stop-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s;
}

.stop-button:hover::before {
  left: 100%;
}

.stop-button:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-2px);
  box-shadow: 
    0 8px 20px rgba(239, 68, 68, 0.35),
    0 4px 8px rgba(239, 68, 68, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.stop-button:active {
  transform: translateY(-1px);
  box-shadow: 
    0 4px 12px rgba(239, 68, 68, 0.3),
    0 2px 4px rgba(239, 68, 68, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}


/* Update processing tools container to accommodate stop button */
.processing-tools-content {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: var(--surface-elevated);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: 0.875rem;
  box-shadow: var(--shadow-sm);
  justify-content: space-between;
  min-width: 280px;
}

.processing-info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  flex: 1;
}

/* Light theme adjustments */
body.vscode-light .stop-button {
  background: #ef4444;
  color: white;
}

body.vscode-light .stop-button:hover {
  background: #dc2626;
}

/* High contrast theme adjustments */
body.vscode-high-contrast .stop-button {
  border: 2px solid #ff6b6b;
  background: #ef4444;
}

body.vscode-high-contrast .stop-button:hover {
  border-color: #ff5252;
  background: #dc2626;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .processing-tools-content {
    min-width: auto;
    width: 100%;
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .stop-button {
    margin-left: 0;
    margin-top: var(--space-1);
    align-self: flex-end;
  }
}

/* Terminal Styles */
.terminal-container {
  width: 100%;
  background-color: #1e1e1e;
  border-top: 1px solid #333;
  transition: height 0.3s ease;
  overflow: hidden;
  margin-bottom: 0;
}

.terminal-container.collapsed {
  height: 40px;
}

.terminal-container.expanded {
  height: 260px;
}

.terminal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #252526;
  color: #fff;
  font-size: 14px;
  user-select: none;
  height: 40px;
  box-sizing: border-box;
}

.terminal-toggle {
  background: none;
  border: none;
  color: #fff;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

  .terminal-toggle:hover {
  background-color: #333;
}

.xterm-rows span {
  letter-spacing: normal !important;
}

.terminal-content {
  height: 0;
  transition: height 0.3s ease;
  overflow: hidden;
  position: relative;
}

.terminal-content.expanded {
  height: 220px;
  display: block;
}

.terminal-content.collapsed {
  height: 0;
  display: block;
}

/* Remove extra padding at the bottom of chat container */
.chat-container {
  padding-bottom: 0;
}

/* Tool Calls Styling */
.tool-calls-section {
  margin-bottom: var(--space-3);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  background: var(--surface-elevated);
  overflow: hidden;
}

.tool-calls-toggle {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  width: 100%;
  padding: var(--space-2) var(--space-3);
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.15s ease;
}

.tool-calls-toggle:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.tool-calls-summary {
  font-weight: 500;
}

.tool-calls-details {
  margin-left: var(--space-2);
  font-size: 0.6875rem;
  color: var(--text-muted);
  font-weight: 400;
  font-family: var(--font-mono);
  background: rgba(59, 130, 246, 0.08);
  padding: 1px 6px;
  border-radius: var(--radius-sm);
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.param-result {
  margin-top: var(--space-1);
}

.result-output-inline {
  margin: 0;
  padding: var(--space-2);
  font-family: var(--font-mono);
  font-size: 0.6875rem;
  line-height: 1.4;
  color: var(--text-primary);
  background: var(--surface-secondary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  display: block;
}

.result-error-inline {
  padding: var(--space-2);
  font-family: var(--font-mono);
  font-size: 0.6875rem;
  color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: var(--radius-sm);
  line-height: 1.4;
}

.tool-calls-expanded {
  border-top: 1px solid var(--border-secondary);
  background: var(--surface-primary);
}

.tool-call-item {
  padding: var(--space-3);
  border-bottom: 1px solid var(--border-secondary);
}

.tool-call-item:last-child {
  border-bottom: none;
}

.tool-call-header {
  margin-bottom: var(--space-2);
}

.tool-name {
  font-family: var(--font-mono);
  font-size: 0.75rem;
  color: var(--text-accent);
  background: rgba(55, 148, 255, 0.1);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.tool-call-params {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.tool-param {
  display: flex;
  gap: var(--space-2);
  font-size: 0.75rem;
  line-height: 1.4;
}

.param-key {
  color: var(--text-secondary);
  font-weight: 500;
  min-width: 60px;
  flex-shrink: 0;
}

.param-value {
  color: var(--text-primary);
  font-family: var(--font-mono);
  word-break: break-all;
  background: var(--surface-elevated);
  padding: 2px 4px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-secondary);
}

/* Monaco Diff Viewer Styling */
.monaco-diff-viewer {
  margin: var(--space-3) 0;
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  background: var(--surface-elevated);
  overflow: hidden;
}

.diff-header {
  background: var(--surface-secondary);
  border-bottom: 1px solid var(--border-secondary);
}

.diff-toggle {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.15s ease;
}

.diff-toggle:hover {
  background: var(--bg-hover);
}

.diff-file-path {
  font-weight: 600;
  color: var(--text-primary);
}

.diff-summary {
  color: var(--text-secondary);
  font-weight: 400;
  margin-left: auto;
}

.monaco-diff-container {
  background: var(--surface-primary);
  padding: 0;
  border-top: 1px solid var(--border-secondary);
}

.diff-content {
  background: var(--surface-primary);
}

.diff-lines {
  font-size: 0.8125rem;
  line-height: 1.4;
}

.diff-line {
  display: flex;
  align-items: center;
  min-height: 20px;
  border-left: 3px solid transparent;
}

.diff-line-added {
  background: rgba(46, 160, 67, 0.15);
  border-left-color: #2ea043;
}

.diff-line-removed {
  background: rgba(248, 81, 73, 0.15);
  border-left-color: #f85149;
}

.diff-line-unchanged {
  background: transparent;
}

.diff-line:hover {
  background: var(--bg-hover);
}

.diff-line-numbers {
  display: flex;
  flex-direction: column;
  min-width: 80px;
  background: var(--surface-elevated);
  border-right: 1px solid var(--border-secondary);
  padding: 2px var(--space-2);
  font-size: 0.75rem;
  color: var(--text-muted);
  text-align: right;
  line-height: 1.2;
  user-select: none;
}

.diff-line-number {
  display: block;
  height: 10px;
  font-family: var(--font-mono);
}

.diff-line-number.old {
  color: var(--text-muted);
}

.diff-line-number.new {
  color: var(--text-muted);
}

.diff-line-indicator {
  min-width: 20px;
  padding: 2px var(--space-1);
  font-weight: bold;
  text-align: center;
  user-select: none;
}

.diff-line-added .diff-line-indicator {
  color: #2ea043;
}

.diff-line-removed .diff-line-indicator {
  color: #f85149;
}

.diff-line-unchanged .diff-line-indicator {
  color: var(--text-muted);
}

.diff-line-content {
  flex: 1;
  padding: 2px var(--space-2);
  overflow-x: auto;
}

.diff-line-content code {
  background: transparent;
  color: inherit;
  font-family: var(--font-mono);
  font-size: inherit;
  padding: 0;
  border: none;
}

/* Dark theme adjustments */
body.vscode-dark .diff-line-added {
  background: rgba(46, 160, 67, 0.2);
}

body.vscode-dark .diff-line-removed {
  background: rgba(248, 81, 73, 0.2);
}

/* Light theme adjustments */
body.vscode-light .diff-line-added {
  background: rgba(46, 160, 67, 0.1);
}

body.vscode-light .diff-line-removed {
  background: rgba(248, 81, 73, 0.1);
}

/* Message metadata styles */
.message-metadata {
  margin-top: var(--space-2);
  padding-top: var(--space-2);
  border-top: 1px solid var(--border-secondary);
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.metadata-item {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
}

.metadata-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.metadata-value {
  font-weight: 400;
  color: var(--text-primary);
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
}

/* History conversation action buttons */
.history-conversation-actions {
  display: flex;
  gap: var(--space-1);
  opacity: 0;
  transition: opacity 0.2s ease;
  margin-left: auto;
  padding-left: var(--space-2);
}

.history-conversation-item:hover .history-conversation-actions {
  opacity: 1;
}

.history-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.15s ease;
  padding: 0;
}

.history-action-button:hover {
  background: var(--background-hover);
  color: var(--text-primary);
}

.history-action-button.rename-button:hover {
  background: var(--vscode-inputValidation-infoBackground);
  color: var(--vscode-inputValidation-infoForeground);
}

.history-action-button.delete-button:hover {
  background: var(--vscode-inputValidation-errorBackground);
  color: var(--vscode-inputValidation-errorForeground);
}

.history-action-button:active {
  transform: scale(0.95);
}

/* Update conversation item layout to accommodate actions */
.history-conversation-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
  padding: var(--space-3);
  border: none;
  background: transparent;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.15s ease;
  border-radius: 6px;
}

.history-conversation-main {
  flex: 1;
  min-width: 0;
}

.history-conversation-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--space-1);
  font-size: 0.75rem;
  color: var(--text-secondary);
  white-space: nowrap;
}



/* Attached Files Styling */
.attached-files {
  margin-bottom: var(--space-3);
  padding: var(--space-3);
  background: var(--surface-elevated);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  animation: slideDown 0.3s ease;
}

.attached-files-compact {
  padding: var(--space-2);
  margin-bottom: var(--space-2);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 200px;
  }
}

.attached-files-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-2);
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--border-secondary);
}

.attached-files-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.attached-files-clear {
  background: transparent;
  border: none;
  color: var(--text-accent);
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  transition: all 0.15s ease;
}

.attached-files-clear:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.attached-files-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.attached-file {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2);
  background: var(--surface-primary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  transition: all 0.15s ease;
}

.attached-file:hover {
  background: var(--bg-hover);
  border-color: var(--border-focus);
}

.attached-file-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  color: var(--text-accent);
}

.attached-file-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.attached-file-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.attached-file-details {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.attached-file-type {
  background: var(--text-accent);
  color: white;
  padding: 1px var(--space-1);
  border-radius: var(--radius-sm);
  font-size: 0.6875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.attached-file-preview {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-sm);
  overflow: hidden;
  border: 1px solid var(--border-secondary);
  flex-shrink: 0;
}

.attached-file-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.attached-file-remove {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.15s ease;
  flex-shrink: 0;
}

.attached-file-remove:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

/* Image Modal Styles */
.image-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
  padding: var(--space-4);
}

.image-modal-container {
  background: var(--surface-elevated);
  border-radius: var(--radius-lg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-secondary);
  animation: scaleIn 0.2s ease;
}

.image-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-secondary);
}

.image-modal-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.image-modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: var(--space-3);
}

.image-modal-action {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-2) var(--space-3);
  background: var(--primary-500);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.15s ease;
  font-size: 14px;
  font-weight: 500;
}

.image-modal-action:hover {
  background: var(--primary-600);
  transform: translateY(-1px);
}

.image-modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.15s ease;
  flex-shrink: 0;
}

.image-modal-close:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.image-modal-content {
  padding: var(--space-4);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  max-height: calc(90vh - 80px);
  overflow: hidden;
}

.image-modal-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: var(--radius-md);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.attached-file-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: opacity 0.15s ease;
}

.attached-file-thumbnail:hover {
  opacity: 0.8;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Chat interface drag styles */
.chat-container.drag-over {
  background: rgba(55, 148, 255, 0.02);
}

.messages-container.drag-over {
  border: 2px dashed var(--text-accent);
  border-radius: var(--radius-lg);
  background: rgba(55, 148, 255, 0.05);
}

/* Input area drag styles */
.chat-input-container.drag-over {
  background: rgba(55, 148, 255, 0.05);
  border-color: var(--text-accent);
}

/* Light theme adjustments */
body.vscode-light .drop-overlay {
  background: rgba(55, 148, 255, 0.06);
}

body.vscode-light .drop-overlay-content {
  background: #ffffff;
  border-color: #e5e7eb;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

body.vscode-light .attached-files {
  background: #ffffff;
  border-color: #e5e7eb;
}

body.vscode-light .attached-file {
  background: #f9fafb;
  border-color: #e5e7eb;
}

body.vscode-light .attached-file:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

body.vscode-light .attached-files-clear:hover {
  background: #f3f4f6;
}

body.vscode-light .attached-file-remove:hover {
  background: #f3f4f6;
}

/* High contrast theme adjustments */
body.vscode-high-contrast .drop-overlay {
  border: 3px dashed var(--text-accent);
}

body.vscode-high-contrast .drop-overlay-content {
  border: 2px solid var(--border-primary);
}

body.vscode-high-contrast .attached-files {
  border: 2px solid var(--border-primary);
}

body.vscode-high-contrast .attached-file {
  border: 2px solid var(--border-secondary);
}

body.vscode-high-contrast .attached-file-preview {
  border: 2px solid var(--border-secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .drop-overlay-content {
    max-width: 280px;
    padding: var(--space-4);
  }
  
  .drop-text h3 {
    font-size: 1rem;
  }
  
  .drop-text p {
    font-size: 0.8125rem;
    flex-direction: column;
    gap: var(--space-1);
  }
  
  .divider {
    display: none;
  }
  
  .attached-file {
    padding: var(--space-2);
  }
  
  .attached-file-preview {
    width: 28px;
    height: 28px;
  }
}

/* File Picker Styles */
.file-picker {
  display: inline-flex;
  align-items: center;
}

.file-picker-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--surface-elevated);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.file-picker-button:hover:not(:disabled) {
  background: var(--bg-hover);
  color: var(--text-primary);
  border-color: var(--border-focus);
  transform: translateY(-1px);
}

.file-picker-button:active:not(:disabled) {
  transform: translateY(0);
}

.file-picker-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.file-picker-compact {
  padding: var(--space-1) var(--space-2);
  min-width: 32px;
  justify-content: center;
}

.file-picker-compact span {
  display: none;
}

/* Light theme adjustments */
body.vscode-light .file-picker-button {
  background: #ffffff;
  border-color: #d1d5db;
  color: #6b7280;
}

body.vscode-light .file-picker-button:hover:not(:disabled) {
  background: #f9fafb;
  color: #374151;
  border-color: #9ca3af;
}

/* High contrast theme adjustments */
body.vscode-high-contrast .file-picker-button {
  border: 2px solid var(--border-primary);
}

/* Keyboard Shortcuts Styles */
.shortcuts-panel {
  max-width: 700px;
  max-height: 80vh;
  width: 90%;
}

.shortcuts-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-4);
}

.shortcuts-category {
  margin-bottom: var(--space-6);
}

.shortcuts-category:last-child {
  margin-bottom: 0;
}

.shortcuts-category-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-3) 0;
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--border-secondary);
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-2) var(--space-3);
  background: var(--surface-elevated);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  transition: all 0.15s ease;
}

.shortcut-item:hover {
  background: var(--bg-hover);
  border-color: var(--border-focus);
}

.shortcut-keys {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  min-width: 120px;
}

.keyboard-key {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  padding: 0 var(--space-1);
  background: var(--surface-secondary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  font-family: var(--font-mono);
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-primary);
  text-align: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.key-separator {
  color: var(--text-muted);
  font-size: 0.75rem;
  font-weight: 500;
  margin: 0 var(--space-1);
}

.shortcut-description {
  flex: 1;
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-left: var(--space-4);
}

.shortcuts-footer {
  padding: var(--space-3) var(--space-4);
  border-top: 1px solid var(--border-secondary);
  background: var(--surface-secondary);
  text-align: center;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

.shortcuts-footer p {
  margin: 0;
  font-size: 0.75rem;
  color: var(--text-muted);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-1);
}

/* Light theme adjustments */
body.vscode-light .shortcut-item {
  background: #ffffff;
  border-color: #e5e7eb;
}

body.vscode-light .shortcut-item:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

body.vscode-light .keyboard-key {
  background: #f3f4f6;
  border-color: #d1d5db;
  color: #374151;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

body.vscode-light .shortcuts-footer {
  background: #f9fafb;
  border-top-color: #e5e7eb;
}

/* High contrast theme adjustments */
body.vscode-high-contrast .shortcut-item {
  border: 2px solid var(--border-primary);
}

body.vscode-high-contrast .keyboard-key {
  border: 2px solid var(--border-primary);
}

body.vscode-high-contrast .shortcuts-footer {
  border-top: 2px solid var(--border-primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .shortcuts-panel {
    width: 95%;
    max-height: 85vh;
  }
  
  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  .shortcut-keys {
    min-width: auto;
  }
  
  .shortcut-description {
    margin-left: 0;
  }
}

/* Shortcut Tooltip Styles */
.shortcut-tooltip {
  position: fixed;
  bottom: var(--space-4);
  right: var(--space-4);
  background: var(--surface-elevated);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-3);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  max-width: 300px;
  animation: slideInFromBottom 0.3s ease-out;
}

.shortcut-tooltip.action-feedback {
  background: var(--text-accent);
  color: white;
  border-color: var(--text-accent);
  min-width: 200px;
  text-align: center;
}

@keyframes slideInFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.shortcut-tooltip-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.shortcut-tooltip-header {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-weight: 600;
  font-size: 0.75rem;
  color: var(--text-accent);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.shortcut-tooltip-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--space-1);
  flex-wrap: wrap;
}

.shortcut-tooltip-close {
  position: absolute;
  top: var(--space-1);
  right: var(--space-1);
  background: transparent;
  border: none;
  color: var(--text-muted);
  font-size: 1rem;
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  transition: all 0.15s ease;
}

.shortcut-tooltip-close:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

/* Light theme adjustments */
body.vscode-light .shortcut-tooltip {
  background: #ffffff;
  border-color: #e5e7eb;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* High contrast theme adjustments */
body.vscode-high-contrast .shortcut-tooltip {
  border: 2px solid var(--border-primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .shortcut-tooltip {
    bottom: var(--space-2);
    right: var(--space-2);
    left: var(--space-2);
    max-width: none;
  }
}

@media (max-width: 768px) {
  .shortcut-tooltip {
    left: var(--space-4);
    right: var(--space-4);
    bottom: var(--space-4);
  }
}

/* File Write Confirmation Styles */
.file-write-confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.file-write-confirmation-modal {
  background: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.file-write-confirmation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background: var(--surface-secondary);
}

.file-write-confirmation-title {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-weight: 600;
  color: var(--text-primary);
}

.file-write-confirmation-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.file-write-confirmation-info {
  padding: var(--space-4);
  background: var(--surface-secondary);
  border-bottom: 1px solid var(--border-primary);
}

.file-info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-2);
}

.file-info-row:last-child {
  margin-bottom: 0;
}

.file-info-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.file-info-value {
  color: var(--text-primary);
  font-family: var(--font-mono);
  font-size: 0.875rem;
}

.file-write-confirmation-diff {
  flex: 1;
  min-height: 300px;
  overflow: hidden;
}

.file-write-confirmation-actions {
  display: flex;
  gap: var(--space-3);
  padding: var(--space-4);
  border-top: 1px solid var(--border-primary);
  background: var(--surface-secondary);
}

.file-write-confirmation-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--surface-primary);
  color: var(--text-primary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-write-confirmation-button:hover {
  background: var(--surface-hover);
}

.file-write-confirmation-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.file-write-confirmation-button.accept {
  background: var(--color-success);
  color: white;
  border-color: var(--color-success);
}

.file-write-confirmation-button.accept:hover {
  background: var(--color-success-hover);
}

.file-write-confirmation-button.reject {
  background: var(--color-danger);
  color: white;
  border-color: var(--color-danger);
}

.file-write-confirmation-button.reject:hover {
  background: var(--color-danger-hover);
}

/* Inline File Write Confirmation Styles */
.file-write-confirmation-inline {
  background: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  margin: var(--space-4) 0;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.file-write-confirmation-inline .file-write-confirmation-header {
  padding: var(--space-3) var(--space-4);
  background: var(--surface-secondary);
  border-bottom: 1px solid var(--border-primary);
}

.file-write-confirmation-inline .file-write-confirmation-title {
  font-size: 0.875rem;
  font-weight: 600;
}

.file-write-confirmation-inline .file-write-confirmation-info {
  padding: var(--space-3) var(--space-4);
  background: var(--surface-secondary);
  border-bottom: 1px solid var(--border-primary);
}

.file-write-confirmation-inline .file-info-row {
  margin-bottom: var(--space-1);
}

.file-write-confirmation-inline .file-info-label {
  font-size: 0.75rem;
}

.file-write-confirmation-inline .file-info-value {
  font-size: 0.75rem;
}

.file-write-confirmation-inline .file-write-confirmation-diff {
  min-height: 200px;
}

.file-write-confirmation-inline .file-write-confirmation-actions {
  padding: var(--space-3) var(--space-4);
  gap: var(--space-2);
}

.file-write-confirmation-inline .file-write-confirmation-button {
  padding: var(--space-1) var(--space-3);
  font-size: 0.875rem;
}

.file-write-status {
  padding: var(--space-3) var(--space-4);
  text-align: center;
  font-weight: 500;
  border-top: 1px solid var(--border-primary);
}

.file-write-status.accepted {
  background: var(--color-success-bg);
  color: var(--color-success);
}

.file-write-status.rejected {
  background: var(--color-danger-bg);
  color: var(--color-danger);
}

/* Message container for file write confirmation */
.message-container.file-write-confirmation {
  margin: var(--space-4) 0;
}

.message-container.file-write-confirmation .message-content {
  padding: 0;
}

.message-container.file-write-confirmation .message-bubble {
  background: transparent;
  border: none;
  padding: 0;
  box-shadow: none;
}

/* Beautified Accept All / Reject All buttons */
.beautified-action-btn {
  display: flex;
  align-items: center;
  font-weight: 600;
  border: none;
  border-radius: 6px;
  padding: 0.5em 1.2em;
  font-size: 1em;
  box-shadow: 0 1px 4px rgba(0,0,0,0.07);
  transition: background 0.15s, color 0.15s, box-shadow 0.15s;
  cursor: pointer;
  outline: none;
}
.accept-all-btn {
  background: #22c55e;
  color: #fff;
}
.accept-all-btn:hover:not(:disabled) {
  background: #16a34a;
  box-shadow: 0 2px 8px rgba(34,197,94,0.15);
}
.reject-all-btn {
  background: #ef4444;
  color: #fff;
}
.reject-all-btn:hover:not(:disabled) {
  background: #b91c1c;
  box-shadow: 0 2px 8px rgba(239,68,68,0.15);
}
.beautified-action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Scroll to Bottom Button */
.scroll-to-bottom-btn {
  position: fixed;
  bottom: 100px;
  right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--text-accent);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  z-index: 1000;
  animation: fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-to-bottom-btn:hover {
  background: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.scroll-to-bottom-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Animation for button appearance */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Theme-specific styles */
body.vscode-light .scroll-to-bottom-btn {
  background: #0078d4;
  box-shadow: 0 4px 12px rgba(0, 120, 212, 0.25);
}

body.vscode-light .scroll-to-bottom-btn:hover {
  background: #106ebe;
  box-shadow: 0 6px 16px rgba(0, 120, 212, 0.35);
}

body.vscode-high-contrast .scroll-to-bottom-btn {
  border: 2px solid var(--border-primary);
  background: var(--text-accent);
}

body.vscode-high-contrast .scroll-to-bottom-btn:hover {
  border-color: var(--border-focus);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .scroll-to-bottom-btn {
    bottom: 80px;
    right: 16px;
    width: 36px;
    height: 36px;
  }
}

/* Mermaid Diagram Styling */
.mermaid-diagram-container {
  margin: 16px 0;
  border-radius: var(--radius-md);
  overflow: hidden;
  background: var(--vscode-editor-background);
  border: 1px solid var(--vscode-panel-border);
}

.mermaid-diagram-content {
  position: relative;
  background: var(--vscode-editor-background);
  padding: 24px;
  overflow: auto;
  max-width: 100%;
  min-height: 300px;
}

.mermaid-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--vscode-descriptionForeground);
  gap: 12px;
  font-size: 14px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--vscode-progressBar-background, #007acc);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: mermaid-spin 1s linear infinite;
}

@keyframes mermaid-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.mermaid-error {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: var(--vscode-inputValidation-errorBackground);
  border: 1px solid var(--vscode-inputValidation-errorBorder);
  border-radius: 4px;
  color: var(--vscode-inputValidation-errorForeground);
  font-size: 14px;
}

.mermaid-error .error-icon {
  font-size: 18px;
  flex-shrink: 0;
  margin-top: 2px;
}

.mermaid-error strong {
  display: block;
  margin-bottom: 4px;
}

.mermaid-error p {
  margin: 0 0 8px 0;
  font-size: 13px;
}

.mermaid-error details {
  margin-top: 8px;
}

.mermaid-error summary {
  cursor: pointer;
  font-size: 12px;
  color: var(--vscode-textLink-foreground);
  margin-bottom: 8px;
}

.mermaid-error summary:hover {
  color: var(--vscode-textLink-activeForeground);
}

.error-source {
  margin: 0;
  padding: 8px;
  background: var(--vscode-textCodeBlock-background);
  border-radius: 3px;
  font-family: var(--vscode-editor-font-family, var(--font-mono));
  font-size: 12px;
  white-space: pre-wrap;
  overflow-x: auto;
  max-height: 200px;
  border: 1px solid var(--vscode-textSeparator-foreground);
}

.mermaid-output {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  overflow: visible;
  min-height: 200px;
}

.mermaid-output svg {
  max-width: none;
  width: auto;
  height: auto;
  display: block;
  overflow: visible;
}

/* Fix foreignObject text positioning - override all Mermaid styles */
.mermaid-output foreignObject {
  overflow: visible !important;
  height: auto !important;
  min-height: 40px !important;
}

.mermaid-output foreignObject > div {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
  min-height: 40px !important;
  padding: 8px 12px !important;
  box-sizing: border-box !important;
  line-height: 1.3 !important;
  white-space: nowrap !important;
  max-width: none !important;
}

.mermaid-output .nodeLabel {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  width: 100% !important;
  height: 100% !important;
}

.mermaid-output .nodeLabel p {
  margin: 0 !important;
  padding: 0 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  text-align: center !important;
  color: #1a1a1a !important;
}

/* Ultra-specific selectors to override any inherited styles */
.mermaid-output foreignObject div span.nodeLabel p,
.mermaid-output foreignObject div span p,
.mermaid-output foreignObject span.nodeLabel p,
.mermaid-output foreignObject span p,
.mermaid-output foreignObject p,
.mermaid-output .nodeLabel {
  color: #1a1a1a !important;
  fill: #1a1a1a !important;
}

/* Target ALL possible text containers in foreignObjects */
.mermaid-output foreignObject * {
  color: #1a1a1a !important;
}

.zoom-indicator {
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
  min-width: 35px;
  text-align: center;
  font-family: var(--font-mono);
  padding: 0 4px;
}

/* Mermaid theme integration for better VS Code compatibility */

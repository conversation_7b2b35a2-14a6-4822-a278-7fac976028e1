import React, { useState, useEffect } from 'react';
import { Keyboard } from 'lucide-react';

interface ShortcutTooltipProps {
  enabled?: boolean;
  onAction?: (actionName: string) => void;
}

const ShortcutTooltip: React.FC<ShortcutTooltipProps> = ({ enabled = false, onAction }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentAction, setCurrentAction] = useState<string | null>(null);

  useEffect(() => {
    if (!enabled) return;

    // Show tooltip for a few seconds when component first loads
    const timer = setTimeout(() => {
      setIsVisible(true);
      // Hide after 5 seconds
      setTimeout(() => setIsVisible(false), 5000);
    }, 2000); // Show after 2 seconds

    return () => clearTimeout(timer);
  }, [enabled]);

  // Listen for shortcut activation notifications
  useEffect(() => {
    const handleShortcutActivated = (event: CustomEvent) => {
      const actionName = event.detail;
      setCurrentAction(actionName);
      
      // Show action feedback briefly
      setTimeout(() => setCurrentAction(null), 1500);
      
      if (onAction) {
        onAction(actionName);
      }
    };

    window.addEventListener('shortcut-activated', handleShortcutActivated as EventListener);
    return () => {
      window.removeEventListener('shortcut-activated', handleShortcutActivated as EventListener);
    };
  }, [onAction]);

  const isMac = navigator.userAgent.includes('Mac');
  const cmdKey = isMac ? '⌘' : 'Ctrl';

  // Show action feedback
  if (currentAction) {
    return (
      <div className="shortcut-tooltip action-feedback">
        <div className="shortcut-tooltip-content">
          <div className="shortcut-tooltip-text">
            ✓ {currentAction}
          </div>
        </div>
      </div>
    );
  }

  if (!isVisible) return null;

  return (
    <div className="shortcut-tooltip">
      <div className="shortcut-tooltip-content">
        <div className="shortcut-tooltip-header">
          <Keyboard size={14} />
          <span>Quick tip</span>
        </div>
        <div className="shortcut-tooltip-text">
          Press <kbd className="keyboard-key">?</kbd> or <kbd className="keyboard-key">F1</kbd> for keyboard shortcuts
        </div>
      </div>
      <button 
        className="shortcut-tooltip-close"
        onClick={() => setIsVisible(false)}
        title="Dismiss"
      >
        ×
      </button>
    </div>
  );
};

export default ShortcutTooltip;

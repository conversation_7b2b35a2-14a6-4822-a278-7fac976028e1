import React from 'react';
import MarkdownPreview from '@uiw/react-markdown-preview';
import CodeBlock from './CodeBlock';
import MermaidDiagram from './MermaidDiagram';

interface EnhancedMarkdownProps {
  source: string;
  onRunCode?: (code: string, language: string) => void;
  style?: React.CSSProperties;
}

const EnhancedMarkdown: React.FC<EnhancedMarkdownProps> = ({ 
  source, 
  onRunCode,
  style 
}) => {
  // Don't render if source is empty or only whitespace
  if (!source || !source.trim()) {
    return null;
  }

  // Don't render if source is just empty code blocks or tool_code blocks
  const trimmedSource = source.trim();
  if (trimmedSource.match(/^```[\w_]*\s*```$/m) || 
      trimmedSource === '```tool_code\n\n```' ||
      trimmedSource === '```\n\n```') {
    return null;
  }

  // Custom components for markdown rendering
  const components = {
    code: ({ children, className, ...props }: any) => {
      const match = /language-(\w+)/.exec(className || '');
      const language = match ? match[1] : '';
      
      // Special handling for Mermaid - use innerText if available
      let code = '';
      
      if (language === 'mermaid') {
        // Special handling for Mermaid: extract text from complex React element structure
        // The markdown parser creates an array of span elements with text content
        
        // If children is a simple string, use it directly
        if (typeof children === 'string') {
          code = children;
        } else {
          
          // Try to access the original source from props first
          if (props?.children) {
            code = String(props.children);
          } else if (Array.isArray(children)) {
            // Try to recursively extract text from array elements
            const extractText = (item: any): string => {
              if (typeof item === 'string') return item;
              if (typeof item === 'number') return String(item);
              if (item?.props?.children) return extractText(item.props.children);
              if (Array.isArray(item)) return item.map(extractText).join('');
              return '';
            };
            code = children.map(extractText).join('');
          } else {
            code = String(children || '');
          }
          
          // Clean up any remaining object artifacts
          code = code.replace(/\[object Object\]/g, '').trim();
        }
        
        return (
          <MermaidDiagram
            chart={code}
          />
        );
      }
      
      // For non-Mermaid code, use the original logic
      const extractTextFromChildren = (children: any): string => {
        if (typeof children === 'string') {
          return children;
        }
        if (typeof children === 'number') {
          return String(children);
        }
        if (children == null) {
          return '';
        }
        if (Array.isArray(children)) {
          return children.map(child => extractTextFromChildren(child)).join('');
        }
        if (children?.props?.children) {
          return extractTextFromChildren(children.props.children);
        }
        if (typeof children === 'object') {
          return '';
        }
        return String(children);
      };

      code = extractTextFromChildren(children).replace(/\n$/, '');
      
      // Check if it's a code block (has newlines) vs inline code
      const isCodeBlock = typeof children !== 'string' || code.includes('\n');

      if (isCodeBlock) {
        return (
          <CodeBlock
            code={code}
            language={language}
            onRunCode={onRunCode}
          />
        );
      }
      
      // Return inline code as-is
      return (
        <code className={className} {...props}>
          {children}
        </code>
      );
    },
    pre: ({ children, ...props }: any) => {
      // Check if the pre contains a code element
      if (React.isValidElement(children) && children.type === 'code') {
        return children; // Let the code component handle it
      }
      
      // Fallback for regular pre blocks
      return <pre {...props}>{children}</pre>;
    }
  };

  return (
    <MarkdownPreview
      source={source}
      style={style}
      components={components}
      wrapperElement={{
        'data-color-mode': 'dark'
      }}
    />
  );
};

export default EnhancedMarkdown;
import React, { useState, useRef, useEffect } from 'react';
import { ChatInput } from './ChatInput';
import { ChatMessage } from './ChatMessage';
import { Message, ProviderStatusInfo, CurrentProviderInfo, ChatSession, AttachedFile  } from '../types';
import { Models, AIProviders } from '../../../src/ai/types';
import { ModelRegistry } from '../../../src/ai/models/ModelRegistry';
import { AlertTriangle, Plus, History, MoreHorizontal, X, Edit2, Trash2, Square, Search, FileText, Settings, Keyboard, Server, ChevronDown  } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import TerminalInstance from './TerminalInstance';
import LoadingMessage from './LoadingMessage';
import GeneratingMessage from './GeneratingMessage';
import ProviderStatus from './ProviderStatus';
import ChatHistory from './ChatHistory';
import ConversationSearch from './ConversationSearch';
import CustomInstructions from './CustomInstructions';
import AttachedFiles from './AttachedFiles';
import KeyboardShortcuts from './KeyboardShortcuts';
import ShortcutTooltip from './ShortcutTooltip';
import McpServersModal from './McpServersModal';
import AddMcpServerModal from './AddMcpServerModal';

interface ConversationSummary {
  id: string;
  title: string;
  messageCount: number;
  updatedAt: number;
  preview?: string;
  isActive?: boolean;
  metadata?: {
    isInterrupted?: boolean;
    interruptedAt?: number;
    interruptionReason?: 'user_stop' | 'error' | 'timeout' | 'manual_switch';
    canResume?: boolean;
    lastInterruptedAt?: number;
    lastInterruptionReason?: 'user_stop' | 'error' | 'timeout' | 'manual_switch';
  };
}

export const ChatView: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentStreamingMessage, setCurrentStreamingMessage] = useState<string>('');
  const [validationError, setValidationError] = useState<string | null>(null);
  const [isProviderAvailable, setIsProviderAvailable] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isUserScrolledUp, setIsUserScrolledUp] = useState(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [inputPosition, setInputPosition] = useState<'top' | 'bottom'>('top');
  const [showDropdown, setShowDropdown] = useState(false);
  const [showHistoryDropdown, setShowHistoryDropdown] = useState(false);
  const [conversations, setConversations] = useState<ConversationSummary[]>([]);
  const [isProcessingTools, setIsProcessingTools] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const historyDropdownRef = useRef<HTMLDivElement>(null);
  const [conversationLoading, setConversationLoading] = useState(false);
  const [processingMessage, setProcessingMessage] = useState("Working on your request...");
  
  // Chat history state
  const [showChatHistory, setShowChatHistory] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [currentSessionName, setCurrentSessionName] = useState<string>('New chat');
  
  // Conversation search state
  const [showConversationSearch, setShowConversationSearch] = useState(false);
  
  // Custom instructions state
  const [currentCustomInstructions, setCurrentCustomInstructions] = useState<string>('');
  const [showCustomInstructionsModal, setShowCustomInstructionsModal] = useState(false);
  
  // File attachment state
  const [attachedFiles, setAttachedFiles] = useState<AttachedFile[]>([]);
  
  // Keyboard shortcuts state
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);
  
  // MCP servers state
  const [showMcpServers, setShowMcpServers] = useState(false);
  const [showAddMcpServer, setShowAddMcpServer] = useState(false);
  const [editingMcpServer, setEditingMcpServer] = useState<any>(null);
  
  // Diff data state - stores diff info by message timestamp
  const [diffDataMap, setDiffDataMap] = useState<Map<number | string, any>>(new Map());
  
  const validationInProgressRef = useRef(false);
  const validateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const timeoutRefs = useRef<Set<NodeJS.Timeout>>(new Set());  
  const [currentProvider, setCurrentProvider] = useState<CurrentProviderInfo>(() => {
    const defaultModel = ModelRegistry.getDefaultModelForProvider(AIProviders.AwsBedrock) || Models.Claude_3_7_Sonnet;
    return {
      provider: AIProviders.AwsBedrock,
      model: defaultModel,
      status: {
        status: 'not-configured',
        message: 'Not configured',
        lastChecked: new Date()
      }
    };
  });

  // Cost tracking state - these will be loaded from settings
  const [showAggregateCost, setShowAggregateCost] = useState(true);
  const [showCostPerMessage, setShowCostPerMessage] = useState(false);

  // Auto-compact and summarization state
  const [isSummarizing, setIsSummarizing] = useState(false);
  const [summarizationProgress, setSummarizationProgress] = useState<{
    stage: string;
    message: string;
    progress?: number;
  } | null>(null);

  const [sessionNotReady, setSessionNotReady] = useState(false);

  // Save state to VS Code state API whenever it changes
  useEffect(() => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi && messages.length > 0) {
      const state = {
        messages,
        inputPosition,
        currentSessionId,
        currentSessionName,
        currentCustomInstructions,
        attachedFiles,
        showAggregateCost,
        showCostPerMessage,
        currentProvider,
        validationError,
        isProviderAvailable
      };
      (vscodeApi as any).setState(state);
    }
  }, [messages, inputPosition, currentSessionId, currentSessionName, currentCustomInstructions, attachedFiles, showAggregateCost, showCostPerMessage, currentProvider, validationError, isProviderAvailable]);

  // Restore state from VS Code state API on component mount
  useEffect(() => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      const savedState = (vscodeApi as any).getState();
      if (savedState) {
        console.log('[ChatView.tsx] Restoring saved state...');
        if (savedState.messages) setMessages(savedState.messages);
        if (savedState.inputPosition) setInputPosition(savedState.inputPosition);
        if (savedState.currentSessionId) setCurrentSessionId(savedState.currentSessionId);
        if (savedState.currentSessionName) setCurrentSessionName(savedState.currentSessionName);
        if (savedState.currentCustomInstructions) setCurrentCustomInstructions(savedState.currentCustomInstructions);
        if (savedState.attachedFiles) setAttachedFiles(savedState.attachedFiles);
        if (savedState.showAggregateCost !== undefined) setShowAggregateCost(savedState.showAggregateCost);
        if (savedState.showCostPerMessage !== undefined) setShowCostPerMessage(savedState.showCostPerMessage);
        if (savedState.currentProvider) setCurrentProvider(savedState.currentProvider);
        if (savedState.validationError) setValidationError(savedState.validationError);
        if (savedState.isProviderAvailable !== undefined) setIsProviderAvailable(savedState.isProviderAvailable);
        
        return;
      }
    }
  }, []);

  // Calculate aggregate cost
  const calculateAggregateCost = () => {
    return messages.reduce((total, message) => {
      return total + (message.metadata?.cost || 0);
    }, 0);
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    setIsUserScrolledUp(false);
    setShowScrollToBottom(false);
  };

  // Check if user is near bottom of chat
  const isNearBottom = (element: HTMLElement) => {
    const threshold = 100; // pixels from bottom
    return element.scrollTop + element.clientHeight >= element.scrollHeight - threshold;
  };

  // Handle scroll event
  const handleScroll = () => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const nearBottom = isNearBottom(container);
    setIsUserScrolledUp(!nearBottom);
    setShowScrollToBottom(!nearBottom);
  };

  useEffect(() => {
    // Only auto-scroll if user hasn't manually scrolled up
    if (!isUserScrolledUp) {
      scrollToBottom();
    }
  }, [messages, currentStreamingMessage, isUserScrolledUp]);

  // Set up scroll event listener
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, []);

  // Auto-save session when messages change (debounced)
  useEffect(() => {
    if (messages.length > 0) {
      const timeoutId = setTimeout(() => {
        saveCurrentSession();
      }, 2000); // Save 2 seconds after last message

      return () => clearTimeout(timeoutId);
    }
  }, [messages]);

  // New state for input value
  const [inputValue, setInputValue] = useState("");

  // Add a flag and expected count for robust checkpoint restore
  const restoringCheckpointRef = useRef(false);
  const expectedMessageCountRef = useRef(0);

  // Function definitions (moved up to avoid hoisting issues)
  const handleNewChat = () => {
    // Save current session before starting new one
    saveCurrentSession();

    // Reset to new session
    setMessages([]);
    setInputPosition('top');
    setInputValue(""); // Clear input box to remove any restored message from previous chat
    setValidationError(null);
    setCurrentStreamingMessage('');
    setIsLoading(false);
    setIsStreaming(false);
    setCurrentSessionId(null); // Do not generate a uuid here
    setCurrentSessionName('New chat');
    setCurrentCustomInstructions('');

    // Notify backend to start new conversation
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'startNewSession'
      });
    }
  };

  const handleSettingsClick = () => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'openSettings'
      });
    }
  };

  const handleAddMcpServer = (serverConfig: any) => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'addMcpServer',
        serverConfig: serverConfig
      });
    }
  };

  const handleEditMcpServer = (server: any) => {
    console.log('⚙️ Edit server clicked:', server.name, server);
    setEditingMcpServer(server);
    setShowAddMcpServer(true);
    setShowMcpServers(false);
  };

  const handleUpdateMcpServer = (serverConfig: any) => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'updateMcpServer',
        serverConfig: serverConfig
      });
    }
  };

  const validateConfiguration = () => {
    if (validationInProgressRef.current) {
      return;
    }

    if (validateTimeoutRef.current) {
      clearTimeout(validateTimeoutRef.current);
    }

    validateTimeoutRef.current = setTimeout(() => {
      validationInProgressRef.current = true;
      
      const vscodeApi = window.vscode || window.__vscodeApi;
      if (vscodeApi) {
        vscodeApi.postMessage({
          type: 'validateConfiguration'
        });
      } else {
        validationInProgressRef.current = false;
      }
    }, 100);
  };

  const handleRetryConnection = () => {
    try {
      // Set connecting status
      setCurrentProvider(prev => ({
        ...prev,
        status: {
          status: 'connecting',
          message: 'Retrying connection...',
          lastChecked: new Date()
        }
      }));

      validateConfiguration();
    } catch (error) {
      console.error('Error in handleRetryConnection:', error);
      setValidationError('Failed to retry connection');
    }
  };

  const handleRegenerateMessage = (messageId: number) => {
    // Find the message to regenerate
    const messageIndex = messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) return;

    // Get the last user message before this AI response
    let lastUserMessage = null;
    for (let i = messageIndex - 1; i >= 0; i--) {
      if (messages[i].sender === 'user') {
        lastUserMessage = messages[i];
        break;
      }
    }

    if (!lastUserMessage) return;

    // Remove all messages after the AI message being regenerated
    setMessages(prev => prev.slice(0, messageIndex));
    
    // Resend the user message to generate a new response
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'sendMessage',
        content: lastUserMessage.content
      });
    }
  };

  const saveCurrentSession = () => {
    if (messages.length === 0) return;

    const sessionData: ChatSession = {
      id: currentSessionId || uuidv4(),
      name: currentSessionName || generateSessionName(messages),
      messages: messages,
      createdAt: currentSessionId ? new Date() : new Date(), // Keep original if existing
      updatedAt: new Date(),
      provider: currentProvider.provider,
      model: currentProvider.model,
      customInstructions: currentCustomInstructions
    };

    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'saveChatSession',
        session: sessionData
      });
    }

    if (!currentSessionId) {
      setCurrentSessionId(sessionData.id);
      setCurrentSessionName(sessionData.name);
    }
  };

  const generateSessionName = (messages: Message[]): string => {
    const userMessages = messages.filter(msg => msg.sender === 'user');
    if (userMessages.length === 0) return 'New chat';
    
    const firstUserMessage = userMessages[0].content;
    // Take first 30 characters and clean up
    let name = firstUserMessage.substring(0, 30).trim();
    if (firstUserMessage.length > 30) {
      name += '...';
    }
    return name;
  };

  // Note: Keyboard shortcuts are handled at the extension level
  // See package.json keybindings and extension.ts for implementation


  useEffect(() => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    console.log('[ChatView.tsx] Initializing chat view...');

    // Load conversations on component mount
    const loadConversations = () => {
      if (vscodeApi) {
        vscodeApi.postMessage({
          type: 'loadConversations'
        });
      }
    };

    loadConversations();

    const handleMessage = (event: MessageEvent) => {
      try {
        const message = event.data;
        console.log('🔍 ChatView received message:', message.type, message);

        switch (message.type) {
        case 'messageStart':
          setIsLoading(true);
          setCurrentStreamingMessage('');
          setValidationError(null);
          break;
        case 'messageChunk':
          if (message.text) {
            setCurrentStreamingMessage(prev => prev + message.text);
          }
          break;
        case 'messageComplete':
          setIsLoading(false);
          
          // Move streaming content to messages array
          setMessages(prev => {
            const newMessages = [...prev];
            if (currentStreamingMessage) {
              // Add the accumulated streaming content as a final message
              newMessages.push({
                id: Date.now(),
                sender: 'bot',
                content: currentStreamingMessage,
                timestamp: new Date(),
                metadata: message.metadata || {}
              });
            } else if (message.metadata && newMessages.length > 0 && newMessages[newMessages.length - 1].sender === 'bot') {
              // Add metadata to existing last message if no streaming content
              const lastMessage = newMessages[newMessages.length - 1];
              lastMessage.metadata = {
                ...lastMessage.metadata,
                ...message.metadata
              };
            }
            return newMessages;
          });
          
          setCurrentStreamingMessage('');
          
          setCurrentProvider(prev => ({
            ...prev,
            status: {
              status: 'connected',
              message: 'Connected and ready',
              lastChecked: new Date()
            }
          }));
          break;
        case 'messageError':
          setIsLoading(false);
          const errorMessage = message.error || 'An unknown error occurred';

          // Check if it's a validation error
          if (errorMessage.includes('not supported by') ||
              errorMessage.includes('not compatible with') ||
              errorMessage.includes('requires') ||
              errorMessage.includes('Model') && errorMessage.includes('provider')) {
            setValidationError(errorMessage);
          } else {
            setMessages(prev => [...prev, {
              id: Date.now(),
              sender: 'bot',
              content: `Error: ${errorMessage}`,
              timestamp: new Date()
            }]);
          }
          setCurrentStreamingMessage('');
          break;
        case 'providerRefreshed':
          setIsProviderAvailable(message.isAvailable);
          // Update provider status based on availability
          setCurrentProvider(prev => ({
            ...prev,
            status: {
              status: message.isAvailable ? 'connected' : 'error',
              message: message.isAvailable ? 'Connected and ready' : 'Provider not available',
              lastChecked: new Date()
            }
          }));
          break;
        case 'validationError':
          validationInProgressRef.current = false;          
          setValidationError(message.error);
          // Update provider status on validation error
          setCurrentProvider(prev => ({
            ...prev,
            status: {
              status: 'error',
              message: message.error,
              lastChecked: new Date()
            }
          }));
          break;
        case 'validationResult':
          validationInProgressRef.current = false;
          try {
            if (message.isValid) {
              const newModel = message.currentModel;
              const newProvider = message.currentProvider;
              
              if (newModel && newProvider) {
                const expectedProvider = ModelRegistry.getProvider(newModel as Models);
                if (expectedProvider && expectedProvider !== newProvider) {
                  throw new Error(`Model ${newModel} requires ${expectedProvider} provider, got ${newProvider}`);
                }
              }
              

              setValidationError(null);
              setCurrentProvider(prev => ({
                provider: newProvider || prev.provider,
                model: newModel || prev.model,
                status: {
                  status: 'connecting',
                  message: 'Testing credentials...',
                  lastChecked: new Date()
                }
              }));
              
              if (newModel && newProvider) {
                const vscodeApi = window.vscode || window.__vscodeApi;
                if (vscodeApi) {
                  vscodeApi.postMessage({
                    type: 'testModelCredentials',
                    model: newModel
                  });
                }
              }
            } else {
              setValidationError(message.error);
              setCurrentProvider(prev => ({
                ...prev,
                status: {
                  status: 'error',
                  message: message.error,
                  lastChecked: new Date()
                }
              }));
            }
          } catch (error) {
            console.error('Error processing validation result:', error);
            setValidationError(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
            setCurrentProvider(prev => ({
              ...prev,
              status: {
                status: 'error',
                message: 'Configuration validation failed',
                lastChecked: new Date()
              }
            }));
          }
          break;
        case 'availableModels':
          break;
        case 'modelChanged':
          try {
            const newModel = message.model as Models;
            
            if (!ModelRegistry.isModelValid(newModel)) {
              throw new Error(`Invalid model: ${newModel}`);
            }
            
            const displayName = ModelRegistry.getDisplayName(newModel);
            
            setValidationError(null);
            setCurrentProvider(prev => ({
              ...prev,
              model: newModel,
              status: {
                status: 'connecting',
                message: 'Testing credentials...',
                lastChecked: new Date()
              }
            }));
            
            const vscodeApi = window.vscode || window.__vscodeApi;
            if (vscodeApi) {
              vscodeApi.postMessage({
                type: 'testModelCredentials',
                model: newModel
              });
            }
            
            setMessages(prev => [...prev, {
              id: Date.now(),
              sender: 'bot',
              content: `Model changed to ${displayName}`,
              timestamp: new Date()
            }]);
          } catch (error) {
            console.error('Error processing model change:', error);
            setValidationError(`Failed to change model: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
          break;
        case 'modelChangeError':
          setValidationError(`Failed to change model: ${message.error}`);
          break;
        case 'streamingStart':
          setIsStreaming(true);
          break;
        case 'streamingEnd':
          setIsStreaming(false);
          break;

        case 'toolProcessing':
          setIsProcessingTools(message.isProcessing);
          if (message.isProcessing) {
            // Set contextual processing message
            const context = message.context || 'default';
            const contextMessages = {
              writing: [
                "✏️ Writing files...",
                "📝 Making changes...",
                "🔧 Updating your code...",
                "💾 Saving modifications...",
              ],
              reading: [
                "📖 Reading files...",
                "🔍 Analyzing your code...",
                "🔎 Searching through files...",
                "📋 Examining content...",
              ],
              exploring: [
                "🗂️ Exploring your project...",
                "📁 Looking through files...",
                "🔍 Scanning directories...",
                "📊 Mapping file structure...",
              ],
              analyzing: [
                "🧠 Analyzing...",
                "⚡ Processing request...",
                "🤔 Working on it...",
                "🔄 Computing solution...",
              ],
              default: [
                "🚀 Working on your request...",
                "💭 Thinking...",
                "⚙️ Processing...",
                "✨ Almost there...",
              ]
            };
            
            const messages = contextMessages[context as keyof typeof contextMessages] || contextMessages.default;
            setProcessingMessage(messages[Math.floor(Math.random() * messages.length)]);
          }
          break;

        case 'toolResult':
          const toolResult = message.result;
          if (toolResult.error) {
            setMessages(prev => [...prev, {
              id: Date.now(),
              content: `Tool Error: ${toolResult.error}`,
              sender: 'bot',
              timestamp: new Date()
            }]);
          } else {
            let resultMessage = '';
            switch (message.toolCall.name) {
              case 'read_file':
                resultMessage = `File read successfully: ${toolResult.result.path}`;
                break;
              case 'write_file':
                resultMessage = `File written successfully: ${toolResult.result.path}`;
                break;
              case 'list_files':
                const fileCount = toolResult.result.files.length;
                resultMessage = `Found ${fileCount} files:\n${toolResult.result.files
                  .map((f: any) => `- ${f.path}`)
                  .join('\n')}`;
                break;
              case 'search_files':
                const { totalMatches } = toolResult.result;
                if (totalMatches === 0) {
                  resultMessage = `No matches found in your project`;
                } else {
                  resultMessage = `Found ${totalMatches} matches in your project\n${
                    toolResult.result.results
                      .map((r: any) => `File: ${r.file}\n${r.matches
                        .map((m: any) => `  Line ${m.line}: ${m.text}`)
                        .join('\n')}`)
                      .join('\n\n')}`;
                }
                break;
              case 'execute_command':
                // Terminal bubble is created via createTerminalBubble message, not via tool result
                resultMessage = toolResult.result?.message || 'Terminal created successfully';
                break;
              case 'get_file_summary':
                resultMessage = toolResult.result?.message || 'File analyzed successfully';
                break;
              case 'hybrid_search':
                resultMessage = `Tool ${message.toolCall.name} executed successfully`;
                break;
              default:
                resultMessage = `Tool ${message.toolCall.name} executed successfully`;
            }

            setMessages(prev => [...prev, {
              id: Date.now(),
              content: resultMessage,
              sender: 'bot',
              timestamp: new Date()
            }]);
          }
          break;
        case 'conversationsLoaded':
          setConversations(message.conversations || []);
          break;

        case 'conversationCreated':
          loadConversations(); // Refresh the list

          // If conversationId is null, this is a fresh start - clear the UI
          if (message.conversationId === null) {
            setMessages([]);
            setInputPosition('top');
            setValidationError(null);
            setCurrentStreamingMessage('');
            setIsLoading(false);
            setIsStreaming(false);
            setConversationLoading(false);
          }
          break;

        case 'conversationSwitched':
          // Start loading state when conversation switch begins
          setConversationLoading(true);
          setShowHistoryDropdown(false);
          // Note: Messages will be loaded via the messagesChanged event
          break;

        case 'testMessage':
          break;
          
        case 'messagesChanged':
          console.log(`🔍 [FRONTEND_DEBUG] Messages changed event received`);
          console.log(`🔍 [FRONTEND_DEBUG] Event conversation ID: ${message.conversationId}`);
          console.log(`🔍 [FRONTEND_DEBUG] Current session ID: ${currentSessionId}`);
          console.log(`🔍 [FRONTEND_DEBUG] New messages count: ${message.messages?.length || 0}`);
          console.log(`🔍 [FRONTEND_DEBUG] Current messages count: ${messages.length}`);
          console.log(`🔍 [FRONTEND_DEBUG] Restoring checkpoint flag: ${restoringCheckpointRef.current}`);
          
          // Convert backend messages to frontend format
          const convertBackendMessages = (backendMessages: any[]): Message[] => {
            return backendMessages.map((msg: any): Message => {
              const sender: "user" | "bot" = msg.type === 'user' ? 'user' : 'bot';
              let attachedFiles: AttachedFile[] | undefined = undefined;
              if (msg.metadata?.attachedFiles) {
                try {
                  attachedFiles = JSON.parse(msg.metadata.attachedFiles);
                } catch (error) {
                  console.warn('Failed to parse attached files from message metadata:', error);
                }
              }
              return {
                id: msg.id || Date.now(),
                content: msg.content,
                sender: sender,
                timestamp: new Date(msg.timestamp),
                metadata: msg.metadata,
                attachedFiles: attachedFiles,
                type: msg.type,
                sessionId: msg.sessionId,
              };
            });
          };

          if (restoringCheckpointRef.current) {
            console.log(`🔍 [FRONTEND_DEBUG] Restore checkpoint in progress, accepting backend message list`);
            if (message.messages && Array.isArray(message.messages)) {
              console.log(`🔍 [FRONTEND_DEBUG] Converting ${message.messages.length} backend messages for checkpoint restore`);
              const convertedMessages = convertBackendMessages(message.messages);
              setMessages(convertedMessages);
            } else {
              console.log(`🔍 [FRONTEND_DEBUG] No messages array during checkpoint restore, setting empty messages`);
              setMessages([]);
            }
            restoringCheckpointRef.current = false;
            expectedMessageCountRef.current = 0;
            console.log(`🔍 [FRONTEND_DEBUG] Restore checkpoint completed, flags reset`);
            
            const newInputPosition = message.messages && message.messages.length > 0 ? 'bottom' : 'top';
            setInputPosition(newInputPosition);
            setConversationLoading(false);
            break;
          }

          // Normal conversation loading (not checkpoint restore)
          if (message.messages && Array.isArray(message.messages)) {
            console.log(`🔍 [FRONTEND_DEBUG] Normal conversation loading: converting ${message.messages.length} backend messages`);
            const convertedMessages = convertBackendMessages(message.messages);

            // Preserve terminal messages from previous state
            setMessages(prev => {
              const terminalMessages = prev.filter(msg => msg.type === 'terminal');
              const merged = [...convertedMessages];
              
              // Re-insert terminal messages at their original positions if not already present
              prev.forEach((msg, idx) => {
                if (msg.type === 'terminal') {
                  const alreadyPresent = merged.some(m => 
                    (m.sessionId && msg.sessionId && m.sessionId === msg.sessionId) || m.id === msg.id
                  );
                  if (!alreadyPresent) {
                    if (idx <= merged.length) {
                      merged.splice(idx, 0, msg);
                    } else {
                      merged.push(msg);
                    }
                  }
                }
              });
              return merged;
            });

            const newInputPosition = convertedMessages.length > 0 ? 'bottom' : 'top';
            setInputPosition(newInputPosition);
            setConversationLoading(false);
            console.log(`🔍 [FRONTEND_DEBUG] Normal conversation loading completed with ${convertedMessages.length} messages`);
          } else {
            console.log(`🔍 [FRONTEND_DEBUG] No messages array found, clearing to empty state`);
            // No messages array found, preserve only terminal messages
            setMessages(prev => prev.filter(msg => msg.type === 'terminal'));
            setInputPosition('top');
            setConversationLoading(false);
          }
          break;

        case 'conversationDeleted':
          // Clear messages and reset to new chat state if the current conversation was deleted
          setMessages([]);
          setInputPosition('top');
          setInputValue(""); // Clear input box when conversation is deleted
          setShowHistoryDropdown(false);
          setConversationLoading(false);
          break;

        case 'showFileChangeReview':
          // Handle file change review display from tool results
          // Add a file change review message to the chat
          setMessages(prev => {
            const newMsg: Message = {
              id: Date.now(),
              sender: 'bot',
              content: `📝 **File Change Review:** \`${message.filePath}\`\n\n${message.diffSummary || 'File changes tracked via shadow Git'}`,
              timestamp: new Date(),
              metadata: {
                fileChangeReview: {
                  filePath: message.filePath,
                  originalContent: message.originalContent,
                  newContent: message.newContent,
                  diffSummary: message.diffSummary,
                  language: message.language,
                  staged: message.staged,
                  changeId: message.changeId
                }
              }
            };
            return [...prev, newMsg];
          });
          break;
        
        case 'fileChangeAccepted':
          // Handle file change acceptance confirmation
          setMessages(prev => [...prev, {
            id: Date.now(),
            sender: 'bot',
            content: `✅ **File Change Accepted:** \`${message.filePath}\`\n\n${message.message}`,
            timestamp: new Date(),
            metadata: {
              type: 'fileChangeAccepted',
              filePath: message.filePath
            }
          }]);
          break;
        
        case 'fileChangeRejected':
          // Handle file change rejection confirmation
          setMessages(prev => [...prev, {
            id: Date.now(),
            sender: 'bot',
            content: `❌ **File Change Rejected:** \`${message.filePath}\`\n\n${message.message}`,
            timestamp: new Date(),
            metadata: {
              type: 'fileChangeRejected',
              filePath: message.filePath
            }
          }]);
          break;
        
        case 'settingsChanged': {
          // Settings have been updated, revalidate configuration and request cost settings
          validateConfiguration();
          
          const vscodeApi = window.vscode || window.__vscodeApi;
          if (vscodeApi) {
            vscodeApi.postMessage({
              type: 'getCostSettings'
            });
          }
          break;
        }
        
        // Handle keyboard shortcut triggers from extension
        case 'triggerNewChat':
          handleNewChat();
          break;
        case 'triggerShowChatHistory':
          setShowChatHistory(true);
          break;
        case 'triggerSearchConversations':
          setShowConversationSearch(true);
          break;
        case 'triggerCustomInstructions':
          setShowCustomInstructionsModal(true);
          break;
        case 'triggerAttachFiles':
          const filePickerButton = document.querySelector('.file-picker-button') as HTMLButtonElement;
          if (filePickerButton) {
            filePickerButton.click();
          }
          break;
        case 'triggerCopyLastResponse':
          const lastBotMessage = messages.filter(m => m.sender === 'bot').pop();
          if (lastBotMessage) {
            navigator.clipboard.writeText(lastBotMessage.content);
          }
          break;
        case 'triggerRegenerateResponse':
          const lastBotMsg = messages.filter(m => m.sender === 'bot').pop();
          if (lastBotMsg) {
            handleRegenerateMessage(lastBotMsg.id);
          }
          break;
        case 'triggerShowKeyboardShortcuts':
          setShowKeyboardShortcuts(true);
          break;
        case 'triggerClearAttachedFiles':
          setAttachedFiles([]);
          break;
        case 'showMcpServers':
          setShowMcpServers(true);
          break;
        case 'showAddMcpServer':
          setShowAddMcpServer(true);
          break;
        
        case 'costSettingsLoaded':
          // Update cost display settings from backend
          if (message.settings) {
            setShowAggregateCost(message.settings.showAggregateCost !== undefined ? message.settings.showAggregateCost : true);
            setShowCostPerMessage(message.settings.showCostPerMessage !== undefined ? message.settings.showCostPerMessage : false);
          }
          break;
        
        case 'costSettingsChanged':
          // Settings have been updated, update local state
          if (message.settings) {
            setShowAggregateCost(message.settings.showAggregateCost !== undefined ? message.settings.showAggregateCost : true);
            setShowCostPerMessage(message.settings.showCostPerMessage !== undefined ? message.settings.showCostPerMessage : false);
          }
          break;
        
        // Auto-compact / Summarization events
        case 'summarizationStarted':
          setIsSummarizing(true);
          setSummarizationProgress({
            stage: 'starting',
            message: 'Auto-compact started...',
            progress: 0
          });
          break;
        
        case 'summarizationProgress':
          setSummarizationProgress(message.progress);
          break;
        
        case 'summarizationComplete': {
          setIsSummarizing(false);
          setSummarizationProgress(null);
          
          // Add a system message about the summarization
          setMessages(prev => [...prev, {
            id: Date.now(),
            sender: 'bot',
            content: `🔄 **Auto-Compact Complete** - Conversation summarized to maintain context window. Original ${message.result?.originalMessageCount || 'multiple'} messages condensed. Cost: $${(message.result?.cost || 0).toFixed(4)}`,
            timestamp: new Date(),
            metadata: {
              type: 'summarizationComplete',
              cost: message.result?.cost || 0,
              originalMessageCount: message.result?.originalMessageCount || 0
            }
          }]);
          
          // Refresh conversation to show the summarized version
          const vscodeApi = window.vscode || window.__vscodeApi;
          if (vscodeApi && currentSessionId) {
            vscodeApi.postMessage({
              type: 'loadConversation',
              conversationId: currentSessionId
            });
          }
          break;
        }
        case 'summarizationFailed':
          setIsSummarizing(false);
          setSummarizationProgress(null);
          
          // Add error message
          setMessages(prev => [...prev, {
            id: Date.now(),
            sender: 'bot',
            content: `❌ **Auto-Compact Failed** - ${message.error || 'Unknown error occurred during conversation summarization'}`,
            timestamp: new Date(),
            metadata: {
              type: 'summarizationFailed',
              error: message.error
            }
          }]);
          break;
        case 'checkpointRestored':
          console.log(`🔍 [FRONTEND_DEBUG] Checkpoint restored message received`);
          console.log(`🔍 [FRONTEND_DEBUG] Restored content length: ${message.content?.length || 0}`);
          console.log(`🔍 [FRONTEND_DEBUG] Restored content preview: ${message.content?.substring(0, 100) || 'empty'}...`);
          console.log(`🔍 [FRONTEND_DEBUG] Current input value before update: ${inputValue}`);
          console.log(`🔍 [FRONTEND_DEBUG] Current messages count: ${messages.length}`);
          
          // Set the input box value with the restored checkpoint content
          setInputValue(message.content || "");
          console.log(`🔍 [FRONTEND_DEBUG] Input value updated with restored content`);
          

          
          // Set flags to accept the next messagesChanged event which will contain the truncated conversation
          restoringCheckpointRef.current = true;
          expectedMessageCountRef.current = -1; // Accept next messagesChanged regardless of count
          console.log(`🔍 [FRONTEND_DEBUG] Restore flags set - restoringCheckpoint: true, expectedMessageCount: -1`);
          break;
        case 'checkpointRestoreFailed':
          // Show a user-friendly error (replace with toast/snackbar if you have one)
          alert(message.error || 'Failed to restore checkpoint.');
          break;
        case 'streamingTimeout':
          setIsLoading(false);
          setIsStreaming(false);
          setCurrentStreamingMessage('');
          break;
        case 'chatSessionSaved':
          if (message.success) {
            if (message.sessionId && !currentSessionId) {
              setCurrentSessionId(message.sessionId);
            }
          } else {
            console.error('[ChatView.tsx] Failed to save chat session:', message.error);
          }
          break;
        case 'newSessionStarted':
          if (message.sessionId) {
            setCurrentSessionId(message.sessionId);
          }
          break;
        case 'modelCredentialsTestResult':
          if (message.success) {
            setCurrentProvider(prev => ({
              ...prev,
              status: {
                status: 'connected',
                message: 'Connected and ready',
                lastChecked: new Date()
              }
            }));
          } else {
            setCurrentProvider(prev => ({
              ...prev,
              status: {
                status: 'error',
                message: message.error || 'Credential test failed',
                lastChecked: new Date()
              }
            }));
          }
          break;
        default:
          console.warn('Unknown message type received:', message.type);
          break;
      }
      } catch (error) {
        console.error('Error processing message:', error, event.data);
        setValidationError('Error processing system message');
      }
    };

    window.addEventListener('message', handleMessage);

    // Validate configuration on mount to get current model/provider
    validateConfiguration();
    
    if (vscodeApi) {
      // Request initial cost display settings
      vscodeApi.postMessage({
        type: 'getCostSettings'
      });
    }

    return () => {
      timeoutRefs.current.forEach(timeout => clearTimeout(timeout));
      timeoutRefs.current.clear();
      if (validateTimeoutRef.current) {
        clearTimeout(validateTimeoutRef.current);
      }      
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  useEffect(() => {
    if (currentSessionId) {
      setSessionNotReady(false);
      setValidationError(null);
    }
  }, [currentSessionId]);

  const generateFileContext = (files: AttachedFile[]): string => {
    if (files.length === 0) return '';
    
    // Only include text files in the context string
    // Images will be handled separately as structured content
    const textFiles = files.filter(file => {
      const isImage = file.name.toLowerCase().match(/\.(png|jpg|jpeg|gif|bmp|webp|svg)$/);
      return !isImage;
    });
    
    if (textFiles.length === 0) return '';
    
    let context = '\n\n## Attached Files\n\n';
    
    textFiles.forEach((file, index) => {
      context += `### File ${index + 1}: ${file.name}\n`;
      context += `**Size:** ${(file.size / 1024).toFixed(2)} KB\n`;
      context += `**Type:** ${file.type || 'Unknown'}\n\n`;
      context += `**Content:**\n\`\`\`\n${file.content}\n\`\`\`\n\n`;
    });
    
    return context;
  };

  const generateStructuredContent = (content: string, files: AttachedFile[]): any => {
    const images = files.filter(file => {
      const isImage = file.name.toLowerCase().match(/\.(png|jpg|jpeg|gif|bmp|webp|svg)$/);
      return isImage && file.content.startsWith('data:image');
    });
    
    
    // If no images, return null structured content
    if (images.length === 0) {
      return { structuredContent: null };
    }
    
    // Return structured content with text and images
    const structuredContent = [];
    
    // Add text content first
    if (content.trim()) {
      structuredContent.push({ text: content });
    }
    
    // Add each image
    images.forEach(image => {
      // Extract base64 data and media type from data URL
      const matches = image.content.match(/^data:([^;]+);base64,(.+)$/);
      if (matches) {
        const [, mediaType, base64Data] = matches;
        structuredContent.push({
          image: {
            format: mediaType.split('/')[1], // e.g., 'png', 'jpeg'
            source: {
              bytes: base64Data
            }
          }
        });
      }
    });
    
    
    return { structuredContent };
  };

  const handleSendMessage = async (content: string) => {
    if (!content.trim() && attachedFiles.length === 0) return;

    if (!currentSessionId) {
      setSessionNotReady(true);
      setValidationError('Please wait for the new chat session to be ready before sending a message.');
      return;
    } else {
      setSessionNotReady(false);
    }

    try {
      setValidationError(null);

    // If message starts with '>', send command to backend to open terminal
    if (content.trim().startsWith('>')) {
      const command = content.trim().replace(/^>\s*/, '');

      // Send to backend to open terminal
      const vscodeApi = window.vscode || window.__vscodeApi;
      if (vscodeApi) {
        vscodeApi.postMessage({
          type: 'openTerminal',
          command: command
        });
      }

      // Move input to bottom after first message
      if (inputPosition === 'top') {
        setInputPosition('bottom');
      }
      return;
    }

    // Prepare message content with attached text files
    const fileContext = generateFileContext(attachedFiles);
    const fullContent = content.trim() + fileContext;
    
    // Generate structured content for AI providers that support images
    const structuredContent = generateStructuredContent(fullContent, attachedFiles);

    const userMessage: Message = {
      id: Date.now(),
      content: content.trim(),
      sender: 'user',
      timestamp: new Date(),
      attachedFiles: attachedFiles.length > 0 ? [...attachedFiles] : undefined
    };

    setMessages(prev => [...prev, userMessage]);

    // Clear attached files after sending
    setAttachedFiles([]);
    
    // Clear input box after sending message
    setInputValue("");

    // Move input to bottom after first message
    if (inputPosition === 'top') {
      setInputPosition('bottom');
    }

    // Auto-generate session name from first message if needed
    if (!currentSessionId && !currentSessionName.includes('...')) {
      setCurrentSessionName(generateSessionName([userMessage]));
    }

    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      const messageToSend = {
        type: 'sendMessage',
        content: fullContent,
        structuredContent: structuredContent.structuredContent || null,
        customInstructions: currentCustomInstructions,
        sessionId: currentSessionId // <-- Ensure sessionId is always included
      };
      vscodeApi.postMessage(messageToSend);
    } else {
      console.error('[ChatView.tsx] VS Code API not available. Message not sent.');
      setValidationError('Failed to send message: VS Code API not available');
    }
    } catch (error) {
      console.error('Error in handleSendMessage:', error);
      setValidationError(`Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };


  const handleCloseChat = () => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'closePanel'
      });
    }
  };

  const handleEditMessage = (messageId: number, newContent: string) => {
    // Find the message to edit
    const messageIndex = messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) return;

    // Update the message content
    setMessages(prev => {
      const newMessages = [...prev];
      newMessages[messageIndex] = {
        ...newMessages[messageIndex],
        content: newContent
      };
      return newMessages;
    });

    // Remove all messages after the edited message and regenerate from there
    const messagesAfterEdit = messages.slice(messageIndex + 1);
    if (messagesAfterEdit.length > 0) {
      setMessages(prev => prev.slice(0, messageIndex + 1));
      
      // Resend the edited message to generate a new response
      const vscodeApi = window.vscode || window.__vscodeApi;
      if (vscodeApi) {
        vscodeApi.postMessage({
          type: 'sendMessage',
          content: newContent
        });
      }
    }
  };

  const handleRunCode = (code: string, language: string) => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'runCode',
        code,
        language
      });
    }
  };

  const handleStopExecution = () => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'stopExecution'
      });
    }
    
    // Reset local state
    setIsLoading(false);
    setIsStreaming(false);
    setIsProcessingTools(false);
    setCurrentStreamingMessage('');
  };

  const handleSelectSession = (session: ChatSession) => {
    // Save current session first
    saveCurrentSession();

    // Load selected session and mark all messages as historical
    const historicalMessages = session.messages.map(msg => ({
      ...msg,
      isHistorical: true
    }));
    
    setMessages(historicalMessages);
    setCurrentSessionId(session.id);
    setCurrentSessionName(session.name);
    setCurrentCustomInstructions(session.customInstructions || '');
    setInputPosition(session.messages.length > 0 ? 'bottom' : 'top');
    
    // Clear any current state
    setValidationError(null);
    setCurrentStreamingMessage('');
    setIsLoading(false);
    setIsStreaming(false);
    setIsProcessingTools(false);

    // Notify backend to load this session's conversation history
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'loadChatSession',
        sessionId: session.id,
        messages: historicalMessages
      });
    }
  };

  const handleDeleteSession = (sessionId: string) => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'deleteChatSession',
        sessionId: sessionId
      });
    }

    // If deleting current session, start new chat
    if (sessionId === currentSessionId) {
      handleNewChat();
    }
  };

  const handleShowHistory = () => {
    setShowChatHistory(true);
  };

  const handleSearchResultSelect = (session: ChatSession, messageIndex?: number) => {
    try {
      // Load the selected session
      handleSelectSession(session);
      
      // If a specific message was selected, scroll to it
      if (messageIndex !== undefined && messageIndex >= 0) {
        const scrollTimeout = setTimeout(() => {
          try {
            // Find the message element and scroll to it
            const messageElements = document.querySelectorAll('.message-container');
            if (messageElements[messageIndex]) {
              messageElements[messageIndex].scrollIntoView({
                behavior: 'smooth',
                block: 'center'
              });
              
              // Briefly highlight the message
              messageElements[messageIndex].classList.add('search-highlighted');
              
              const highlightTimeout = setTimeout(() => {
                try {
                  messageElements[messageIndex]?.classList.remove('search-highlighted');
                } catch (error) {
                  console.warn('Failed to remove highlight class:', error);
                } finally {
                  timeoutRefs.current.delete(highlightTimeout);
                }
              }, 2000);
              
              timeoutRefs.current.add(highlightTimeout);
            }
          } catch (error) {
            console.error('Error in scroll timeout:', error);
          } finally {
            timeoutRefs.current.delete(scrollTimeout);
          }
        }, 300); // Wait for session to load
        
        timeoutRefs.current.add(scrollTimeout);
      }
    } catch (error) {
      console.error('Error in handleSearchResultSelect:', error);
    }
  };

  const handleSaveCustomInstructions = (instructions: string) => {
    setCurrentCustomInstructions(instructions);
    // Save to current session - this will be handled when the session is saved
    // The instructions will be included in the next AI conversation
  };

  const handleHistoryClick = () => {
    setShowHistoryDropdown(!showHistoryDropdown);
    setShowDropdown(false); // Close other dropdown
  };

  const handleConversationSelect = (conversationId: string) => {
    console.log(`[ChatView] Selecting conversation: ${conversationId}`);
    setInputValue(""); // Clear input box when switching conversations
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'switchConversation',
        conversationId: conversationId
      });
      console.log(`[ChatView] Sent switchConversation message for: ${conversationId}`);
    } else {
      console.error('[ChatView] VS Code API not available for conversation switch');
    }
    setShowHistoryDropdown(false);
  };

  const handleResumeInterruptedConversation = (conversationId: string) => {
    console.log(`[ChatView] Resuming interrupted conversation: ${conversationId}`);
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'resumeInterruptedConversation',
        conversationId: conversationId
      });
    }
    setShowHistoryDropdown(false);
  };

  const getConversationStatus = (conversation: ConversationSummary): {
    label: string;
    className: string;
    icon: string;
    canResume: boolean;
  } => {
    if (conversation.metadata?.isInterrupted) {
      const reason = conversation.metadata.interruptionReason;
      const canResume = conversation.metadata.canResume || false;
      
      switch (reason) {
        case 'user_stop':
          return {
            label: 'Stopped by user',
            className: 'conversation-status-interrupted-user',
            icon: '⏸️',
            canResume
          };
        case 'error':
          return {
            label: 'Interrupted by error',
            className: 'conversation-status-interrupted-error',
            icon: '❌',
            canResume
          };
        case 'timeout':
          return {
            label: 'Timed out',
            className: 'conversation-status-interrupted-timeout',
            icon: '⏰',
            canResume
          };
        case 'manual_switch':
          return {
            label: 'Switched to another conversation',
            className: 'conversation-status-interrupted-switch',
            icon: '🔄',
            canResume
          };
        default:
          return {
            label: 'Interrupted',
            className: 'conversation-status-interrupted',
            icon: '⚠️',
            canResume
          };
      }
    }

    return {
      label: 'Complete',
      className: 'conversation-status-complete',
      icon: '✅',
      canResume: false
    };
  };

  const handleConversationRename = (conversationId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent conversation selection
    console.log(`[ChatView] Initiating rename for conversation: ${conversationId}`);

    const currentConversation = conversations.find(c => c.id === conversationId);
    const newTitle = window.prompt("Enter the new title for the conversation:", currentConversation?.title || '');

    if (newTitle && newTitle.trim()) {
      console.log(`[ChatView] Sending renameConversation message for ${conversationId} to "${newTitle.trim()}"`);
      const vscodeApi = window.vscode || window.__vscodeApi;
      if (vscodeApi) {
        vscodeApi.postMessage({
          type: 'renameConversation',
          conversationId: conversationId,
          newTitle: newTitle.trim()
        });
      }
    } else {
      console.log('[ChatView] Rename cancelled by user.');
    }
  };

  const handleConversationDelete = (conversationId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent conversation selection
    console.log(`[ChatView] Deleting conversation: ${conversationId}`);

    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'deleteConversation',
        conversationId: conversationId
      });
    }
  };

  const formatTimeAgo = (timestamp: number): string => {
    const now = Date.now();
    const diffMs = now - timestamp;
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMinutes < 60) {
      return diffMinutes <= 1 ? 'Just now' : `${diffMinutes}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else {
      return `${diffDays}d ago`;
    }
  };


  const streamingMessage: Message | null = currentStreamingMessage ? {
    id: Date.now(),
    content: currentStreamingMessage,
    sender: 'bot',
    timestamp: new Date()
  } : null;

  const handleFilesSelected = (files: AttachedFile[]) => {
    setAttachedFiles(prev => [...prev, ...files]);
  };

  const handleRemoveFile = (index: number) => {
    setAttachedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleClearFiles = () => {
    setAttachedFiles([]);
  };

  const handleAcceptFileChange = async (filePath: string, originalContent: string, newContent: string, changeId?: string) => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'acceptFileChange',
        filePath,
        originalContent,
        newContent,
        changeId
      });
    }
    // Persist reviewStatus in message metadata
    setMessages(prev => prev.map(msg => {
      if (msg.metadata?.fileChangeReview && msg.metadata.fileChangeReview.filePath === filePath && msg.metadata.fileChangeReview.changeId === changeId) {
        return {
          ...msg,
          metadata: {
            ...msg.metadata,
            fileChangeReview: {
              ...msg.metadata.fileChangeReview,
              reviewStatus: 'accepted'
            }
          }
        };
      }
      return msg;
    }));
  };

  const handleRejectFileChange = async (filePath: string, originalContent: string, changeId?: string, targetContent?: string) => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'rejectFileChange',
        filePath,
        originalContent,
        changeId,
        targetContent
      });
    }
    // Persist reviewStatus in message metadata
    setMessages(prev => prev.map(msg => {
      if (msg.metadata?.fileChangeReview && msg.metadata.fileChangeReview.filePath === filePath && msg.metadata.fileChangeReview.changeId === changeId) {
        return {
          ...msg,
          metadata: {
            ...msg.metadata,
            fileChangeReview: {
              ...msg.metadata.fileChangeReview,
              reviewStatus: 'rejected'
            }
          }
        };
      }
      return msg;
    }));
  };

  // Compute actionable (pending) file changes (for Accept All, still only latest per file)
  const actionableFileChanges: {
    filePath: string;
    originalContent: string;
    newContent: string;
    changeId?: string;
    reviewStatus?: 'accepted' | 'rejected' | 'auto_approve' | 'pending';
  }[] = [];
  {
    // Find latest review index for each file
    const latestReviewIndexByFile: Record<string, number> = {};
    messages.forEach((msg, idx) => {
      const filePath = msg.metadata?.fileChangeReview?.filePath;
      if (filePath) {
        latestReviewIndexByFile[filePath] = idx;
      }
    });
    // Collect actionable file changes (for Accept All: only latest, not accepted/rejected)
    messages.forEach((msg, idx) => {
      const review = msg.metadata?.fileChangeReview;
      if (
        review &&
        latestReviewIndexByFile[review.filePath] === idx &&
        review.reviewStatus !== 'accepted' &&
        review.reviewStatus !== 'rejected'
      ) {
        actionableFileChanges.push({
          filePath: review.filePath,
          originalContent: review.originalContent,
          newContent: review.newContent,
          changeId: review.changeId,
          reviewStatus: review.reviewStatus,
        });
      }
    });
  }

  const handleAcceptAllFileChanges = async () => {
    for (const change of actionableFileChanges) {
      if (change.reviewStatus !== 'accepted') {
        await handleAcceptFileChange(
          change.filePath,
          change.originalContent,
          change.newContent,
          change.changeId
        );
      }
    }
  };

  const handleRejectAllFileChanges = async () => {
    // Group all file change reviews by filePath (all, not just pending/auto_approve)
    const fileReviews: Record<string, {msgIdx: number, review: any}[]> = {};
    messages.forEach((msg, idx) => {
      const review = msg.metadata?.fileChangeReview;
      if (review && review.filePath) {
        if (!fileReviews[review.filePath]) fileReviews[review.filePath] = [];
        fileReviews[review.filePath].push({msgIdx: idx, review});
      }
    });

    for (const filePath in fileReviews) {
      const reviews = fileReviews[filePath];
      // Find the last accepted review (if any)
      const lastAccepted = [...reviews].reverse().find(r => r.review.reviewStatus === 'accepted');
      let targetContent: string;
      let targetType: string;
      if (lastAccepted) {
        targetContent = lastAccepted.review.newContent;
        targetType = 'last accepted';
      } else {
        targetContent = reviews[0].review.originalContent;
        targetType = 'base/original';
      }
      console.log(`[RejectAll] Reverting file '${filePath}' to ${targetType} state.`);
      // Reject all reviews in pending or auto_approve state
      for (const {review} of reviews) {
        if (review.reviewStatus !== 'accepted' && review.reviewStatus !== 'rejected') {
          console.log(`[RejectAll] Rejecting changeId=${review.changeId} for file '${filePath}' to content from ${targetType} state.`);
          await handleRejectFileChange(filePath, targetContent, review.changeId, targetContent);
        }
      }
    }
    // Update local state for all affected file change review messages
    setMessages(prev => prev.map(msg => {
      if (
        msg.metadata &&
        msg.metadata.fileChangeReview &&
        fileReviews[msg.metadata.fileChangeReview.filePath] &&
        msg.metadata.fileChangeReview.reviewStatus !== 'accepted' &&
        msg.metadata.fileChangeReview.reviewStatus !== 'rejected'
      ) {
        // Find the last accepted review (if any)
        const reviews = fileReviews[msg.metadata.fileChangeReview.filePath];
        const lastAccepted = [...reviews].reverse().find(r => r.review.reviewStatus === 'accepted');
        let targetContent: string;
        if (lastAccepted) {
          targetContent = lastAccepted.review.newContent;
        } else {
          targetContent = reviews[0].review.originalContent;
        }
        return {
          ...msg,
          metadata: {
            ...msg.metadata,
            fileChangeReview: {
              ...msg.metadata.fileChangeReview,
              reviewStatus: 'rejected',
              newContent: targetContent,
            }
          }
        } as Message;
      }
      return msg;
    }));
  };

  // Add restore checkpoint handler
  const handleRestoreCheckpoint = (messageId: string) => {
    console.log(`🔍 [FRONTEND_DEBUG] Restore checkpoint clicked for message ID: ${messageId}`);
    console.log(`🔍 [FRONTEND_DEBUG] Current messages count: ${messages.length}`);
    console.log(`🔍 [FRONTEND_DEBUG] Current message IDs: ${messages.map(m => m.id).join(', ')}`);
    
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      console.log(`🔍 [FRONTEND_DEBUG] VSCode API available, sending restoreCheckpoint message`);
      vscodeApi.postMessage({
        type: 'restoreCheckpoint',
        messageId
      });
      console.log(`🔍 [FRONTEND_DEBUG] restoreCheckpoint message sent to extension`);
    } else {
      console.error(`🔍 [FRONTEND_DEBUG] VSCode API not available`);
    }
  };

  const handleContinueAfterTimeout = () => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'continueAfterTimeout'
      });
    }
  };

  return (
    <div className="chat-container">
      <div className="chat-header-new">
        <div className="chat-header-left">
          <h2>{currentSessionName}</h2>
          {showAggregateCost && messages.length > 0 && (
            <div className="aggregate-cost-display">
              <span className="cost-value">${calculateAggregateCost().toFixed(4)}</span>
            </div>
          )}
        </div>
        <div className="chat-header-right">
          <button className="header-icon-button" onClick={handleNewChat} title="New chat">
            <Plus size={16} />
          </button>
          <button className="header-icon-button" onClick={handleShowHistory} title="Chat History">
            <History size={16} />
          </button>
          <button 
            className="header-icon-button" 
            onClick={() => setShowConversationSearch(true)} 
            title="Search Conversations (Ctrl+F)"
          >
            <Search size={16} />
          </button>
          <button 
            className="header-icon-button" 
            onClick={() => setShowCustomInstructionsModal(true)} 
            title="Custom Instructions for this session"
          >
            <FileText size={16} />
          </button>
          <button 
            className="header-icon-button" 
            onClick={() => setShowKeyboardShortcuts(true)} 
            title="Keyboard Shortcuts"
          >
            <Keyboard size={16} />
          </button>
          <button 
            className="header-icon-button" 
            onClick={() => setShowMcpServers(true)} 
            title="MCP Servers"
          >
            <Server size={16} />
          </button>
          <button 
            className="header-icon-button" 
            onClick={handleSettingsClick} 
            title="Settings"
          >
            <Settings size={16} />
          </button>
          <button className="header-icon-button" onClick={handleCloseChat} title="Close">
            <X size={16} />
          </button>
        </div>
      </div>

      {/* Input at top when no messages */}
      {inputPosition === 'top' && (
        <div className="input-top-container">
          {actionableFileChanges.length > 0 && (
            <div style={{ display: 'flex', gap: 8, marginBottom: 8 }}>
              <button
                className="accept-all-btn"
                onClick={handleAcceptAllFileChanges}
                disabled={isLoading || actionableFileChanges.length === 0}
              >
                Accept All
              </button>
              <button
                className="reject-all-btn"
                onClick={handleRejectAllFileChanges}
                disabled={isLoading || actionableFileChanges.length === 0}
              >
                Reject All
              </button>
            </div>
          )}
          {attachedFiles.length > 0 && (
            <AttachedFiles
              files={attachedFiles}
              onRemoveFile={handleRemoveFile}
              onClearAll={handleClearFiles}
            />
          )}
          {sessionNotReady && (
            <div className="session-warning" style={{ color: 'red', margin: '8px 0' }}>
              Please wait for the new chat session to be ready before sending a message.
            </div>
          )}
          <ChatInput
            onSendMessage={handleSendMessage}
            disabled={isLoading || !!validationError}
            currentProvider={currentProvider}
            isStreaming={isStreaming || isLoading || !!currentStreamingMessage}
            onStopExecution={handleStopExecution}
            onFilesSelected={handleFilesSelected}
            value={inputValue}
            setValue={setInputValue}
          />
        </div>
      )}

      {/* Provider Status Banner for Errors */}
      {(currentProvider.status.status === 'error' || currentProvider.status.status === 'not-configured') && (
        <div className="provider-status-banner">
          <ProviderStatus
            provider={currentProvider.provider as AIProviders}
            model={currentProvider.model as Models}
            status={currentProvider.status}
            onOpenSettings={handleSettingsClick}
            onRetryConnection={handleRetryConnection}
          />
        </div>
      )}

      {validationError && (
        <div className="validation-error-banner">
          <div className="validation-error-content">
            <AlertTriangle size={16} />
            <div className="validation-error-text">
              <strong>Configuration Error:</strong> {validationError}
            </div>
          </div>
        </div>
      )}

      <div className="messages-container" ref={messagesContainerRef}>
        {messages.length === 0 && inputPosition === 'top' ? (
          <div className="welcome-container">
            <div className="welcome-content">
              <div className="welcome-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                  <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                  <path d="M2 17l10 5 10-5"/>
                  <path d="M2 12l10 5 10-5"/>
                </svg>
              </div>
              <h3 className="welcome-title">Welcome to AI Assistant</h3>
              <p className="welcome-subtitle">
                Start a conversation by typing a message above. I can help you with code, answer questions, or assist with various tasks.
              </p>
            </div>
          </div>
        ) :
          (() => {
            // Compute latest file change review index for each file
            const latestReviewIndexByFile: Record<string, number> = {};
            messages.forEach((msg, idx) => {
              const filePath = msg.metadata?.fileChangeReview?.filePath;
              if (filePath) {
                latestReviewIndexByFile[filePath] = idx;
              }
            });
            return messages.map((message, idx) => {
              if (message.type === 'terminal') {
                // Use the isHistorical flag to determine if terminal should be readonly
                // Also check if we're explicitly showing chat history modal
                const isHistorical = showChatHistory || message.isHistorical || (message.metadata?.terminalHistory !== undefined);
                return <TerminalInstance 
                  key={message.sessionId} 
                  sessionId={message.sessionId!} 
                  initialCommand={message.content} 
                  readonly={isHistorical}
                  terminalHistory={message.metadata?.terminalHistory}
                  onClose={() => {
                    setMessages(prev => prev.filter(m => m.sessionId !== message.sessionId));
                  }} 
                />;
              }
              const messageIsStreaming = isStreaming && idx === messages.length - 1;
              // Determine if this is the latest review for this file
              const isLatestReviewForFile = message.metadata?.fileChangeReview?.filePath
                ? latestReviewIndexByFile[message.metadata.fileChangeReview.filePath] === idx
                : false;
              return (
                <ChatMessage
                  key={message.id}
                  message={message}
                  isStreaming={messageIsStreaming}
                  onRegenerateMessage={handleRegenerateMessage}
                  onEditMessage={handleEditMessage}
                  onRunCode={handleRunCode}
                  diffData={diffDataMap.get(message.id)}
                  showCostPerMessage={showCostPerMessage}
                  onAcceptFileChange={(filePath, originalContent, newContent, changeId) => 
                    handleAcceptFileChange(filePath, originalContent, newContent, changeId)
                  }
                  onRejectFileChange={(filePath, originalContent, changeId) => 
                    handleRejectFileChange(filePath, originalContent, changeId)
                  }
                  isLatestReviewForFile={isLatestReviewForFile}
                  isReadOnly={showChatHistory}
                  onRestoreCheckpoint={handleRestoreCheckpoint}
                  onContinueAfterTimeout={handleContinueAfterTimeout}
                />
              );
            });
          })()
        }

        {streamingMessage && (
          <div className="streaming-message-container">
            <ChatMessage 
              message={streamingMessage} 
              onRegenerateMessage={handleRegenerateMessage}
              onEditMessage={handleEditMessage}
              onRunCode={handleRunCode}
              showCostPerMessage={showCostPerMessage}
              onAcceptFileChange={(filePath, originalContent, newContent, changeId) => 
                handleAcceptFileChange(filePath, originalContent, newContent, changeId)
              }
              onRejectFileChange={(filePath, originalContent, changeId) => 
                handleRejectFileChange(filePath, originalContent, changeId)
              }
              onContinueAfterTimeout={handleContinueAfterTimeout}
            />
          </div>
        )}

        {/* Show generating message when streaming starts but no content yet */}
        {isStreaming && !currentStreamingMessage && !conversationLoading && (
          <GeneratingMessage />
        )}

        {conversationLoading && (
          <LoadingMessage />
        )}

        {isLoading && !currentStreamingMessage && !conversationLoading && (
          <div className="message bot">
            <div className="message-content">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}

        {isProcessingTools && (
          <div className="processing-tools-container">
            <div className="processing-tools-content">
              <div className="processing-info">
                <div className="processing-spinner">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M21 12a9 9 0 11-6.219-8.56"/>
                  </svg>
                </div>
                <span className="processing-text">{processingMessage}</span>
              </div>
              <button 
                onClick={handleStopExecution}
                className="stop-button"
                title="Stop execution"
              >
                <Square size={14} />
                Stop
              </button>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Scroll to bottom button */}
      {showScrollToBottom && (
        <button 
          className="scroll-to-bottom-btn"
          onClick={scrollToBottom}
          title="Scroll to bottom"
        >
          <ChevronDown size={20} />
        </button>
      )}

      {/* Summarization progress indicator - shows below messages, above input */}
      {(isSummarizing || summarizationProgress) && (
        <div className="summarization-progress-display">
          <div className="progress-indicator">
            <span className="progress-icon">🔄</span>
            <span className="progress-text">
              {summarizationProgress?.message || 'Auto-compacting...'}
            </span>
            {summarizationProgress?.progress !== undefined && (
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${summarizationProgress.progress}%` }}
                ></div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Input at bottom after first message */}
      {inputPosition === 'bottom' && (
        <div className="input-bottom-container">
          {actionableFileChanges.length > 0 && (
            <div style={{ display: 'flex', gap: 8, marginBottom: 8 }}>
              <button
                className="accept-all-btn"
                onClick={handleAcceptAllFileChanges}
                disabled={isLoading || actionableFileChanges.length === 0}
              >
                Accept All
              </button>
              <button
                className="reject-all-btn"
                onClick={handleRejectAllFileChanges}
                disabled={isLoading || actionableFileChanges.length === 0}
              >
                Reject All
              </button>
            </div>
          )}
          {attachedFiles.length > 0 && (
            <AttachedFiles
              files={attachedFiles}
              onRemoveFile={handleRemoveFile}
              onClearAll={handleClearFiles}
            />
          )}
          <ChatInput
            onSendMessage={handleSendMessage}
            disabled={isLoading || !!validationError}
            currentProvider={currentProvider}
            isStreaming={isStreaming || isLoading || !!currentStreamingMessage}
            onStopExecution={handleStopExecution}
            onFilesSelected={handleFilesSelected}
            value={inputValue}
            setValue={setInputValue}
          />
        </div>
      )}

      {/* Chat History Modal */}
      <ChatHistory
        isOpen={showChatHistory}
        onClose={() => setShowChatHistory(false)}
        onSelectSession={handleSelectSession}
        onNewSession={handleNewChat}
        onDeleteSession={handleDeleteSession}
        currentSessionId={currentSessionId}
      />

      {/* Conversation Search Modal */}
      <ConversationSearch
        isOpen={showConversationSearch}
        onClose={() => setShowConversationSearch(false)}
        onSelectResult={handleSearchResultSelect}
      />

      {/* Custom Instructions Modal */}
      <CustomInstructions
        isOpen={showCustomInstructionsModal}
        onClose={() => setShowCustomInstructionsModal(false)}
        currentInstructions={currentCustomInstructions}
        onSave={handleSaveCustomInstructions}
      />

      {/* Keyboard Shortcuts Modal */}
      <KeyboardShortcuts
        isOpen={showKeyboardShortcuts}
        onClose={() => setShowKeyboardShortcuts(false)}
      />

      {/* MCP Servers Modal */}
      <McpServersModal
        isOpen={showMcpServers}
        onClose={() => setShowMcpServers(false)}
        onShowAddServer={() => {
          setEditingMcpServer(null);
          setShowAddMcpServer(true);
        }}
        onEditServer={handleEditMcpServer}
      />

      {/* Add/Edit MCP Server Modal */}
      <AddMcpServerModal
        isOpen={showAddMcpServer}
        onClose={() => {
          setShowAddMcpServer(false);
          setEditingMcpServer(null);
        }}
        onAddServer={handleAddMcpServer}
        editServer={editingMcpServer}
        onUpdateServer={handleUpdateMcpServer}
      />

      {/* Shortcut Discovery Tooltip */}
      <ShortcutTooltip enabled={false} />
      
    </div>
  );
};

export default ChatView;

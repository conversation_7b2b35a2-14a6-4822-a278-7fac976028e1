import React, { useRef, useState, useCallback, useEffect } from "react";
import TextareaAutosize from 'react-textarea-autosize';
import { ArrowUp, Square, ChevronDown, Settings } from "lucide-react";
import { ChatMode, CurrentProviderInfo, AttachedFile } from "../types";
import FilePicker from "./FilePicker";
import { useModelData } from "../hooks/useModelData";
import { groupModelsByProvider } from "../utils/modelUtils";

interface ChatInputProps {
  onSendMessage: (content: string) => void;
  disabled?: boolean;
  currentProvider?: CurrentProviderInfo;
  isStreaming?: boolean;
  onStopExecution?: () => void;
  onFilesSelected?: (files: AttachedFile[]) => void;
  value?: string;
  setValue?: (v: string) => void;
}

const MAX_TOKENS = 102400;
function estimateTokens(text: string): number {
  return Math.ceil(text.length / 4);
}

export const ChatInput: React.FC<ChatInputProps> = ({ 
  onSendMessage, 
  disabled = false, 
  currentProvider,
  isStreaming = false,
  onStopExecution,
  onFilesSelected,
  value = "",
  setValue
}) => {
  const [queryText, setQueryText] = useState(value);
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Use the model data hook
  const { 
    availableModels, 
    isLoading: isLoadingModels, 
    getDisplayName, 
    changeModel 
  } = useModelData();

  // Simplified state for dropdowns
  const [chatMode, setChatMode] = useState<ChatMode>(ChatMode.Ask);
  const [showModelDropdown, setShowModelDropdown] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const modelDropdownRef = useRef<HTMLDivElement>(null);
  const providerButtonRef = useRef<HTMLDivElement>(null);
  
  // Simplified input history state
  const [inputHistory, setInputHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  useEffect(() => {
    if (!isStreaming) {
      setIsSending(false);
    }
  }, [isStreaming]);

  // Keep local state in sync with value prop
  useEffect(() => {
    setQueryText(value);
  }, [value]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      const isClickInButton = modelDropdownRef.current && modelDropdownRef.current.contains(target);
      const isClickInDropdown = document.querySelector('.model-dropdown-menu')?.contains(target);
      
      if (!isClickInButton && !isClickInDropdown) {
        setShowModelDropdown(false);
      }
    };

    if (showModelDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showModelDropdown]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const text = e.target.value;
    const tokenCount = estimateTokens(text);
    
    if (tokenCount > MAX_TOKENS) {
      setError(`Input too large (max ${MAX_TOKENS} tokens, currently ${tokenCount})`);
      return;
    } else {
      setError("");
    }
    
    setQueryText(text);
    if (setValue) setValue(text);

    // Reset history navigation if user is manually editing
    if (historyIndex >= 0) {
      setHistoryIndex(-1);
    }
  }, [setValue, historyIndex]);

  const addToHistory = useCallback((message: string) => {
    if (!message.trim()) return;
    
    setInputHistory(prev => {
      // Don't add duplicate of the last message
      if (prev.length > 0 && prev[prev.length - 1] === message.trim()) {
        return prev;
      }
      
      // Add new message and keep last 50 entries
      const newHistory = [...prev, message.trim()];
      return newHistory.slice(-50);
    });
  }, []);

  const navigateHistory = useCallback((direction: 'up' | 'down') => {
    if (inputHistory.length === 0) return;

    if (direction === 'up') {
      if (historyIndex === -1) {
        // First time navigating up - go to most recent
        setHistoryIndex(inputHistory.length - 1);
        setQueryText(inputHistory[inputHistory.length - 1]);
        if (setValue) setValue(inputHistory[inputHistory.length - 1]);
      } else if (historyIndex > 0) {
        // Go to previous (older) message
        const newIndex = historyIndex - 1;
        setHistoryIndex(newIndex);
        setQueryText(inputHistory[newIndex]);
        if (setValue) setValue(inputHistory[newIndex]);
      }
    } else { // down
      if (historyIndex === -1) {
        return; // Already at current input
      } else if (historyIndex < inputHistory.length - 1) {
        // Go to next (newer) message
        const newIndex = historyIndex + 1;
        setHistoryIndex(newIndex);
        setQueryText(inputHistory[newIndex]);
        if (setValue) setValue(inputHistory[newIndex]);
      } else {
        // Go back to current input
        setHistoryIndex(-1);
        setQueryText('');
        if (setValue) setValue('');
      }
    }
  }, [inputHistory, historyIndex, setValue]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    const isComposing = e.nativeEvent?.isComposing ?? false;
    
    // Send message on Enter (without Shift)
    if (e.key === "Enter" && !e.shiftKey && !isComposing) {
      e.preventDefault();
      handleSend();
      return;
    }
    
    // Simple history navigation - only when input is empty or at very start/end
    if (!isComposing) {
      if (e.key === "ArrowUp" && !e.shiftKey && !e.ctrlKey && !e.altKey && !e.metaKey) {
        const textarea = e.currentTarget;
        const isEmpty = queryText.trim() === '';
        const isAtStart = textarea.selectionStart === 0 && textarea.selectionEnd === 0;
        
        if (isEmpty || isAtStart) {
          e.preventDefault();
          navigateHistory('up');
        }
      }
      
      if (e.key === "ArrowDown" && !e.shiftKey && !e.ctrlKey && !e.altKey && !e.metaKey) {
        const textarea = e.currentTarget;
        const isEmpty = queryText.trim() === '';
        const isAtEnd = textarea.selectionStart === queryText.length && textarea.selectionEnd === queryText.length;
        
        if (isEmpty || isAtEnd) {
          e.preventDefault();
          navigateHistory('down');
        }
      }
    }
  }, [queryText, navigateHistory]);

  const handleSend = useCallback(() => {
    if (disabled || !queryText.trim()) return;

    const message = queryText.trim();
    
    // Add to history before sending
    addToHistory(message);
    
    // Reset history navigation
    setHistoryIndex(-1);
    
    onSendMessage(message);
    setQueryText('');
    if (setValue) setValue('');
    setIsSending(true);
  }, [onSendMessage, queryText, disabled, addToHistory, setValue]);

  const handleStop = useCallback(() => {
    if (onStopExecution) {
      onStopExecution();
    }
  }, [onStopExecution]);

  const handleModelChange = (modelId: string) => {
    changeModel(modelId);
  };

  const calculateDropdownPosition = () => {
    if (providerButtonRef.current) {
      const rect = providerButtonRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;
      const dropdownHeight = 300;
      const dropdownWidth = Math.max(rect.width, 200);
      
      let top = rect.bottom + 2;
      let left = rect.left;
      
      if (top + dropdownHeight > viewportHeight) {
        top = rect.top - dropdownHeight - 2;
        if (top < 0) {
          top = viewportHeight - dropdownHeight - 10;
        }
      }
      
      if (left + dropdownWidth > viewportWidth) {
        left = viewportWidth - dropdownWidth - 10;
      }
      
      if (left < 10) {
        left = 10;
      }
      
      setDropdownPosition({
        top,
        left,
        width: Math.min(dropdownWidth, viewportWidth - 20)
      });
    }
  };

  const toggleDropdown = () => {
    if (!showModelDropdown) {
      calculateDropdownPosition();
    }
    setShowModelDropdown(!showModelDropdown);
  };

  const handleOpenSettings = () => {
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'openSettings'
      });
    }
    setShowModelDropdown(false);
  };

  const canSend = queryText.trim().length > 0 && !disabled && !isStreaming && !isSending;
  const showStopButton = isStreaming && onStopExecution;

  return (
    <div className="sleek-input-wrapper">
      {/* Error message for token limit */}
      {error && (
        <div style={{ color: 'red', marginBottom: 4 }}>{error}</div>
      )}
      
      {/* Main Input Container */}
      <div className="sleek-input-container">
        <div className="sleek-input-field-wrapper">
          <TextareaAutosize
            ref={textareaRef}
            className="sleek-input-field"
            placeholder="Ask anything..."
            value={queryText}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            disabled={disabled}
            minRows={1}
            maxRows={6}
            style={{
              resize: 'none',
              border: 'none',
              outline: 'none',
              background: 'transparent',
              width: '100%',
              fontSize: 'inherit',
              fontFamily: 'inherit',
              padding: 0,
              margin: 0,
              overflow: 'hidden'
            }}
          />
        </div>
      </div>

      {/* Bottom Controls */}
      <div className="sleek-bottom-controls">
        {/* Left Side - Mode, Model Dropdowns and Provider Status */}
        <div className="sleek-controls-left">
          {/* File Picker */}
          {onFilesSelected && (
            <FilePicker
              onFilesSelected={onFilesSelected}
              disabled={disabled}
              compact={true}
            />
          )}
          
          {/* Chat Mode Dropdown */}
          <div className="sleek-mode-container">
            <select
              className="sleek-mode-dropdown"
              value={chatMode}
              onChange={(e) => setChatMode(e.target.value as ChatMode)}
            >
              <option value={ChatMode.Ask}>Ask</option>
              <option value={ChatMode.Agent}>Agent</option>
            </select>
          </div>

          {/* Provider Status with Model Dropdown */}
          {currentProvider && (
            <div className="provider-model-selector" ref={modelDropdownRef}>
              <div 
                ref={providerButtonRef}
                className="provider-status-compact" 
                onClick={toggleDropdown}
              >
                <div className={`status-dot ${currentProvider.status.status}`}>
                  <div className="status-icon" />
                </div>
                <span className="provider-model-name">
                  {getDisplayName(currentProvider.model)}
                </span>
                <ChevronDown size={12} className={`dropdown-arrow ${showModelDropdown ? 'open' : ''}`} />
              </div>
            </div>
          )}

          {/* Fixed Position Dropdown Menu */}
          {showModelDropdown && currentProvider && (
            <div 
              className="model-dropdown-menu"
              style={{
                top: `${dropdownPosition.top}px`,
                left: `${dropdownPosition.left}px`,
                width: `${dropdownPosition.width}px`
              }}
            >
              {isLoadingModels ? (
                <div className="model-dropdown-item loading">Loading models...</div>
              ) : (() => {
                const filteredModels = availableModels;
                return filteredModels.length === 0 ? (
                  <>
                    <div className="model-dropdown-item empty">
                      No models configured. Configure API keys to see available models.
                    </div>
                    
                    {/* Settings Option - always show */}
                    <div className="model-group">
                      <div
                        className="model-dropdown-item settings-item"
                        onClick={handleOpenSettings}
                      >
                        <Settings size={12} />
                        <span>Configure API Keys</span>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    {groupModelsByProvider(filteredModels).map(group => (
                      <div key={group.providerId} className="model-group">
                        <div className="model-group-label">
                          {group.providerName} Models
                        </div>
                        {group.models.map(model => (
                          <div
                            key={model.id}
                            className={`model-dropdown-item ${currentProvider?.model === model.id ? 'active' : ''}`}
                            onClick={() => {
                              handleModelChange(model.id);
                              setShowModelDropdown(false);
                            }}
                          >
                            {model.displayName}
                          </div>
                        ))}
                      </div>
                    ))}
                    
                    {/* Settings Option */}
                    <div className="model-group">
                      <div
                        className="model-dropdown-item settings-item"
                        onClick={handleOpenSettings}
                      >
                        <Settings size={12} />
                        <span>Open Settings</span>
                      </div>
                    </div>
                  </>
                );
              })()}
            </div>
          )}

        </div>

        {/* Right Side - Send/Stop Button */}
        <div className="sleek-controls-right">
          {showStopButton ? (
            <button
              onClick={handleStop}
              className="sleek-stop-button"
              title="Stop generation"
            >
              <Square size={12} />
            </button>
          ) : (
            <button
              onClick={handleSend}
              disabled={!canSend}
              className="sleek-send-button"
            >
              <ArrowUp size={12} />
            </button>
          )}
        </div>
      </div>

      {/* History navigation indicator */}
      {historyIndex >= 0 && inputHistory.length > 0 && (
        <div className="history-navigation-indicator">
          <span className="history-position">
            {inputHistory.length - historyIndex} of {inputHistory.length}
          </span>
          <span className="history-hint">
            Use ↑↓ to navigate history • Press Enter to send • Shift+Enter for new line
          </span>
        </div>
      )}
    </div>
  );
};

export default ChatInput;
import React, { useState, useEffect, useRef } from 'react';
import { ChatSession } from '../types';
import { History, Plus, Search, Trash2, MessageSquare, Clock, X } from 'lucide-react';

interface ChatHistoryProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectSession: (session: ChatSession) => void;
  onNewSession: () => void;
  onDeleteSession: (sessionId: string) => void;
  currentSessionId: string | null;
}

export const ChatHistory: React.FC<ChatHistoryProps> = ({
  isOpen,
  onClose,
  onSelectSession,
  onNewSession,
  onDeleteSession,
  currentSessionId
}) => {
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen) {
      // Request sessions from backend when opened
      const vscodeApi = window.vscode || window.__vscodeApi;
      if (vscodeApi) {
        vscodeApi.postMessage({
          type: 'getChatSessions'
        });
      }

      // Focus search input
      setTimeout(() => searchInputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      
      if (message.type === 'chatSessionsLoaded') {
        setSessions(message.sessions || []);
        setIsLoading(false);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  const filteredSessions = sessions.filter(session =>
    session.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    session.messages.some(msg => 
      msg.content.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  const formatDate = (date: Date) => {
    const now = new Date();
    const sessionDate = new Date(date);
    const diffInHours = (now.getTime() - sessionDate.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return sessionDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return sessionDate.toLocaleDateString([], { weekday: 'short' });
    } else {
      return sessionDate.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const getSessionPreview = (session: ChatSession): string => {
    const userMessages = session.messages.filter(msg => msg.sender === 'user');
    if (userMessages.length === 0) return 'New conversation';
    
    const lastUserMessage = userMessages[userMessages.length - 1];
    return lastUserMessage.content.length > 50 
      ? lastUserMessage.content.substring(0, 50) + '...'
      : lastUserMessage.content;
  };

  const handleSessionClick = (session: ChatSession) => {
    onSelectSession(session);
    onClose();
  };

  const handleDeleteClick = (e: React.MouseEvent, sessionId: string) => {
    e.stopPropagation();
    onDeleteSession(sessionId);
  };

  const handleNewSessionClick = () => {
    onNewSession();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="chat-history-overlay">
      <div className="chat-history-background" onClick={onClose}></div>
      <div className="chat-history-panel">
        {/* Header */}
        <div className="chat-history-header">
          <div className="chat-history-title">
            <History size={18} />
            <h3>Chat History</h3>
          </div>
          <button className="chat-history-close" onClick={onClose}>
            <X size={16} />
          </button>
        </div>

        {/* Search */}
        <div className="chat-history-search">
          <Search size={16} className="search-icon" />
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
        </div>

        {/* New Session Button */}
        <button className="new-session-button" onClick={handleNewSessionClick}>
          <Plus size={16} />
          New Conversation
        </button>

        {/* Sessions List */}
        <div className="chat-history-content">
          {isLoading ? (
            <div className="chat-history-loading">
              <div className="loading-spinner" />
              <span>Loading conversations...</span>
            </div>
          ) : filteredSessions.length === 0 ? (
            <div className="chat-history-empty">
              {searchQuery ? (
                <>
                  <Search size={32} />
                  <p>No conversations found</p>
                  <span>Try a different search term</span>
                </>
              ) : (
                <>
                  <MessageSquare size={32} />
                  <p>No conversations yet</p>
                  <span>Start a new conversation to see it here</span>
                </>
              )}
            </div>
          ) : (
            <div className="sessions-list">
              {filteredSessions.map((session) => (
                <div
                  key={session.id}
                  className={`session-item ${session.id === currentSessionId ? 'active' : ''}`}
                  onClick={() => handleSessionClick(session)}
                >
                  <div className="session-content">
                    <div className="session-header">
                      <h4 className="session-name">{session.name}</h4>
                      <div className="session-meta">
                        <Clock size={12} />
                        <span>{formatDate(session.updatedAt)}</span>
                      </div>
                    </div>
                    <p className="session-preview">{getSessionPreview(session)}</p>
                    <div className="session-stats">
                      <span className="message-count">
                        {session.messages.length} message{session.messages.length !== 1 ? 's' : ''}
                      </span>
                      {session.model && (
                        <span className="session-model">{session.model}</span>
                      )}
                    </div>
                  </div>
                  <button
                    className="session-delete"
                    onClick={(e) => handleDeleteClick(e, session.id)}
                    title="Delete conversation"
                  >
                    <Trash2 size={14} />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatHistory;
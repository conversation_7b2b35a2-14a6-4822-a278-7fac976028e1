import React, { useRef } from 'react';
import { Paperclip, Image, FileText } from 'lucide-react';
import { AttachedFile } from '../types';

interface FilePickerProps {
  onFilesSelected: (files: AttachedFile[]) => void;
  disabled?: boolean;
  compact?: boolean;
}

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

const SUPPORTED_TEXT_EXTENSIONS = [
  '.txt', '.md', '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.h',
  '.css', '.scss', '.html', '.xml', '.json', '.yaml', '.yml', '.sql', '.sh', '.bat',
  '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.dart', '.vue', '.svelte', '.purs', '.hs'
];

const SUPPORTED_IMAGE_EXTENSIONS = [
  '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.svg'
];

export const FilePicker: React.FC<FilePickerProps> = ({
  onFilesSelected,
  disabled = false,
  compact = false
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const isTextFile = (fileName: string): boolean => {
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return SUPPORTED_TEXT_EXTENSIONS.includes(extension);
  };

  const isImageFile = (fileName: string): boolean => {
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return SUPPORTED_IMAGE_EXTENSIONS.includes(extension);
  };

  const isSupportedFile = (fileName: string): boolean => {
    return isTextFile(fileName) || isImageFile(fileName);
  };

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        const result = e.target?.result;
        if (typeof result === 'string') {
          resolve(result);
        } else if (result instanceof ArrayBuffer) {
          // For binary files, convert to base64
          const uint8Array = new Uint8Array(result);
          const binaryString = Array.from(uint8Array, byte => String.fromCharCode(byte)).join('');
          const base64 = btoa(binaryString);
          resolve(`data:${file.type};base64,${base64}`);
        } else {
          reject(new Error('Unable to read file content'));
        }
      };
      
      reader.onerror = () => reject(new Error('Error reading file'));
      
      if (isImageFile(file.name)) {
        reader.readAsDataURL(file);
      } else {
        reader.readAsText(file);
      }
    });
  };

  const processFiles = async (fileList: FileList): Promise<AttachedFile[]> => {
    const processedFiles: AttachedFile[] = [];
    
    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];
      
      // Check file size
      if (file.size > MAX_FILE_SIZE) {
        console.warn(`File ${file.name} is too large (${file.size} bytes). Maximum size is ${MAX_FILE_SIZE} bytes.`);
        continue;
      }
      
      // Check if file type is supported
      if (!isSupportedFile(file.name)) {
        console.warn(`File ${file.name} is not a supported file type.`);
        continue;
      }
      
      try {
        const content = await readFileContent(file);
        processedFiles.push({
          name: file.name,
          size: file.size,
          type: file.type,
          content,
          path: file.name
        });
      } catch (error) {
        console.error(`Error reading file ${file.name}:`, error);
      }
    }
    
    return processedFiles;
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    try {
      const processedFiles = await processFiles(files);
      if (processedFiles.length > 0) {
        onFilesSelected(processedFiles);
      }
    } catch (error) {
      console.error('Error processing selected files:', error);
    }

    // Reset the input value so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleButtonClick = () => {
    if (disabled) return;
    fileInputRef.current?.click();
  };

  // Create accept string from supported extensions
  const acceptString = [...SUPPORTED_TEXT_EXTENSIONS, ...SUPPORTED_IMAGE_EXTENSIONS].join(',');

  return (
    <div className="file-picker">
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptString}
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />
      
      <button
        onClick={handleButtonClick}
        disabled={disabled}
        className={`file-picker-button ${compact ? 'file-picker-compact' : ''}`}
        title="Attach files (text files, code files, images)"
      >
        <Paperclip size={compact ? 14 : 16} />
        {!compact && <span>Attach Files</span>}
      </button>
    </div>
  );
};

export default FilePicker;
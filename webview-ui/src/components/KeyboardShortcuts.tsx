import React from 'react';
import { X, Keyboard } from 'lucide-react';

interface KeyboardShortcutsProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Shortcut {
  keys: string[];
  description: string;
  category: string;
}

const shortcuts: Shortcut[] = [
  // Navigation
  { keys: ['Ctrl', 'Shift', 'X'], description: 'Focus Xyne Chat (global)', category: 'Navigation' },
  { keys: ['Ctrl', 'Shift', 'X', '→', 'Ctrl', 'Shift', 'F'], description: 'Search conversations', category: 'Navigation' },
  
  // Chat Actions  
  { keys: ['Ctrl', 'Shift', 'X', '→', 'Ctrl', 'Shift', 'N'], description: 'Start new chat', category: 'Chat' },
  { keys: ['Ctrl', 'Shift', 'X', '→', 'Ctrl', 'Shift', 'H'], description: 'Open chat history', category: 'Chat' },
  { keys: ['Ctrl', 'Shift', 'X', '→', 'Ctrl', 'Shift', 'I'], description: 'Edit custom instructions', category: 'Chat' },
  { keys: ['Ctrl', 'Shift', 'X', '→', 'Ctrl', 'Shift', 'R'], description: 'Regenerate last response', category: 'Chat' },
  
  // File Operations
  { keys: ['Ctrl', 'Shift', 'X', '→', 'Ctrl', 'Shift', 'U'], description: 'Attach files', category: 'Files' },
  { keys: ['Ctrl', 'Shift', 'X', '→', 'Ctrl', 'Shift', 'Alt', 'U'], description: 'Clear attached files', category: 'Files' },
  
  // Editing
  { keys: ['Ctrl', 'Shift', 'X', '→', 'Ctrl', 'Shift', 'C'], description: 'Copy last AI response', category: 'Editing' },
  { keys: ['Enter'], description: 'Send message', category: 'Editing' },
  { keys: ['Shift', 'Enter'], description: 'New line in input', category: 'Editing' },
  
  // Settings
  { keys: ['Ctrl', 'Shift', 'X', '→', 'Ctrl', 'Shift', 'S'], description: 'Open settings', category: 'Settings' },
  { keys: ['Ctrl', 'Shift', 'X', '→', 'Ctrl', 'Shift', 'Alt', 'R'], description: 'Refresh AI provider', category: 'Settings' },
  
  // Help
  { keys: ['Ctrl', 'Shift', 'X', '→', 'Shift', '/'], description: 'Show keyboard shortcuts', category: 'Help' },
  
  // Platform Notes
  { keys: ['ℹ️'], description: 'On Mac: Cmd instead of Ctrl', category: 'Info' },
  { keys: ['💡'], description: 'All shortcuts start with Ctrl+Shift+X followed by another key combo', category: 'Info' },
];

const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
  if (!acc[shortcut.category]) {
    acc[shortcut.category] = [];
  }
  acc[shortcut.category].push(shortcut);
  return acc;
}, {} as Record<string, Shortcut[]>);

const KeyboardShortcuts: React.FC<KeyboardShortcutsProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  const formatKeys = (keys: string[]): React.ReactNode => {
    return keys.map((key, index) => (
      <React.Fragment key={key}>
        {index > 0 && <span className="key-separator">+</span>}
        <kbd className="keyboard-key">{key}</kbd>
      </React.Fragment>
    ));
  };

  return (
    <div className="search-overlay">
      <div className="search-panel shortcuts-panel">
        {/* Header */}
        <div className="search-header">
          <div className="search-title">
            <Keyboard size={18} />
            <h3>Keyboard Shortcuts</h3>
          </div>
          <button className="search-close" onClick={onClose}>
            <X size={16} />
          </button>
        </div>

        {/* Content */}
        <div className="shortcuts-content">
          {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
            <div key={category} className="shortcuts-category">
              <h4 className="shortcuts-category-title">{category}</h4>
              <div className="shortcuts-list">
                {categoryShortcuts.map((shortcut, index) => (
                  <div key={index} className="shortcut-item">
                    <div className="shortcut-keys">
                      {formatKeys(shortcut.keys)}
                    </div>
                    <div className="shortcut-description">
                      {shortcut.description}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className="shortcuts-footer">
          <p>
            Press <kbd className="keyboard-key">Esc</kbd> to close this dialog
          </p>
        </div>
      </div>
    </div>
  );
};

export default KeyboardShortcuts;
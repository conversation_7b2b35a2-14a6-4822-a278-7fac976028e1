import React, { useEffect, useRef, useState } from 'react';
import { ChevronDown, ChevronRight, File } from 'lucide-react';

interface MonacoDiffProps {
  filePath: string;
  originalContent: string;
  newContent: string;
  diffSummary: string;
  language?: string;
}

// Simple diff line representation
interface DiffLine {
  type: 'context' | 'added' | 'removed';
  content: string;
  oldLineNumber?: number;
  newLineNumber?: number;
}

// Simple diff algorithm
const computeUnifiedDiff = (original: string, modified: string): DiffLine[] => {
  const originalLines = original.split('\n');
  const modifiedLines = modified.split('\n');
  const diffLines: DiffLine[] = [];
  
  let i = 0; // original pointer
  let j = 0; // modified pointer
  
  while (i < originalLines.length || j < modifiedLines.length) {
    if (i >= originalLines.length) {
      // Only modified lines remain - all additions
      while (j < modifiedLines.length) {
        diffLines.push({
          type: 'added',
          content: modifiedLines[j],
          newLineNumber: j + 1
        });
        j++;
      }
      break;
    }
    
    if (j >= modifiedLines.length) {
      // Only original lines remain - all deletions
      while (i < originalLines.length) {
        diffLines.push({
          type: 'removed',
          content: originalLines[i],
          oldLineNumber: i + 1
        });
        i++;
      }
      break;
    }
    
    if (originalLines[i] === modifiedLines[j]) {
      // Lines are the same - context
      diffLines.push({
        type: 'context',
        content: originalLines[i],
        oldLineNumber: i + 1,
        newLineNumber: j + 1
      });
      i++;
      j++;
    } else {
      // Lines are different - need to determine if it's modification, addition, or deletion
      
      // Look ahead to find next matching line
      let matchFound = false;
      let nextMatchI = -1;
      let nextMatchJ = -1;
      
      // Simple lookahead (up to 10 lines)
      for (let lookAhead = 1; lookAhead <= 10; lookAhead++) {
        if (i + lookAhead < originalLines.length && j + lookAhead < modifiedLines.length) {
          if (originalLines[i + lookAhead] === modifiedLines[j + lookAhead]) {
            nextMatchI = i + lookAhead;
            nextMatchJ = j + lookAhead;
            matchFound = true;
            break;
          }
        }
        
        // Check if original line matches a future modified line (insertion)
        if (j + lookAhead < modifiedLines.length && originalLines[i] === modifiedLines[j + lookAhead]) {
          // Current original line will appear later in modified - insertions before it
          for (let k = 0; k < lookAhead; k++) {
            diffLines.push({
              type: 'added',
              content: modifiedLines[j + k],
              newLineNumber: j + k + 1
            });
          }
          j += lookAhead;
          matchFound = true;
          break;
        }
        
        // Check if modified line matches a future original line (deletion)
        if (i + lookAhead < originalLines.length && modifiedLines[j] === originalLines[i + lookAhead]) {
          // Current modified line will appear later in original - deletions before it
          for (let k = 0; k < lookAhead; k++) {
            diffLines.push({
              type: 'removed',
              content: originalLines[i + k],
              oldLineNumber: i + k + 1
            });
          }
          i += lookAhead;
          matchFound = true;
          break;
        }
      }
      
      if (matchFound && nextMatchI !== -1 && nextMatchJ !== -1) {
        // Add all lines before the match as changes
        while (i < nextMatchI) {
          diffLines.push({
            type: 'removed',
            content: originalLines[i],
            oldLineNumber: i + 1
          });
          i++;
        }
        while (j < nextMatchJ) {
          diffLines.push({
            type: 'added',
            content: modifiedLines[j],
            newLineNumber: j + 1
          });
          j++;
        }
      } else if (!matchFound) {
        // No match found - treat as line replacement
        diffLines.push({
          type: 'removed',
          content: originalLines[i],
          oldLineNumber: i + 1
        });
        diffLines.push({
          type: 'added',
          content: modifiedLines[j],
          newLineNumber: j + 1
        });
        i++;
        j++;
      }
    }
  }
  
  return diffLines;
};

export const MonacoDiff: React.FC<MonacoDiffProps> = (props) => {
  console.log('[MonacoDiff] render props', props);
  const { filePath, originalContent, newContent, diffSummary, language } = props;
  if (!filePath || !originalContent || !newContent) {
    console.error('[MonacoDiff] Missing required prop!', { filePath, originalContent, newContent });
  }
  const [isExpanded, setIsExpanded] = useState(true);
  const diffContainerRef = useRef<HTMLDivElement>(null);
  
  const diffLines = React.useMemo(() => {
    return computeUnifiedDiff(originalContent || '', newContent || '');
  }, [originalContent, newContent]);

  useEffect(() => {
    if (!isExpanded || !diffContainerRef.current) return;
    
    const container = diffContainerRef.current;
    container.innerHTML = '';
    
    // Create unified diff view
    const diffView = document.createElement('div');
    diffView.style.fontFamily = 'var(--vscode-editor-font-family, "SF Mono", "Monaco", "Inconsolata", "Fira Code", "Consolas", monospace)';
    diffView.style.fontSize = '12px';
    diffView.style.lineHeight = '18px';
    diffView.style.backgroundColor = 'var(--vscode-editor-background, #1e1e1e)';
    diffView.style.color = 'var(--vscode-editor-foreground, #d4d4d4)';
    diffView.style.overflow = 'auto';
    diffView.style.height = '100%';
    diffView.style.padding = '8px';
    diffView.style.whiteSpace = 'pre';
    
    diffLines.forEach((line, index) => {
      const lineElement = document.createElement('div');
      lineElement.style.padding = '0 4px';
      lineElement.style.margin = '0';
      lineElement.style.whiteSpace = 'pre';
      lineElement.style.userSelect = 'text';
      
      let prefix = ' ';
      let backgroundColor = 'transparent';
      let color = 'var(--vscode-editor-foreground, #d4d4d4)';
      
      switch (line.type) {
        case 'added':
          prefix = '+';
          backgroundColor = 'var(--vscode-diffEditor-insertedLineBackground, rgba(155, 185, 85, 0.2))';
          color = 'var(--vscode-gitDecoration-addedResourceForeground, #81b88b)';
          break;
        case 'removed':
          prefix = '-';
          backgroundColor = 'var(--vscode-diffEditor-removedLineBackground, rgba(255, 0, 0, 0.2))';
          color = 'var(--vscode-gitDecoration-deletedResourceForeground, #c74e39)';
          break;
        case 'context':
          prefix = ' ';
          break;
      }
      
      lineElement.style.backgroundColor = backgroundColor;
      lineElement.style.color = color;
      
      // Add line number information for context
      const lineNumbers = [];
      if (line.oldLineNumber !== undefined) {
        lineNumbers.push(line.oldLineNumber.toString().padStart(4, ' '));
      } else {
        lineNumbers.push('    ');
      }
      
      if (line.newLineNumber !== undefined) {
        lineNumbers.push(line.newLineNumber.toString().padStart(4, ' '));
      } else {
        lineNumbers.push('    ');
      }
      
      const lineNumberSpan = document.createElement('span');
      lineNumberSpan.style.color = 'var(--vscode-editorLineNumber-foreground, #858585)';
      lineNumberSpan.style.marginRight = '8px';
      lineNumberSpan.style.userSelect = 'none';
      lineNumberSpan.textContent = lineNumbers.join(' ');
      
      const prefixSpan = document.createElement('span');
      prefixSpan.style.marginRight = '4px';
      prefixSpan.style.fontWeight = 'bold';
      prefixSpan.textContent = prefix;
      
      const contentSpan = document.createElement('span');
      contentSpan.textContent = line.content;
      
      lineElement.appendChild(lineNumberSpan);
      lineElement.appendChild(prefixSpan);
      lineElement.appendChild(contentSpan);
      
      diffView.appendChild(lineElement);
    });
    
    container.appendChild(diffView);
  }, [isExpanded, diffLines]);

  return (
    <div className="monaco-diff-viewer" style={{ 
      border: '1px solid var(--vscode-panel-border, #555)', 
      borderRadius: '4px',
      margin: '8px 0',
      overflow: 'hidden',
      backgroundColor: 'var(--vscode-editor-background, #1e1e1e)'
    }}>
      <div className="diff-header" style={{
        padding: '8px 12px',
        backgroundColor: 'var(--vscode-editorGroupHeader-tabsBackground, #252526)',
        borderBottom: isExpanded ? '1px solid var(--vscode-panel-border, #555)' : 'none',
        display: 'flex',
        alignItems: 'center',
        userSelect: 'none'
      }}>
        <button
          className="diff-toggle"
          onClick={() => setIsExpanded(!isExpanded)}
          style={{
            background: 'none',
            border: 'none',
            color: 'var(--vscode-editor-foreground, #ccc)',
            display: 'flex',
            alignItems: 'center',
            padding: '0',
            cursor: 'pointer',
            width: '100%',
            textAlign: 'left'
          }}
        >
          {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          <File size={16} style={{ marginLeft: '6px', marginRight: '8px' }} />
          <span className="diff-file-path" style={{ 
            fontSize: '13px', 
            fontFamily: 'var(--vscode-font-family, "SF Mono", monospace)',
            color: 'var(--vscode-editor-foreground, #cccccc)',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {filePath}
          </span>
          <span className="diff-summary" style={{ 
            marginLeft: '8px', 
            opacity: 0.7,
            fontSize: '12px',
            color: 'var(--vscode-descriptionForeground, #cccccc99)',
            fontStyle: 'italic'
          }}>
            ({diffSummary})
          </span>
        </button>
      </div>

      {isExpanded && (
        <div 
          ref={diffContainerRef}
          className="monaco-diff-container" 
          style={{ 
            height: '300px',
            width: '100%',
            overflow: 'hidden',
            backgroundColor: 'var(--vscode-editor-background, #1e1e1e)'
          }}
        />
      )}
    </div>
  );
};

export default MonacoDiff; 
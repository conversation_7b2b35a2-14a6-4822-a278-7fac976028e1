import React, { useState, useEffect } from 'react';
import { X, Server, Settings, Play, Square, Plus, Trash2 } from 'lucide-react';

interface McpServer {
  name: string;
  config: {
    name: string;
    type: 'stdio' | 'sse' | 'http';
    command?: string;
    args?: string[];
    url?: string;
    disabled?: boolean;
    autoApprove?: string[];
    timeout?: number;
  };
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
  tools?: Array<{
    name: string;
    description?: string;
    serverName: string;
  }>;
  resources?: Array<{
    uri: string;
    name?: string;
    description?: string;
    serverName: string;
  }>;
  lastError?: string;
  connected?: boolean;
}

interface McpServersModalProps {
  isOpen: boolean;
  onClose: () => void;
  onShowAddServer?: () => void;
  onEditServer?: (server: McpServer) => void;
}

export const McpServersModal: React.FC<McpServersModalProps> = ({ isOpen, onClose, onShowAddServer, onEditServer }) => {
  const [servers, setServers] = useState<McpServer[]>([]);
  const [activeTab, setActiveTab] = useState<'installed' | 'marketplace'>('installed');
  const [selectedServer, setSelectedServer] = useState<string | null>(null);

  // Get VS Code API
  const vscode = (window as any).vscode || (window as any).__vscodeApi;

  useEffect(() => {
    if (!isOpen) return;

    // Request current MCP servers when modal opens
    if (vscode) {
      vscode.postMessage({
        type: 'getMcpServers'
      });
    }

    // Listen for server updates
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      switch (message.type) {
        case 'mcpServers':
          setServers(message.servers || []);
          break;
        case 'mcpServerUpdated':
          setServers(prev => prev.map(server => 
            server.name === message.server.name ? message.server : server
          ));
          break;
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [isOpen, vscode]);

  const handleToggleServer = async (serverName: string) => {
    console.log('🔄 Toggle server clicked:', serverName);
    if (vscode) {
      vscode.postMessage({
        type: 'toggleMcpServer',
        serverName
      });
    } else {
      console.error('❌ VS Code API not available');
    }
  };

  const handleRemoveServer = async (serverName: string) => {
    console.log('🗑️ Remove server clicked:', serverName);
    if (vscode) {
      vscode.postMessage({
        type: 'removeMcpServer',
        serverName
      });
    } else {
      console.error('❌ VS Code API not available');
    }
  };

  const getStatusColor = (status: string, connected?: boolean) => {
    if (connected && status === 'connected') return 'text-green-500';
    if (status === 'connecting') return 'text-yellow-500';
    if (status === 'error') return 'text-red-500';
    return 'text-gray-500';
  };

  const getStatusIcon = (status: string, connected?: boolean) => {
    if (connected && status === 'connected') return '●';
    if (status === 'connecting') return '◐';
    if (status === 'error') return '●';
    return '○';
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'var(--vscode-editor-background)',
        border: '1px solid var(--vscode-panel-border)',
        borderRadius: '8px',
        maxWidth: '800px',
        width: '90%',
        maxHeight: '80vh',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '16px',
          borderBottom: '1px solid var(--vscode-panel-border)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Server size={20} style={{ color: 'var(--vscode-foreground)' }} />
            <h2 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: 'var(--vscode-foreground)',
              margin: 0
            }}>
              MCP Servers
            </h2>
          </div>
          <button
            onClick={onClose}
            style={{
              padding: '4px',
              backgroundColor: 'transparent',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              color: 'var(--vscode-foreground)'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-toolbar-hoverBackground)'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
          >
            <X size={16} />
          </button>
        </div>

        {/* Tabs */}
        <div style={{
          display: 'flex',
          borderBottom: '1px solid var(--vscode-panel-border)'
        }}>
          {[
            { id: 'installed', label: 'Installed', count: servers.length },
            { id: 'marketplace', label: 'Marketplace', count: 0 }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              style={{
                padding: '8px 16px',
                borderBottom: activeTab === tab.id ? '2px solid var(--vscode-focusBorder)' : '2px solid transparent',
                backgroundColor: 'transparent',
                border: 'none',
                cursor: 'pointer',
                color: activeTab === tab.id ? 'var(--vscode-foreground)' : 'var(--vscode-descriptionForeground)',
                transition: 'all 0.2s'
              }}
              onMouseOver={(e) => {
                if (activeTab !== tab.id) {
                  e.currentTarget.style.color = 'var(--vscode-foreground)';
                }
              }}
              onMouseOut={(e) => {
                if (activeTab !== tab.id) {
                  e.currentTarget.style.color = 'var(--vscode-descriptionForeground)';
                }
              }}
            >
              {tab.label}
              {tab.count > 0 && (
                <span style={{
                  marginLeft: '8px',
                  padding: '2px 8px',
                  fontSize: '12px',
                  backgroundColor: 'var(--vscode-badge-background)',
                  color: 'var(--vscode-badge-foreground)',
                  borderRadius: '12px'
                }}>
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>

        {/* Content */}
        <div style={{
          flex: 1,
          overflow: 'auto',
          padding: '16px'
        }}>
          {activeTab === 'installed' && (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              {/* Add Server Button */}
              <button
                onClick={() => {
                  if (onShowAddServer) {
                    onShowAddServer();
                  } else if (vscode) {
                    vscode.postMessage({
                      type: 'showAddMcpServer'
                    });
                  }
                }}
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '2px dashed var(--vscode-panel-border)',
                  borderRadius: '8px',
                  backgroundColor: 'transparent',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px',
                  color: 'var(--vscode-descriptionForeground)',
                  transition: 'all 0.2s'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.borderColor = 'var(--vscode-focusBorder)';
                  e.currentTarget.style.color = 'var(--vscode-foreground)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.borderColor = 'var(--vscode-panel-border)';
                  e.currentTarget.style.color = 'var(--vscode-descriptionForeground)';
                }}
              >
                <Plus size={16} />
                Add MCP Server
              </button>

              {/* Server List */}
              {servers.length === 0 ? (
                <div style={{
                  textAlign: 'center',
                  padding: '32px 0',
                  color: 'var(--vscode-descriptionForeground)'
                }}>
                  <Server size={48} style={{
                    margin: '0 auto 16px',
                    opacity: 0.5,
                    color: 'var(--vscode-descriptionForeground)'
                  }} />
                  <p style={{ margin: 0, fontSize: '16px' }}>No MCP servers configured</p>
                  <p style={{
                    fontSize: '14px',
                    margin: '8px 0 0',
                    color: 'var(--vscode-descriptionForeground)'
                  }}>Add a server to get started with external tools and resources</p>
                </div>
              ) : (
                servers.map(server => (
                  <div
                    key={server.name}
                    style={{
                      border: '1px solid var(--vscode-panel-border)',
                      borderRadius: '8px',
                      padding: '16px',
                      backgroundColor: 'var(--vscode-editor-background)',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-list-hoverBackground)'}
                    onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-editor-background)'}
                  >
                    <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
                      <div style={{ flex: 1, minWidth: 0 }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                          <span style={{ 
                            fontSize: '18px', 
                            color: getStatusColor(server.status, server.connected) === 'text-green-500' ? '#4ade80' :
                                  getStatusColor(server.status, server.connected) === 'text-yellow-500' ? '#eab308' :
                                  getStatusColor(server.status, server.connected) === 'text-red-500' ? '#ef4444' : 
                                  'var(--vscode-descriptionForeground)'
                          }}>
                            {getStatusIcon(server.status, server.connected)}
                          </span>
                          <h3 style={{ 
                            margin: 0, 
                            fontSize: '16px', 
                            fontWeight: '500', 
                            color: 'var(--vscode-foreground)' 
                          }}>
                            {server.name}
                          </h3>
                          <span style={{
                            padding: '2px 8px',
                            fontSize: '12px',
                            backgroundColor: 'var(--vscode-badge-background)',
                            color: 'var(--vscode-badge-foreground)',
                            borderRadius: '4px'
                          }}>
                            {server.config.type}
                          </span>
                          {server.config.disabled && (
                            <span style={{
                              padding: '2px 8px',
                              fontSize: '12px',
                              backgroundColor: 'var(--vscode-errorForeground)',
                              color: 'white',
                              borderRadius: '4px'
                            }}>
                              Disabled
                            </span>
                          )}
                        </div>
                        
                        <div style={{ fontSize: '14px', color: 'var(--vscode-descriptionForeground)' }}>
                          {server.config.type === 'stdio' && server.config.command && (
                            <div style={{ marginBottom: '4px', fontFamily: 'var(--vscode-editor-font-family)', fontSize: '13px' }}>
                              <strong>Command:</strong> {server.config.command} {server.config.args?.join(' ')}
                            </div>
                          )}
                          {(server.config.type === 'http' || server.config.type === 'sse') && server.config.url && (
                            <div style={{ marginBottom: '4px' }}>
                              <strong>URL:</strong> {server.config.url}
                            </div>
                          )}
                          {server.lastError && (
                            <div style={{ 
                              color: 'var(--vscode-errorForeground)', 
                              marginTop: '4px',
                              padding: '8px',
                              backgroundColor: 'var(--vscode-inputValidation-errorBackground)',
                              borderRadius: '4px',
                              fontSize: '12px'
                            }}>
                              <strong>Error:</strong> {server.lastError}
                            </div>
                          )}
                        </div>

                        {/* Tools and Resources */}
                        {server.connected && (server.tools?.length || server.resources?.length) && (
                          <div style={{ 
                            marginTop: '12px', 
                            display: 'flex', 
                            gap: '16px', 
                            fontSize: '13px',
                            padding: '8px',
                            backgroundColor: 'var(--vscode-editor-inactiveSelectionBackground)',
                            borderRadius: '4px'
                          }}>
                            {server.tools && server.tools.length > 0 && (
                              <span style={{ color: 'var(--vscode-foreground)' }}>
                                🔧 {server.tools.length} tool{server.tools.length !== 1 ? 's' : ''}
                              </span>
                            )}
                            {server.resources && server.resources.length > 0 && (
                              <span style={{ color: 'var(--vscode-foreground)' }}>
                                📚 {server.resources.length} resource{server.resources.length !== 1 ? 's' : ''}
                              </span>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Actions */}
                      <div style={{ 
                        display: 'flex', 
                        alignItems: 'flex-start', 
                        gap: '4px',
                        flexShrink: 0,
                        minWidth: '120px'
                      }}>
                        <button
                          onClick={() => handleToggleServer(server.name)}
                          style={{
                            padding: '8px',
                            backgroundColor: 'transparent',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                            color: 'var(--vscode-foreground)',
                            display: 'flex',
                            alignItems: 'center',
                            minWidth: '32px',
                            minHeight: '32px',
                            justifyContent: 'center'
                          }}
                          title={server.config.disabled ? 'Enable server' : 'Disable server'}
                          onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-toolbar-hoverBackground)'}
                          onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                        >
                          {server.config.disabled ? (
                            <Play size={16} />
                          ) : (
                            <Square size={16} />
                          )}
                        </button>
                        <button
                          onClick={() => {
                            if (onEditServer) {
                              onEditServer(server);
                            } else {
                              // Fallback: open VS Code settings if no edit handler
                              if (vscode) {
                                vscode.postMessage({
                                  type: 'openMcpSettings'
                                });
                              }
                            }
                          }}
                          style={{
                            padding: '8px',
                            backgroundColor: 'transparent',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                            color: 'var(--vscode-foreground)',
                            display: 'flex',
                            alignItems: 'center',
                            minWidth: '32px',
                            minHeight: '32px',
                            justifyContent: 'center'
                          }}
                          title="Edit server settings"
                          onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-toolbar-hoverBackground)'}
                          onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                        >
                          <Settings size={16} />
                        </button>
                        <button
                          onClick={() => handleRemoveServer(server.name)}
                          style={{
                            padding: '8px',
                            backgroundColor: 'transparent',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                            color: 'var(--vscode-errorForeground)',
                            display: 'flex',
                            alignItems: 'center',
                            minWidth: '32px',
                            minHeight: '32px',
                            justifyContent: 'center'
                          }}
                          title="Remove server"
                          onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-toolbar-hoverBackground)'}
                          onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}

          {activeTab === 'marketplace' && (
            <div style={{
              textAlign: 'center',
              padding: '32px 0',
              color: 'var(--vscode-descriptionForeground)'
            }}>
              <Server size={48} style={{
                margin: '0 auto 16px',
                opacity: 0.5,
                color: 'var(--vscode-descriptionForeground)'
              }} />
              <p style={{ margin: 0, fontSize: '16px' }}>MCP Marketplace</p>
              <p style={{
                fontSize: '14px',
                margin: '8px 0 0',
                color: 'var(--vscode-descriptionForeground)'
              }}>Coming soon - discover community MCP servers</p>
            </div>
          )}

        </div>

        {/* Footer */}
        <div style={{
          borderTop: '1px solid var(--vscode-panel-border)',
          padding: '16px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{
            fontSize: '14px',
            color: 'var(--vscode-descriptionForeground)'
          }}>
            {servers.filter(s => s.connected).length} of {servers.length} servers connected
          </div>
          <button
            onClick={onClose}
            style={{
              padding: '8px 16px',
              backgroundColor: 'var(--vscode-button-background)',
              color: 'var(--vscode-button-foreground)',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-button-hoverBackground)'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-button-background)'}
          >
            Done
          </button>
        </div>
      </div>
    </div>
  );
};

export default McpServersModal;
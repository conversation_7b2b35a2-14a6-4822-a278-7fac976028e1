import React from "react";
import { Bot } from "lucide-react";

export const LoadingMessage: React.FC = () => {
  return (
    <div className="message-container">
      {/* Avatar */}
      <div className="message-avatar">
        <div className="avatar-ai">
          <Bot size={16} />
        </div>
      </div>

      {/* Loading Content */}
      <div className="message-content">
        <div className="message-bubble message-ai">
          <div className="cursor-loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoadingMessage;
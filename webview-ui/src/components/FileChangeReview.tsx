import React, { useState } from 'react';
import { Check, X, File } from 'lucide-react';
import MonacoDiff from './MonacoDiff';

interface FileChangeReviewProps {
  filePath: string;
  originalContent: string;
  newContent: string;
  diffSummary: string;
  language?: string;
  staged?: boolean;
  changeId?: string;
  reviewStatus?: 'pending' | 'accepted' | 'rejected' | 'auto_approve';
  onAccept?: (filePath: string, originalContent: string, newContent: string, changeId?: string) => void;
  onReject?: (filePath: string, originalContent: string, changeId?: string, targetContent?: string) => void;
  isLatestReviewForFile?: boolean;
  isReadOnly?: boolean;
}

export const FileChangeReview: React.FC<FileChangeReviewProps> = ({
  filePath,
  originalContent,
  newContent,
  diffSummary,
  language,
  staged = false,
  changeId,
  reviewStatus = 'pending',
  onAccept,
  onReject,
  isLatestReviewForFile = true,
  isReadOnly = false
}) => {
  console.log('[FileChangeReview] props', { filePath, originalContent, newContent, diffSummary, language, staged, changeId, reviewStatus });
  const [isExpanded, setIsExpanded] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleAccept = async () => {
    if (isProcessing || !onAccept) return;
    
    setIsProcessing(true);
    try {
      await onAccept(filePath, originalContent, newContent, changeId);
    } catch (error) {
      console.error('Error accepting file change:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReject = async () => {
    if (isProcessing || !onReject) return;
    
    setIsProcessing(true);
    try {
      await onReject(filePath, originalContent, changeId, originalContent);
    } catch (error) {
      console.error('Error rejecting file change:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="file-change-review" style={{ 
      border: '1px solid var(--vscode-panel-border, #555)', 
      borderRadius: '8px',
      margin: '20px 0 28px 0',
      overflow: 'visible',
      backgroundColor: 'var(--vscode-editor-background, #1e1e1e)',
      boxShadow: '0 2px 8px 0 rgba(0,0,0,0.08)'
    }}>
      <div style={{background: 'red', color: 'white', padding: 4, fontWeight: 'bold'}}>RENDERED FILE CHANGE REVIEW</div>
      {/* Header */}
      <div className="file-change-review-header" style={{
        padding: '12px 16px',
        backgroundColor: 'var(--vscode-editorGroupHeader-tabsBackground, #252526)',
        borderBottom: isExpanded ? '1px solid var(--vscode-panel-border, #555)' : 'none',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        userSelect: 'none'
      }}>
        <div className="file-change-review-title" style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          flex: 1
        }}>
          <button
            className="file-change-review-toggle"
            onClick={() => setIsExpanded(!isExpanded)}
            style={{
              background: 'none',
              border: 'none',
              color: 'var(--vscode-editor-foreground, #ccc)',
              display: 'flex',
              alignItems: 'center',
              padding: '0',
              cursor: 'pointer',
              marginRight: '8px'
            }}
          >
            {isExpanded ? '▼' : '▶'}
          </button>
          <File size={16} style={{ color: 'var(--vscode-editor-foreground, #ccc)' }} />
          <span className="file-change-review-path" style={{ 
            fontSize: '13px', 
            fontFamily: 'var(--vscode-font-family, "SF Mono", monospace)',
            color: 'var(--vscode-editor-foreground, #cccccc)',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            flex: 1
          }}>
            {filePath}
          </span>
          <span className="file-change-review-summary" style={{ 
            marginLeft: '8px', 
            opacity: 0.7,
            fontSize: '12px',
            color: 'var(--vscode-descriptionForeground, #cccccc99)',
            fontStyle: 'italic'
          }}>
            ({diffSummary})
          </span>
                     {staged && (
             <span className="file-change-review-staged" style={{
               marginLeft: '8px',
               fontSize: '11px',
               backgroundColor: 'var(--vscode-gitDecoration-modifiedResourceForeground, #ffa500)',
               color: 'white',
               padding: '2px 6px',
               borderRadius: '4px',
               fontWeight: '500'
             }}>
               STAGED
             </span>
           )}
        </div>
      </div>

      {/* Content */}
      {isExpanded && (
        <>
          {/* Diff Viewer */}
          <div className="file-change-review-diff" style={{ 
            minHeight: '300px',
            borderBottom: '1px solid var(--vscode-panel-border, #555)'
          }}>
            <MonacoDiff
              filePath={filePath}
              originalContent={originalContent}
              newContent={newContent}
              diffSummary={diffSummary}
              language={language}
            />
          </div>

          {/* Action Buttons or Status */}
          {reviewStatus === 'pending' && isLatestReviewForFile && !isReadOnly && (
            <div className="file-change-review-actions" style={{
              display: 'flex',
              gap: '12px',
              padding: '16px',
              backgroundColor: 'var(--vscode-editorGroupHeader-tabsBackground, #252526)',
              justifyContent: 'flex-end'
            }}>
              <button
                className="file-change-review-button reject"
                onClick={handleReject}
                disabled={isProcessing}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  padding: '8px 16px',
                  border: '1px solid var(--vscode-errorForeground, #f48771)',
                  borderRadius: '6px',
                  background: 'var(--vscode-errorForeground, #f48771)',
                  color: 'white',
                  fontWeight: '500',
                  cursor: isProcessing ? 'not-allowed' : 'pointer',
                  opacity: isProcessing ? 0.6 : 1,
                  transition: 'all 0.2s ease'
                }}
              >
                <X size={16} />
                Reject Changes
              </button>
              <button
                className="file-change-review-button accept"
                onClick={handleAccept}
                disabled={isProcessing}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  padding: '8px 16px',
                  border: '1px solid var(--vscode-gitDecoration-addedResourceForeground, #81b88b)',
                  borderRadius: '6px',
                  background: 'var(--vscode-gitDecoration-addedResourceForeground, #81b88b)',
                  color: 'white',
                  fontWeight: '500',
                  cursor: isProcessing ? 'not-allowed' : 'pointer',
                  opacity: isProcessing ? 0.6 : 1,
                  transition: 'all 0.2s ease'
                }}
              >
                <Check size={16} />
                Accept Changes
              </button>
            </div>
          )}
          {reviewStatus === 'accepted' && (
            <div className="file-change-review-status" style={{ color: 'green', padding: 12, fontWeight: 500, textAlign: 'right' }}>
              ✅ Change Accepted
            </div>
          )}
          {reviewStatus === 'rejected' && (
            <div className="file-change-review-status" style={{ color: 'red', padding: 12, fontWeight: 500, textAlign: 'right' }}>
              ❌ Change Rejected
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default FileChangeReview; 
import React, { useState, useEffect, ChangeEvent } from 'react';
import { Message, ToolExecution } from '../types';
import { ChevronDown, ChevronRight, Copy, RotateCcw, Check, Edit } from 'lucide-react';
import MonacoDiff from './MonacoDiff';
import FileChangeReview from './FileChangeReview';
import EnhancedMarkdown from './EnhancedMarkdown';
import AttachedFiles from './AttachedFiles';

interface ChatMessageProps {
  message: Message;
  isStreaming?: boolean;
  onRegenerateMessage?: (messageId: number) => void;
  onEditMessage?: (messageId: number, newContent: string) => void;
  onRunCode?: (code: string, language: string) => void;
  diffData?: any; // Diff data passed from ChatView
  showCostPerMessage?: boolean; // Control whether to show cost per message
  onAcceptFileChange?: (filePath: string, originalContent: string, newContent: string, changeId?: string) => void;
  onRejectFileChange?: (filePath: string, originalContent: string, changeId?: string, targetContent?: string) => void;
  isLatestReviewForFile?: boolean;
  isReadOnly?: boolean;
  onRestoreCheckpoint?: (messageId: string) => void;
  onContinueAfterTimeout?: () => void;
}

interface ToolCall {
  tool: string;
  params: Record<string, string>;
  fullMatch: string;
}

// Extract tool calls from message content
const extractToolCalls = (content: string): { cleanContent: string; toolCalls: ToolCall[] } => {
  const toolCallRegex = /<(\w+)>([\s\S]*?)<\/\1>/g;
  const toolCalls: ToolCall[] = [];
  let match;

  while ((match = toolCallRegex.exec(content)) !== null) {
    const tool = match[1];
    const paramsContent = match[2];
    const params: Record<string, string> = {};

    // Extract parameters from within the tool
    const paramRegex = /<(\w+)>([\s\S]*?)<\/\1>/g;
    let paramMatch;
    while ((paramMatch = paramRegex.exec(paramsContent)) !== null) {
      params[paramMatch[1]] = paramMatch[2].trim();
    }

    toolCalls.push({
      tool,
      params,
      fullMatch: match[0]
    });
  }

  // Remove tool calls from content
  const cleanContent = content.replace(toolCallRegex, '').trim();

  return { cleanContent, toolCalls };
};

export const ChatMessage: React.FC<ChatMessageProps> = ({ message, isStreaming, onRegenerateMessage, onEditMessage, onRunCode, diffData, showCostPerMessage = false, onAcceptFileChange, onRejectFileChange, isLatestReviewForFile, isReadOnly, onRestoreCheckpoint, onContinueAfterTimeout }) => {
  const isUser = message.sender === 'user';
  const [showToolCalls, setShowToolCalls] = useState(false);
  const [copied, setCopied] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content);

  // Extract tool calls from AI messages - but hide them during streaming
  const { cleanContent, toolCalls } = !isUser && !isStreaming ? extractToolCalls(message.content) : { cleanContent: message.content, toolCalls: [] };
  const toolExecutions = message.metadata?.tool_executions || [];
  

  // During streaming, filter out partial tool calls to avoid showing raw XML
  const displayContent = isStreaming && !isUser ? 
    message.content
      .replace(/<\w+(?:\s[^>]*)?[\s\S]*?$/g, '') // Remove incomplete opening tags and everything after
      .replace(/<\/?\w+[^>]*>/g, '') // Remove any remaining complete tags
      .trim() || message.content.replace(/<[^>]*>[\s\S]*$/g, '').trim() :
    cleanContent;

  // Diff data is now passed as a prop from ChatView, no global listener needed

    // Copy message content to clipboard
  const handleCopyMessage = async () => {
    try {
      await navigator.clipboard.writeText(displayContent);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy message:', err);
    }
  };

    // Handle regenerate message
  const handleRegenerateMessage = () => {
    if (onRegenerateMessage && !isUser) {
      onRegenerateMessage(message.id);
    }
  };

  // Handle edit message
  const handleEditMessage = () => {
    if (isUser) {
      setIsEditing(true);
    }
  };

  // Save edited message
  const handleSaveEdit = () => {
    if (onEditMessage && editContent.trim() !== message.content) {
      onEditMessage(message.id, editContent.trim());
    }
    setIsEditing(false);
  };

  // Cancel edit
  const handleCancelEdit = () => {
    setEditContent(message.content);
    setIsEditing(false);
  };

  // File Change Review logic
  if (message.metadata?.fileChangeReview) {
    console.log('[ChatMessage] Rendering FileChangeReview for', message.metadata.fileChangeReview.filePath, { message, fileChangeReview: message.metadata.fileChangeReview });
    return (
      <FileChangeReview
        {...message.metadata.fileChangeReview}
        onAccept={onAcceptFileChange}
        onReject={onRejectFileChange}
        isLatestReviewForFile={isLatestReviewForFile}
        isReadOnly={isReadOnly}
      />
    );
  }

  return (
    <div className={`message-container ${isUser ? 'user' : 'ai'}`}>
      {/* Tool Calls Section (for AI messages only) */}
      {!isUser && (toolCalls.length > 0 || toolExecutions.length > 0) && (
        <div className="tool-calls-section">
          <button
            className="tool-calls-toggle"
            onClick={() => setShowToolCalls(!showToolCalls)}
          >
            {showToolCalls ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
            <span className="tool-calls-summary">
              {toolExecutions.length > 0 ? toolExecutions.length : toolCalls.length} tool call{(toolExecutions.length > 0 ? toolExecutions.length : toolCalls.length) > 1 ? 's' : ''} executed
            </span>
            <span className="tool-calls-details">
              {toolExecutions.length > 0 
                ? toolExecutions.map(exec => exec.toolCall.name).join(', ')
                : toolCalls.map(call => call.tool).join(', ')
              }
            </span>
          </button>

          {showToolCalls && (
            <div className="tool-calls-expanded">
              {toolExecutions.length > 0 ? (
                toolExecutions.map((execution, index) => (
                  <div key={index} className="tool-call-item">
                    <div className="tool-call-header">
                      <span className="tool-name">{execution.toolCall.name}</span>
                    </div>
                    <div className="tool-call-params">
                      {execution.toolCall.params && Object.entries(execution.toolCall.params).map(([key, value]) => (
                        <div key={key} className="tool-param">
                          <span className="param-key">{key}:</span>
                          <span className="param-value">{String(value)}</span>
                        </div>
                      ))}
                      {execution.toolResult && (
                        <div className="tool-param">
                          <span className="param-key">result:</span>
                          <div className="param-result">
                            {execution.toolResult.error ? (
                              <div className="result-error-inline">{execution.toolResult.error}</div>
                            ) : (
                              <div className="result-output-inline">
                                {(() => {
                                  const result = execution.toolResult.result;
                                  let displayText = '';
                                  
                                  if (typeof result === 'string') {
                                    displayText = result;
                                  } else if (typeof result === 'object' && result !== null) {
                                    displayText = JSON.stringify(result, null, 2);
                                  } else {
                                    displayText = String(result);
                                  }
                                  
                                  return displayText;
                                })()}
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                toolCalls.map((toolCall, index) => (
                  <div key={index} className="tool-call-item">
                    <div className="tool-call-header">
                      <span className="tool-name">{toolCall.tool}</span>
                    </div>
                    <div className="tool-call-params">
                      {Object.entries(toolCall.params).map(([key, value]) => (
                        <div key={key} className="tool-param">
                          <span className="param-key">{key}:</span>
                          <span className="param-value">{value}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      )}

      {/* Legacy Monaco Diff Section (for backward compatibility) */}
      {!isUser && diffData && !message.metadata?.type && (
        <div className="diff-viewer-container">
          <div className="diff-header">
            <span className="diff-title">File Changes: {diffData.filePath}</span>
            <span className="diff-summary">{diffData.diffSummary}</span>
          </div>
          <MonacoDiff
            filePath={diffData.filePath}
            originalContent={diffData.originalContent}
            newContent={diffData.newContent}
            diffSummary={diffData.diffSummary}
          />
        </div>
      )}

      {/* Message Content */}
      <div className="message-content">
        <div className={`message-bubble ${isUser ? 'message-user' : 'message-ai'}`}>
          {isUser ? (
            <div className="user-message-text">
              {isEditing ? (
                <div className="edit-message-container">
                  <textarea
                    value={editContent}
                    onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setEditContent(e.target.value)}
                    className="edit-message-textarea"
                    rows={3}
                    autoFocus
                  />
                  <div className="edit-message-actions">
                    <button 
                      onClick={handleSaveEdit}
                      className="edit-action-button save"
                      disabled={!editContent.trim()}
                    >
                      <Check size={14} />
                      Save
                    </button>
                    <button 
                      onClick={handleCancelEdit}
                      className="edit-action-button cancel"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              ) : (
                <div>
                  {message.content}
                  {message.attachedFiles && message.attachedFiles.length > 0 && (
                    <div style={{ marginTop: '12px' }}>
                      <AttachedFiles
                        files={message.attachedFiles}
                        onRemoveFile={() => {}} // Read-only in message display
                        onClearAll={() => {}} // Read-only in message display
                        compact={true}
                      />
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            displayContent && displayContent.trim() && (
              <div className="ai-message-content">
                <EnhancedMarkdown
                  source={displayContent}
                  onRunCode={onRunCode}
                  style={{
                    backgroundColor: 'transparent',
                    color: 'inherit',
                    fontFamily: 'inherit',
                    fontSize: 'inherit',
                    lineHeight: 'inherit',
                  }}
                />
                {isStreaming && <span className="streaming-cursor-enhanced"></span>}
              </div>
            )
          )}
            {!isStreaming && !isEditing && (
            <div className="message-actions">
                <button 
                onClick={handleCopyMessage}
                className="message-action-button"
                title="Copy message"
                >
                {copied ? <Check size={12} /> : <Copy size={12} />}
                </button>
                
                {isUser && (
                <button 
                    onClick={handleEditMessage}
                    className="message-action-button"
                    title="Edit message"
                >
                    <Edit size={12} />
                </button>
                )}
                
                {!isUser && onRegenerateMessage && (
                <button 
                    onClick={handleRegenerateMessage}
                    className="message-action-button"
                    title="Regenerate response"
                >
                    <RotateCcw size={12} />
                </button>
                )}
            </div>
          )}
        </div>
        {/* Model Information */}
        {!isUser && message.metadata && (
          <div className="message-metadata">
            {message.metadata.model && (
              <span className="metadata-item">
                <span className="metadata-label">Model:</span>
                <span className="metadata-value">{message.metadata.model}</span>
              </span>
            )}
            {(message.metadata.tokens) && (message.metadata.tokens > 0) && (
              <span className="metadata-item">
                <span className="metadata-label">Tokens:</span>
                <span className="metadata-value">{message.metadata.tokens}</span>
              </span>
            )}
            {(typeof message.metadata.cost === 'number' && showCostPerMessage) && (
              <span className="metadata-item">
                <span className="metadata-label">Cost:</span>
                <span className="metadata-value">${message.metadata.cost.toFixed(4)}</span>
              </span>
            )}
          </div>
        )}

      </div>
      {/* Restore checkpoint button for user messages */}
      {isUser && onRestoreCheckpoint && (
        <button
          className="restore-checkpoint-btn"
          onClick={() => onRestoreCheckpoint(String(message.id))}
          style={{ marginLeft: 8, padding: '2px 8px', borderRadius: 4, border: '1px solid #888', background: '#222', color: '#fff', fontSize: 12 }}
        >
          ↩️ Restore checkpoint
        </button>
      )}
      {/* Continue button for streaming timeout messages */}
      {message.metadata?.isStreamingTimeout && onContinueAfterTimeout && (
        <button
          className="continue-after-timeout-btn"
          onClick={onContinueAfterTimeout}
          style={{ 
            marginTop: 12, 
            padding: '8px 16px', 
            borderRadius: 6, 
            border: 'none', 
            background: '#007acc', 
            color: '#fff', 
            fontSize: 14,
            fontWeight: 'bold',
            cursor: 'pointer'
          }}
        >
          Continue Processing
        </button>
      )}
    </div>
  );
};

export default ChatMessage;
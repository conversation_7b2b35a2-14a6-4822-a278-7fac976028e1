import React, { useEffect } from 'react';
import { X, ExternalLink } from 'lucide-react';

interface ImageModalProps {
  isOpen: boolean;
  imageUrl: string;
  imageName: string;
  onClose: () => void;
  onOpenInVSCode?: (imageUrl: string, imageName: string) => void;
}

export const ImageModal: React.FC<ImageModalProps> = ({ 
  isOpen, 
  imageUrl, 
  imageName, 
  onClose,
  onOpenInVSCode
}) => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleOpenInVSCode = () => {
    if (onOpenInVSCode) {
      onOpenInVSCode(imageUrl, imageName);
    }
  };

  return (
    <div 
      className="image-modal-backdrop"
      onClick={handleBackdropClick}
    >
      <div className="image-modal-container">
        <div className="image-modal-header">
          <h3 className="image-modal-title">{imageName}</h3>
          <div className="image-modal-actions">
            {onOpenInVSCode && (
              <button 
                className="image-modal-action" 
                onClick={handleOpenInVSCode}
                title="Open in VS Code"
              >
                <ExternalLink size={16} />
                Open in VS Code
              </button>
            )}
            <button 
              className="image-modal-close" 
              onClick={onClose}
              title="Close image"
            >
              <X size={20} />
            </button>
          </div>
        </div>
        <div className="image-modal-content">
          <img 
            src={imageUrl} 
            alt={imageName}
            className="image-modal-image"
          />
        </div>
      </div>
    </div>
  );
};

export default ImageModal;
/* Sleek Chat Input Styles */
.sleek-input-wrapper {
  position: relative;
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
}

/* Main Input Container */
.sleek-input-container {
  position: relative;
  display: flex;
  align-items: flex-end;
  background: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  border-radius: 12px;
  padding: 12px 16px;
  gap: 12px;
  transition: border-color 0.2s ease;
}

.sleek-input-container:focus-within {
  border-color: var(--vscode-focusBorder);
  box-shadow: 0 0 0 1px var(--vscode-focusBorder);
}

/* Input Field Wrapper */
.sleek-input-field-wrapper {
  position: relative;
  flex: 1;
  min-height: 20px;
}

/* Placeholder */
.sleek-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  color: var(--vscode-input-placeholderForeground);
  pointer-events: none;
  font-size: 14px;
  line-height: 20px;
  user-select: none;
}

/* Input Field */
.sleek-input-field {
  width: 100%;
  min-height: 20px;
  max-height: 120px;
  overflow-y: auto;
  background: transparent;
  border: none;
  outline: none;
  color: var(--vscode-input-foreground);
  font-size: 14px;
  line-height: 20px;
  font-family: var(--vscode-font-family);
  resize: none;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.sleek-input-field:empty::before {
  content: "";
  display: inline-block;
}

/* Send Button */
.sleek-send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.sleek-send-button:hover:not(:disabled) {
  background: var(--vscode-button-hoverBackground);
  transform: scale(1.05);
}

.sleek-send-button:active:not(:disabled) {
  transform: scale(0.95);
}

.sleek-send-button:disabled {
  background: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  cursor: not-allowed;
  opacity: 0.6;
}

/* Bottom Controls */
.sleek-bottom-controls {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 8px;
  padding: 0;
}

.sleek-controls-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Mode and Model Containers */
.sleek-mode-container,
.sleek-model-container {
  position: relative;
}

/* Dropdowns */
.sleek-mode-dropdown,
.sleek-model-dropdown {
  background: var(--vscode-dropdown-background);
  color: var(--vscode-dropdown-foreground);
  border: 1px solid var(--vscode-dropdown-border);
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  font-family: var(--vscode-font-family);
  cursor: pointer;
  outline: none;
  transition: all 0.2s ease;
  min-width: 80px;
}

.sleek-mode-dropdown:hover,
.sleek-model-dropdown:hover {
  background: var(--vscode-dropdown-listBackground);
  border-color: var(--vscode-focusBorder);
}

.sleek-mode-dropdown:focus,
.sleek-model-dropdown:focus {
  border-color: var(--vscode-focusBorder);
  box-shadow: 0 0 0 1px var(--vscode-focusBorder);
}

/* Model specific styling */
.sleek-model-dropdown {
  min-width: 140px;
}

/* Loading and Empty States */
.sleek-model-loading,
.sleek-model-empty {
  background: var(--vscode-dropdown-background);
  color: var(--vscode-descriptionForeground);
  border: 1px solid var(--vscode-dropdown-border);
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  font-family: var(--vscode-font-family);
  min-width: 140px;
  text-align: center;
}

/* @mention Suggestions */
.sleek-mention-suggestions {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: var(--vscode-quickInput-background);
  border: 1px solid var(--vscode-quickInput-border);
  border-radius: 8px;
  padding: 8px 12px;
  margin-bottom: 4px;
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

/* Reference Pills */
.reference-pill {
  display: inline-block;
  background: var(--vscode-badge-background);
  color: var(--vscode-badge-foreground);
  border-radius: 12px;
  padding: 2px 8px;
  margin: 0 2px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  user-select: none;
  vertical-align: baseline;
  line-height: 16px;
}

.reference-pill:hover {
  background: var(--vscode-button-hoverBackground);
}

/* Scrollbar Styling */
.sleek-input-field::-webkit-scrollbar {
  width: 4px;
}

.sleek-input-field::-webkit-scrollbar-track {
  background: transparent;
}

.sleek-input-field::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-background);
  border-radius: 2px;
}

.sleek-input-field::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground);
}

/* Responsive Design */
@media (max-width: 600px) {
  .sleek-input-container {
    padding: 10px 12px;
    gap: 8px;
  }

  .sleek-send-button {
    width: 28px;
    height: 28px;
  }

  .sleek-controls-left {
    gap: 6px;
  }

  .sleek-mode-dropdown,
  .sleek-model-dropdown {
    font-size: 11px;
    padding: 3px 6px;
    min-width: 70px;
  }

  .sleek-model-dropdown {
    min-width: 120px;
  }
}

/* Dark theme adjustments */
.vscode-dark .sleek-input-container {
  background: var(--vscode-input-background);
  border-color: var(--vscode-input-border);
}

.vscode-dark .sleek-send-button {
  background: var(--vscode-button-background);
}

.vscode-dark .sleek-send-button:hover:not(:disabled) {
  background: var(--vscode-button-hoverBackground);
}

/* High contrast theme adjustments */
.vscode-high-contrast .sleek-input-container {
  border-width: 2px;
}

.vscode-high-contrast .sleek-send-button {
  border: 1px solid var(--vscode-contrastBorder);
}

.vscode-high-contrast .sleek-mode-dropdown,
.vscode-high-contrast .sleek-model-dropdown {
  border-width: 2px;
}
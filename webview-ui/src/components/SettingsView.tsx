import React, { useState, useEffect } from 'react';
import { AIProviders, Models } from '../../../src/ai/types';
import { useModelData } from '../hooks/useModelData';
import { groupModelsByProvider } from '../utils/modelUtils';

// VS Code API types
declare global {
  interface Window {
    acquireVsCodeApi?: () => {
      postMessage: (message: any) => void;
      getState: () => any;
      setState: (state: any) => void;
    };
    __vscodeApi?: {
      postMessage: (message: any) => void;
      getState: () => any;
      setState: (state: any) => void;
    };
  }
}

// Helper function to get VS Code API
const getVsCodeApi = () => {
  return window.__vscodeApi;
};

// Add logging utility - sends to VS Code output channel
const log = (level: string, message: string, data?: any) => {
  console.log(`[SettingsView] ${level}: ${message}`, data || '');

  // Send log to extension for proper VS Code logging
  const vscode = getVsCodeApi();
  if (vscode) {
    try {
      vscode.postMessage({
        type: 'log',
        level,
        message: `SettingsView: ${message}`,
        data
      });
    } catch (error) {
      console.error('Failed to send log to extension:', error);
    }
  }
};

// Add error reporting utility - uses VS Code error notifications
const reportError = (message: string, error?: any) => {
  console.error(`[SettingsView] Error: ${message}`, error);

  const vscode = getVsCodeApi();
  if (vscode) {
    try {
      vscode.postMessage({
        type: 'error',
        message: `${message}${error ? ': ' + (error.message || error) : ''}`
      });
    } catch (e) {
      console.error('Failed to report error to extension:', e);
    }
  }
};

const PROVIDERS = [
  { id: AIProviders.AwsBedrock, name: 'AWS Bedrock', description: 'Amazon Bedrock Claude models' },
  { id: AIProviders.OpenAI, name: 'OpenAI', description: 'GPT models from OpenAI' },
  { id: AIProviders.AzureOpenAI, name: 'Azure OpenAI', description: 'GPT models via Azure OpenAI Service' },
  { id: AIProviders.GoogleAI, name: 'Google AI', description: 'Gemini models from Google' },
  { id: AIProviders.VertexAI, name: 'Vertex AI', description: 'Claude and Gemini models via Google Cloud Vertex AI' },
  { id: AIProviders.OpenRouter, name: 'OpenRouter', description: 'Multiple AI models via OpenRouter API' },
];


type SettingsSection = 'model' | 'autocompletion' | 'autocompact' | 'streaming' | 'general';

const SettingsView: React.FC = () => {
  const { availableModels, getDisplayName, getModelsForProvider } = useModelData();
  
  const [selectedProvider, setSelectedProvider] = useState<AIProviders>(AIProviders.AwsBedrock);
  const [selectedModel, setSelectedModel] = useState<Models>(() => {
    const defaultProvider = AIProviders.AwsBedrock;
    return availableModels.find(m => m.provider === defaultProvider)?.id as Models || Models.Claude_3_7_Sonnet;
  });
  const [awsAccessKeyId, setAwsAccessKeyId] = useState('');
  const [awsSecretAccessKey, setAwsSecretAccessKey] = useState('');
  const [awsRegion, setAwsRegion] = useState('us-east-1');
  const [openaiApiKey, setOpenaiApiKey] = useState('');
  // Azure OpenAI state
  const [azureApiKey, setAzureApiKey] = useState('');
  const [azureEndpoint, setAzureEndpoint] = useState('');
  const [azureApiVersion, setAzureApiVersion] = useState('2024-02-15-preview');
  const [azureDeploymentName, setAzureDeploymentName] = useState('');
  const [googleApiKey, setGoogleApiKey] = useState('');
  // Vertex AI state
  const [vertexProjectId, setVertexProjectId] = useState('');
  const [vertexRegion, setVertexRegion] = useState('us-east5');
  // OpenRouter state
  const [openRouterApiKey, setOpenRouterApiKey] = useState('');
  const [openRouterProviderSorting, setOpenRouterProviderSorting] = useState('');
  const [autoCompletionEnabled, setAutoCompletionEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);
  
  // Sidebar navigation state
  const [activeSection, setActiveSection] = useState<SettingsSection>('model');
  
  // Cost display settings
  const [showAggregateCost, setShowAggregateCost] = useState(true);
  const [showCostPerMessage, setShowCostPerMessage] = useState(false);
  
  // Auto Compact settings
  const [autoCompactEnabled, setAutoCompactEnabled] = useState(true);
  const [autoCompactThreshold, setAutoCompactThreshold] = useState(0.95);
  const [maxContextTokens, setMaxContextTokens] = useState(200000);

  // Streaming timeout settings
  const [streamingTimeoutEnabled, setStreamingTimeoutEnabled] = useState(true);
  const [streamingTimeoutMinutes, setStreamingTimeoutMinutes] = useState(2);

  // Add state to track if settings have been changed
  const [hasChanges, setHasChanges] = useState(false);
  const [originalSettings, setOriginalSettings] = useState<any>(null);

  // Helper function to check if current settings differ from original
  const checkForChanges = () => {
    if (!originalSettings) return false;

    const currentSettings = {
      selectedProvider,
      selectedModel,
      awsAccessKeyId,
      awsSecretAccessKey,
      awsRegion,
      openaiApiKey,
      googleApiKey,
      vertexProjectId,
      vertexRegion,
      openRouterApiKey,
      openRouterProviderSorting,
      autoCompletionEnabled,
      showAggregateCost,
      showCostPerMessage,
      autoCompactEnabled,
      autoCompactThreshold,
      maxContextTokens,
      streamingTimeoutEnabled,
      streamingTimeoutMinutes,
    };

    return JSON.stringify(currentSettings) !== JSON.stringify(originalSettings);
  };

  // Update hasChanges whenever settings change
  useEffect(() => {
    if (originalSettings) {
      setHasChanges(checkForChanges());
    }
  }, [selectedProvider, selectedModel, awsAccessKeyId, awsSecretAccessKey, awsRegion, openaiApiKey, googleApiKey, vertexProjectId, vertexRegion, openRouterApiKey, openRouterProviderSorting, autoCompletionEnabled, showAggregateCost, showCostPerMessage, autoCompactEnabled, autoCompactThreshold, maxContextTokens, streamingTimeoutEnabled, streamingTimeoutMinutes, originalSettings]);

  useEffect(() => {
    log('INFO', 'SettingsView component mounted');

    // Get VS Code API from global
    const vscode = getVsCodeApi();
    if (!vscode) {
      reportError('VS Code API not available');
      setIsLoading(false);
      return;
    }

    log('INFO', 'VS Code API available, setting up message handler');

    const handleMessage = (event: MessageEvent) => {
      log('INFO', `Received message: ${event.data.type}`);

      try {
        switch (event.data.type) {
          case 'settingsLoaded':
            log('INFO', 'Settings loaded from extension');
            const settings = event.data.settings;
            setSelectedProvider(settings.selectedProvider || AIProviders.AwsBedrock);
            setSelectedModel(settings.selectedModel || Models.Claude_3_7_Sonnet);

            // Set provider-specific settings
            const awsConfig = settings.providers?.[AIProviders.AwsBedrock];
            const openaiConfig = settings.providers?.[AIProviders.OpenAI];
            const googleConfig = settings.providers?.[AIProviders.GoogleAI];
            const vertexConfig = settings.providers?.[AIProviders.VertexAI];
            const openRouterConfig = settings.providers?.[AIProviders.OpenRouter];

            const awsAccessKey = awsConfig?.accessKeyId || '';
            const awsSecretKey = awsConfig?.secretAccessKey || '';
            const awsRegionValue = awsConfig?.region || 'us-east-1';
            const openaiKey = openaiConfig?.apiKey || '';
            const googleKey = googleConfig?.apiKey || '';
            const vertexProject = vertexConfig?.vertexProjectId || '';
            const vertexRegionValue = vertexConfig?.vertexRegion || 'us-east5';
            const openRouterKey = openRouterConfig?.openRouterApiKey || '';
            const openRouterSorting = openRouterConfig?.openRouterProviderSorting || '';

            setAwsAccessKeyId(awsAccessKey);
            setAwsSecretAccessKey(awsSecretKey);
            setAwsRegion(awsRegionValue);
            setOpenaiApiKey(openaiKey);
            setGoogleApiKey(googleKey);
            setVertexProjectId(vertexProject);
            setVertexRegion(vertexRegionValue);
            setOpenRouterApiKey(openRouterKey);
            setOpenRouterProviderSorting(openRouterSorting);
            setAutoCompletionEnabled(settings.autoCompletionEnabled || false);
            
            // Load cost display settings
            setShowAggregateCost(settings.showAggregateCost !== undefined ? settings.showAggregateCost : true);
            setShowCostPerMessage(settings.showCostPerMessage !== undefined ? settings.showCostPerMessage : false);
            
            // Load auto-compact settings
            const autoCompactConfig = settings.autoCompact || {};
            setAutoCompactEnabled(autoCompactConfig.enabled !== undefined ? autoCompactConfig.enabled : true);
            setAutoCompactThreshold(autoCompactConfig.threshold || 0.95);
            setMaxContextTokens(autoCompactConfig.maxContextTokens || 200000);

            // Load streaming timeout settings
            const streamingTimeoutConfig = settings.streamingTimeout || {};
            setStreamingTimeoutEnabled(streamingTimeoutConfig.enabled !== undefined ? streamingTimeoutConfig.enabled : true);
            setStreamingTimeoutMinutes(streamingTimeoutConfig.timeoutMinutes || 2);
            const azureConfig = settings.providers[AIProviders.AzureOpenAI];
            if (azureConfig) {
              setAzureApiKey(azureConfig.apiKey || '');
              setAzureEndpoint(azureConfig.azureEndpoint || '');
              setAzureApiVersion(azureConfig.azureApiVersion || '2024-02-15-preview');
              setAzureDeploymentName(azureConfig.azureDeploymentName || '');
            }


            // Store original settings for change tracking
            setOriginalSettings({
              selectedProvider: settings.selectedProvider || AIProviders.AwsBedrock,
              selectedModel: settings.selectedModel || Models.Claude_3_7_Sonnet,
              awsAccessKeyId: awsAccessKey,
              awsSecretAccessKey: awsSecretKey,
              awsRegion: awsRegionValue,
              openaiApiKey: openaiKey,
              googleApiKey: googleKey,
              vertexProjectId: vertexProject,
              vertexRegion: vertexRegionValue,
              openRouterApiKey: openRouterKey,
              openRouterProviderSorting: openRouterSorting,
              autoCompletionEnabled: settings.autoCompletionEnabled || false,
              showAggregateCost: settings.showAggregateCost !== undefined ? settings.showAggregateCost : true,
              showCostPerMessage: settings.showCostPerMessage !== undefined ? settings.showCostPerMessage : false,
              autoCompactEnabled: autoCompactConfig.enabled !== undefined ? autoCompactConfig.enabled : true,
              autoCompactThreshold: autoCompactConfig.threshold || 0.95,
              maxContextTokens: autoCompactConfig.maxContextTokens || 200000,
              streamingTimeoutEnabled: streamingTimeoutConfig.enabled !== undefined ? streamingTimeoutConfig.enabled : true,
              streamingTimeoutMinutes: streamingTimeoutConfig.timeoutMinutes || 2,
            });

            setHasChanges(false);
            setIsLoading(false);
            log('INFO', 'Settings loaded and applied successfully');
            break;

          case 'settingsUpdated':
            log('INFO', 'Settings update confirmed');
            setIsLoading(false);
            setMessage({ type: 'success', text: 'Settings saved successfully!' });

            // Update original settings to current values and reset hasChanges
            setOriginalSettings({
              selectedProvider,
              selectedModel,
              awsAccessKeyId,
              awsSecretAccessKey,
              awsRegion,
              openaiApiKey,
              googleApiKey,
              autoCompletionEnabled,
              showAggregateCost,
              showCostPerMessage,
            });
            setHasChanges(false);

            // Clear message after 3 seconds
            setTimeout(() => setMessage(null), 3000);
            break;

          case 'awsCredentialsValidated':
            log('INFO', `AWS credentials validation result: ${event.data.isValid}`);
            setIsLoading(false);
            setMessage({
              type: event.data.isValid ? 'success' : 'error',
              text: event.data.isValid ? 'AWS credentials are valid!' : 'AWS credentials are invalid!'
            });
            // Clear message after 3 seconds
            setTimeout(() => setMessage(null), 3000);
            break;

          case 'error':
            log('ERROR', `Extension reported error: ${event.data.message}`);
            setIsLoading(false);
            setMessage({ type: 'error', text: event.data.message });
            // Clear message after 5 seconds for errors
            setTimeout(() => setMessage(null), 5000);
            break;

          default:
            log('WARN', `Unknown message type: ${event.data.type}`);
        }
      } catch (error) {
        reportError('Error handling message from extension', error);
        setIsLoading(false);
        setMessage({ type: 'error', text: 'Error processing message from extension' });
        setTimeout(() => setMessage(null), 5000);
      }
    };

    window.addEventListener('message', handleMessage);

    // Request initial settings
    log('INFO', 'Requesting initial settings');
    vscode.postMessage({ type: 'getSettings' });

    return () => {
      log('INFO', 'Cleaning up SettingsView component');
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  const handleSaveSettings = () => {
    log('INFO', 'Save settings requested');

    const vscode = getVsCodeApi();
    if (!vscode) {
      reportError('VS Code API not available for saving settings');
      return;
    }

    setIsLoading(true);
    setMessage(null);

    const settings = {
      selectedProvider,
      selectedModel,
      autoCompletionEnabled,
      showAggregateCost,
      showCostPerMessage,
      autoCompact: {
        enabled: autoCompactEnabled,
        threshold: autoCompactThreshold,
        maxContextTokens: maxContextTokens,
      },
      streamingTimeout: {
        enabled: streamingTimeoutEnabled,
        timeoutMinutes: streamingTimeoutMinutes,
      },
      providers: {
        [AIProviders.AwsBedrock]: {
          provider: AIProviders.AwsBedrock,
          accessKeyId: awsAccessKeyId,
          secretAccessKey: awsSecretAccessKey,
          region: awsRegion,
        },
        [AIProviders.OpenAI]: {
          provider: AIProviders.OpenAI,
          apiKey: openaiApiKey,
        },
        [AIProviders.AzureOpenAI]: {
          provider: AIProviders.AzureOpenAI,
          apiKey: azureApiKey,
          azureEndpoint: azureEndpoint,
          azureApiVersion: azureApiVersion,
          azureDeploymentName: azureDeploymentName,
        },
        [AIProviders.GoogleAI]: {
          provider: AIProviders.GoogleAI,
          apiKey: googleApiKey,
        },
        [AIProviders.VertexAI]: {
          provider: AIProviders.VertexAI,
          vertexProjectId: vertexProjectId,
          vertexRegion: vertexRegion,
        },
        [AIProviders.OpenRouter]: {
          provider: AIProviders.OpenRouter,
          openRouterApiKey: openRouterApiKey,
          openRouterProviderSorting: openRouterProviderSorting,
        },
      },
    };

    log('INFO', 'Sending settings update');
    vscode.postMessage({ type: 'updateSettings', settings });
  };

  const handleValidateAwsCredentials = () => {
    log('INFO', 'AWS credentials validation requested');

    const vscode = getVsCodeApi();
    if (!vscode) {
      reportError('VS Code API not available for AWS validation');
      return;
    }

    setIsLoading(true);
    setMessage(null);

    log('INFO', 'Sending AWS credentials for validation');
    vscode.postMessage({
      type: 'validateAwsCredentials',
      accessKeyId: awsAccessKeyId,
      secretAccessKey: awsSecretAccessKey,
      region: awsRegion,
    });
  };

  const handleClearSettings = () => {
    log('INFO', 'Clear settings requested');

    const vscode = getVsCodeApi();
    if (!vscode) {
      reportError('VS Code API not available for clearing settings');
      return;
    }

    if (confirm('Are you sure you want to clear all settings? This action cannot be undone.')) {
      log('INFO', 'Sending clear settings request');
      vscode.postMessage({ type: 'clearSettings' });
    }
  };

  const getAvailableModels = (): Models[] => {
    const providerModels = getModelsForProvider(selectedProvider);
    return providerModels.map(model => model.id as Models);
  };

  const handleProviderChange = (newProvider: AIProviders) => {
    log('INFO', `Provider changed from ${selectedProvider} to ${newProvider}`);
    setSelectedProvider(newProvider);

    const providerModels = getModelsForProvider(newProvider);
    if (providerModels && providerModels.length > 0) {
      const newModel = providerModels[0].id as Models;
      log('INFO', `Auto-updating model to ${newModel} for provider ${newProvider}`);
      setSelectedModel(newModel);
    }
  };

  if (isLoading) {
    return (
      <div className="settings-loading">
        <div>Loading settings...</div>
      </div>
    );
  }

  const sidebarItems = [
    { id: 'model' as SettingsSection, label: 'Model Settings', icon: '🤖' },
    { id: 'autocompletion' as SettingsSection, label: 'Auto Completion Settings', icon: '⚡' },
    { id: 'autocompact' as SettingsSection, label: 'Auto Compact', icon: '📊' },
    { id: 'streaming' as SettingsSection, label: 'Streaming Timeout', icon: '⏱️' },
    { id: 'general' as SettingsSection, label: 'General Settings', icon: '⚙️' },
  ];

  const renderModelSettings = () => (
    <>
      {/* Provider Selection */}
      <div className="settings-section">
        <h2>AI Provider</h2>
        <p className="section-description">Choose your preferred AI provider</p>
        <div className="provider-options">
          {PROVIDERS.map(provider => (
            <label key={provider.id} className="provider-option">
              <input
                type="radio"
                name="provider"
                value={provider.id}
                checked={selectedProvider === provider.id}
                onChange={(e) => handleProviderChange(e.target.value as AIProviders)}
              />
              <div className="provider-info">
                <div className="provider-name">{provider.name}</div>
                <div className="provider-description">{provider.description}</div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Model Selection */}
      <div className="settings-section">
        <h2>Model</h2>
        <p className="section-description">Select the AI model to use</p>
        <div className="model-selection">
          <select
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value as Models)}
            className="model-select"
          >
            {getAvailableModels().map(model => (
              <option key={model} value={model}>
                {getDisplayName(model)}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Provider-specific Configuration */}
      {selectedProvider === AIProviders.AwsBedrock && (
        <div className="settings-section">
          <h2>AWS Configuration</h2>
          <p className="section-description">Configure your AWS Bedrock credentials</p>
          <div className="form-fields">
            <div className="form-field">
              <label htmlFor="aws-access-key">Access Key ID</label>
              <input
                id="aws-access-key"
                type="password"
                value={awsAccessKeyId}
                onChange={(e) => setAwsAccessKeyId(e.target.value)}
                placeholder="Enter your AWS Access Key ID"
                className="form-input"
              />
            </div>
            <div className="form-field">
              <label htmlFor="aws-secret-key">Secret Access Key</label>
              <input
                id="aws-secret-key"
                type="password"
                value={awsSecretAccessKey}
                onChange={(e) => setAwsSecretAccessKey(e.target.value)}
                placeholder="Enter your AWS Secret Access Key"
                className="form-input"
              />
            </div>
            <div className="form-field">
              <label htmlFor="aws-region">Region</label>
              <select
                id="aws-region"
                value={awsRegion}
                onChange={(e) => setAwsRegion(e.target.value)}
                className="form-select"
              >
                <option value="us-east-1">US East (N. Virginia)</option>
                <option value="us-west-2">US West (Oregon)</option>
                <option value="eu-west-1">Europe (Ireland)</option>
                <option value="ap-southeast-1">Asia Pacific (Singapore)</option>
              </select>
            </div>
            <button
              onClick={handleValidateAwsCredentials}
              disabled={isLoading}
              className="btn btn-secondary"
            >
              {isLoading ? 'Validating...' : 'Validate Credentials'}
            </button>
          </div>
        </div>
      )}

      {selectedProvider === AIProviders.OpenAI && (
        <div className="settings-section">
          <h2>OpenAI Configuration</h2>
          <p className="section-description">Configure your OpenAI API access</p>
          <div className="form-fields">
            <div className="form-field">
              <label htmlFor="openai-api-key">API Key</label>
              <input
                id="openai-api-key"
                type="password"
                value={openaiApiKey}
                onChange={(e) => setOpenaiApiKey(e.target.value)}
                placeholder="Enter your OpenAI API Key"
                className="form-input"
              />
              <p className="field-help">
                Get your API key from{' '}
                <a
                  href="https://platform.openai.com/api-keys"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  OpenAI Platform
                </a>
              </p>
            </div>
          </div>
        </div>
      )}

      {selectedProvider === AIProviders.AzureOpenAI && (
        <div className="settings-section">
          <h2>Azure OpenAI Configuration</h2>
          <p className="section-description">Configure your Azure OpenAI Service access</p>
          <div className="form-fields">
            <div className="form-field">
              <label htmlFor="azure-api-key">API Key</label>
              <input
                id="azure-api-key"
                type="password"
                value={azureApiKey}
                onChange={(e) => setAzureApiKey(e.target.value)}
                placeholder="Enter your Azure OpenAI API Key"
                className="form-input"
              />
            </div>

            <div className="form-field">
              <label htmlFor="azure-endpoint">Endpoint</label>
              <input
                id="azure-endpoint"
                type="text"
                value={azureEndpoint}
                onChange={(e) => setAzureEndpoint(e.target.value)}
                placeholder="https://your-resource.openai.azure.com/"
                className="form-input"
              />
            </div>

            <div className="form-field">
              <label htmlFor="azure-deployment">Deployment Name</label>
              <input
                id="azure-deployment"
                type="text"
                value={azureDeploymentName}
                onChange={(e) => setAzureDeploymentName(e.target.value)}
                placeholder="Enter your model deployment name"
                className="form-input"
              />
            </div>

            <div className="form-field">
              <label htmlFor="azure-api-version">API Version (Optional)</label>
              <input
                id="azure-api-version"
                type="text"
                value={azureApiVersion}
                onChange={(e) => setAzureApiVersion(e.target.value)}
                placeholder="2024-02-15-preview"
                className="form-input"
              />
            </div>

            <div className="form-field">
              <p className="field-help">
                Configure your Azure OpenAI resource from the{' '}
                <a
                  href="https://portal.azure.com"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Azure Portal
                </a>
              </p>
            </div>
          </div>
        </div>
      )}

      {selectedProvider === AIProviders.GoogleAI && (
        <div className="settings-section">
          <h2>Google AI Configuration</h2>
          <p className="section-description">Configure your Google AI API access</p>
          <div className="form-fields">
            <div className="form-field">
              <label htmlFor="google-api-key">API Key</label>
              <input
                id="google-api-key"
                type="password"
                value={googleApiKey}
                onChange={(e) => setGoogleApiKey(e.target.value)}
                placeholder="Enter your Google AI API Key"
                className="form-input"
              />
              <p className="field-help">
                Get your API key from{' '}
                <a
                  href="https://makersuite.google.com/app/apikey"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Google AI Studio
                </a>
              </p>
            </div>
          </div>
        </div>
      )}

      {selectedProvider === AIProviders.VertexAI && (
        <div className="settings-section">
          <h2>Vertex AI Configuration</h2>
          <p className="section-description">Configure your Google Cloud Vertex AI access</p>
          <div className="form-fields">
            <div className="form-field">
              <label htmlFor="vertex-project-id">Project ID</label>
              <input
                id="vertex-project-id"
                type="text"
                value={vertexProjectId}
                onChange={(e) => setVertexProjectId(e.target.value)}
                placeholder="Enter your Google Cloud Project ID"
                className="form-input"
              />
              <p className="field-help">
                Your Google Cloud project ID (e.g., my-project-123)
              </p>
            </div>
            <div className="form-field">
              <label htmlFor="vertex-region">Region</label>
              <select
                id="vertex-region"
                value={vertexRegion}
                onChange={(e) => setVertexRegion(e.target.value)}
                className="form-select"
              >
                <option value="">Select a region...</option>
                <option value="us-east5">us-east5</option>
                <option value="us-central1">us-central1</option>
                <option value="europe-west1">europe-west1</option>
                <option value="europe-west4">europe-west4</option>
                <option value="asia-southeast1">asia-southeast1</option>
                <option value="global">global</option>
              </select>
            </div>
            <div className="form-field">
              <p className="field-help">
                Authentication uses Google Cloud Application Default Credentials (ADC). 
                Run <code>gcloud auth application-default login</code> to authenticate.{' '}
                <a
                  href="https://cloud.google.com/vertex-ai?hl=en"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Learn more
                </a>
              </p>
            </div>
          </div>
        </div>
      )}

      {selectedProvider === AIProviders.OpenRouter && (
        <div className="settings-section">
          <h2>OpenRouter Configuration</h2>
          <p className="section-description">Configure your OpenRouter API access</p>
          <div className="form-fields">
            <div className="form-field">
              <label htmlFor="openrouter-api-key">API Key</label>
              <input
                id="openrouter-api-key"
                type="password"
                value={openRouterApiKey}
                onChange={(e) => setOpenRouterApiKey(e.target.value)}
                placeholder="Enter your OpenRouter API Key"
                className="form-input"
              />
              <p className="field-help">
                Get your API key from{' '}
                <a
                  href="https://openrouter.ai/keys"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  OpenRouter Dashboard
                </a>
              </p>
            </div>
            <div className="form-field">
              <label htmlFor="openrouter-provider-sorting">Provider Sorting (Optional)</label>
              <input
                id="openrouter-provider-sorting"
                type="text"
                value={openRouterProviderSorting}
                onChange={(e) => setOpenRouterProviderSorting(e.target.value)}
                placeholder="e.g., OpenAI,Anthropic,Google"
                className="form-input"
              />
              <p className="field-help">
                Comma-separated list of preferred providers for model routing
              </p>
            </div>
          </div>
        </div>
      )}
    </>
  );

  const renderAutoCompletionSettings = () => (
    <div className="settings-section">
      <h2>Auto Completion</h2>
      <p className="section-description">Configure VS Code auto completion settings</p>
      <div className="form-fields">
        <div className="form-field">
          <label className="toggle-label">
            <input
              type="checkbox"
              checked={autoCompletionEnabled}
              onChange={(e) => setAutoCompletionEnabled(e.target.checked)}
              className="toggle-checkbox"
            />
            <span className="toggle-text">Enable Auto Completion</span>
          </label>
          <p className="field-help">
            This will automatically configure VS Code with optimal auto completion settings including inline suggestions, quick suggestions, and suggestion previews.
          </p>
        </div>
      </div>
    </div>
  );

  const renderAutoCompactSettings = () => (
    <div className="settings-section">
      <h2>Auto Compact</h2>
      <p className="section-description">Configure automatic conversation summarization to manage context window limits</p>
      <div className="form-fields">
        <div className="form-field">
          <label className="toggle-label">
            <input
              type="checkbox"
              checked={autoCompactEnabled}
              onChange={(e) => setAutoCompactEnabled(e.target.checked)}
              className="toggle-checkbox"
            />
            <span className="toggle-text">Enable Auto Compact</span>
          </label>
          <p className="field-help">
            Automatically summarize long conversations when they approach the context window limit to maintain unlimited conversation length.
          </p>
        </div>
        
        {autoCompactEnabled && (
          <>
            <div className="form-field">
              <label className="form-label">Context Window Threshold</label>
              <div className="input-with-unit">
                <input
                  type="range"
                  min="0.8"
                  max="0.98"
                  step="0.01"
                  value={autoCompactThreshold}
                  onChange={(e) => setAutoCompactThreshold(parseFloat(e.target.value))}
                  className="range-input"
                />
                <span className="input-unit">{Math.round(autoCompactThreshold * 100)}%</span>
              </div>
              <p className="field-help">
                Trigger auto-compact when conversation reaches this percentage of the context window ({Math.round(autoCompactThreshold * 100)}% of {maxContextTokens.toLocaleString()} tokens = {Math.round(autoCompactThreshold * maxContextTokens).toLocaleString()} tokens).
              </p>
            </div>
            
            <div className="form-field">
              <label className="form-label">Max Context Tokens</label>
              <input
                type="number"
                min="50000"
                max="2000000"
                step="10000"
                value={maxContextTokens}
                onChange={(e) => setMaxContextTokens(parseInt(e.target.value) || 200000)}
                className="form-input"
                placeholder="200000"
              />
              <p className="field-help">
                Maximum context window size in tokens. Default is 200,000 tokens for most modern models.
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  );

  const renderStreamingTimeoutSettings = () => (
    <div className="settings-section">
      <h2>Streaming Timeout</h2>
      <p className="section-description">Configure timeout behavior for AI response streaming to prevent infinite loops</p>
      <div className="form-fields">
        <div className="form-field">
          <label className="toggle-label">
            <input
              type="checkbox"
              checked={streamingTimeoutEnabled}
              onChange={(e) => setStreamingTimeoutEnabled(e.target.checked)}
              className="toggle-checkbox"
            />
            <span className="toggle-text">Enable Streaming Timeout</span>
          </label>
          <p className="field-help">
            Automatically pause AI processing after a specified duration to prevent excessive resource usage and give you control.
          </p>
        </div>
        
        {streamingTimeoutEnabled && (
          <div className="form-field">
            <label className="form-label">Timeout Duration (minutes)</label>
            <input
              type="number"
              min="1"
              max="30"
              step="1"
              value={streamingTimeoutMinutes}
              onChange={(e) => setStreamingTimeoutMinutes(parseInt(e.target.value) || 2)}
              className="form-input"
              placeholder="2"
            />
            <p className="field-help">
              AI processing will pause after {streamingTimeoutMinutes} minute{streamingTimeoutMinutes === 1 ? '' : 's'} and show a "Continue" button. Recommended: 2-5 minutes.
            </p>
          </div>
        )}
      </div>
    </div>
  );

  const renderGeneralSettings = () => (
    <>
      <div className="settings-section">
        <h2>Cost Display Options</h2>
        <p className="section-description">Configure how cost information is displayed in the chat</p>
        <div className="form-fields">
          <div className="form-field">
            <label className="toggle-label">
              <input
                type="checkbox"
                checked={showAggregateCost}
                onChange={(e) => setShowAggregateCost(e.target.checked)}
                className="toggle-checkbox"
              />
              <span className="toggle-text">Show Aggregate Cost</span>
            </label>
            <p className="field-help">
              Display the total accumulated cost at the top of the chat window.
            </p>
          </div>
          
          <div className="form-field">
            <label className="toggle-label">
              <input
                type="checkbox"
                checked={showCostPerMessage}
                onChange={(e) => setShowCostPerMessage(e.target.checked)}
                className="toggle-checkbox"
              />
              <span className="toggle-text">Show Cost per Message</span>
            </label>
            <p className="field-help">
              Display the individual cost for each AI response message.
            </p>
          </div>
        </div>
      </div>
    </>
  );

  const renderActiveSection = () => {
    switch (activeSection) {
      case 'model':
        return renderModelSettings();
      case 'autocompletion':
        return renderAutoCompletionSettings();
      case 'autocompact':
        return renderAutoCompactSettings();
      case 'streaming':
        return renderStreamingTimeoutSettings();
      case 'general':
        return renderGeneralSettings();
      default:
        return renderModelSettings();
    }
  };

  return (
    <div className="settings-container">
      <div className="settings-sidebar">
        <div className="settings-sidebar-header">
          <h1>Settings</h1>
        </div>
        <nav className="settings-sidebar-nav">
          {sidebarItems.map(item => (
            <button
              key={item.id}
              onClick={() => setActiveSection(item.id)}
              className={`settings-sidebar-item ${activeSection === item.id ? 'active' : ''}`}
            >
              <span className="sidebar-item-icon">{item.icon}</span>
              <span className="sidebar-item-label">{item.label}</span>
            </button>
          ))}
        </nav>
      </div>

      <div className="settings-main">
        {/* Status Message */}
        {message && (
          <div className={`settings-message ${message.type}`}>
            {message.text}
          </div>
        )}

        <div className="settings-content">
          {renderActiveSection()}
        </div>

        {/* Action Buttons */}
        <div className="settings-actions">
          <button
            onClick={handleClearSettings}
            className="btn btn-danger-subtle"
            disabled={!hasChanges && !originalSettings}
          >
            Clear All Settings
          </button>
          <button
            onClick={handleSaveSettings}
            disabled={isLoading || !hasChanges}
            className={`btn ${hasChanges ? 'btn-primary' : 'btn-disabled'}`}
          >
            {isLoading ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsView;
import React, { useState, useRef, useEffect } from 'react';
import { Copy, Check, Play, Terminal, FileText } from 'lucide-react';

interface CodeBlockProps {
  code: string;
  language?: string;
  filename?: string;
  showLineNumbers?: boolean;
  onRunCode?: (code: string, language: string) => void;
}

// Languages that can be executed
const EXECUTABLE_LANGUAGES = [
  'javascript', 'js', 'typescript', 'ts', 'python', 'py', 
  'bash', 'sh', 'shell', 'node', 'npm', 'yarn'
];

// Language display names and file extensions
const LANGUAGE_INFO: Record<string, { name: string; extension: string; icon?: string }> = {
  'javascript': { name: 'JavaScript', extension: '.js' },
  'js': { name: 'JavaScript', extension: '.js' },
  'typescript': { name: 'TypeScript', extension: '.ts' },
  'ts': { name: 'TypeScript', extension: '.ts' },
  'tsx': { name: 'TypeScript React', extension: '.tsx' },
  'jsx': { name: 'JavaScript React', extension: '.jsx' },
  'python': { name: 'Python', extension: '.py' },
  'py': { name: 'Python', extension: '.py' },
  'bash': { name: 'Bash', extension: '.sh' },
  'sh': { name: 'Shell', extension: '.sh' },
  'shell': { name: 'Shell', extension: '.sh' },
  'json': { name: 'JSON', extension: '.json' },
  'yaml': { name: 'YAML', extension: '.yaml' },
  'yml': { name: 'YAML', extension: '.yml' },
  'css': { name: 'CSS', extension: '.css' },
  'scss': { name: 'SCSS', extension: '.scss' },
  'html': { name: 'HTML', extension: '.html' },
  'xml': { name: 'XML', extension: '.xml' },
  'sql': { name: 'SQL', extension: '.sql' },
  'markdown': { name: 'Markdown', extension: '.md' },
  'md': { name: 'Markdown', extension: '.md' },
  'mermaid': { name: 'Mermaid Diagram', extension: '.mmd' },
  'plaintext': { name: 'Plain Text', extension: '.txt' },
  'text': { name: 'Plain Text', extension: '.txt' },
};

const getLanguageInfo = (language?: string) => {
  if (!language) return { name: 'Code', extension: '' };
  const normalizedLang = language.toLowerCase();
  return LANGUAGE_INFO[normalizedLang] || { name: language, extension: '' };
};

const isExecutable = (language?: string): boolean => {
  if (!language) return false;
  return EXECUTABLE_LANGUAGES.includes(language.toLowerCase());
};

const CodeBlock: React.FC<CodeBlockProps> = ({
  code,
  language,
  filename,
  showLineNumbers = false,
  onRunCode
}) => {
  const [copied, setCopied] = useState(false);
  const [isExpanded, setIsExpanded] = useState(true);
  const codeRef = useRef<HTMLElement>(null);
  
  const languageInfo = getLanguageInfo(language);
  const canExecute = isExecutable(language) && onRunCode;
  const lines = code.split('\n');
  const shouldShowLineNumbers = showLineNumbers || lines.length > 10;

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  const handleRunCode = () => {
    if (canExecute && language) {
      onRunCode(code, language);
    }
  };

  const handleCreateFile = () => {
    // Send message to VS Code to create a new file with this content
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      const extension = languageInfo.extension || '.txt';
      const suggestedFilename = filename || `untitled${extension}`;
      
      vscodeApi.postMessage({
        type: 'createFile',
        content: code,
        filename: suggestedFilename,
        language: language
      });
    }
  };

  // Auto-expand if code is short, collapse if very long
  useEffect(() => {
    setIsExpanded(lines.length <= 20);
  }, [lines.length]);

  const displayLines = isExpanded ? lines : lines.slice(0, 5);
  const hasMore = lines.length > 5 && !isExpanded;

  return (
    <div className="code-block-container">
      {/* Code Block Header */}
      <div className="code-block-header">
        <div className="code-block-info">
          <div className="language-badge">
            {languageInfo.name}
          </div>
          {filename && (
            <div className="filename-badge">
              {filename}
            </div>
          )}
          <div className="code-stats">
            {lines.length} line{lines.length !== 1 ? 's' : ''}
          </div>
        </div>
        
        <div className="code-block-actions">
          {/* Copy Button */}
          <button
            onClick={handleCopyCode}
            className="code-action-button"
            title="Copy code"
          >
            {copied ? <Check size={14} /> : <Copy size={14} />}
          </button>
          
          {/* Create File Button */}
          <button
            onClick={handleCreateFile}
            className="code-action-button"
            title="Create file"
          >
            <FileText size={14} />
          </button>
          
          {/* Run Code Button (for executable languages) */}
          {canExecute && (
            <button
              onClick={handleRunCode}
              className="code-action-button run-button"
              title={`Run ${languageInfo.name}`}
            >
              {language === 'bash' || language === 'sh' || language === 'shell' ? 
                <Terminal size={14} /> : <Play size={14} />
              }
            </button>
          )}
        </div>
      </div>

      {/* Code Content */}
      <div className="code-block-content">
        <pre className="code-block-pre">
          <code ref={codeRef} className={`language-${language || 'text'}`}>
            {shouldShowLineNumbers ? (
              <table className="code-table">
                <tbody>
                  {displayLines.map((line, index) => (
                    <tr key={index} className="code-line">
                      <td className="line-number">
                        {index + 1}
                      </td>
                      <td className="line-content">
                        {line || '\u00A0'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              displayLines.join('\n')
            )}
          </code>
        </pre>
        
        {/* Expand/Collapse for long code */}
        {lines.length > 5 && (
          <div className="code-expand-section">
            <button 
              onClick={() => setIsExpanded(!isExpanded)}
              className="code-expand-button"
            >
              {isExpanded ? 
                `Collapse (${lines.length - 5} more lines)` : 
                `Show all ${lines.length} lines`
              }
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CodeBlock;
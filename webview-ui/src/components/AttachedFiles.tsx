import React, { useState } from 'react';
import { X, FileText, Image, File } from 'lucide-react';
import { AttachedFile } from '../types';
import ImageModal from './ImageModal';

interface AttachedFilesProps {
  files: AttachedFile[];
  onRemoveFile: (index: number) => void;
  onClearAll: () => void;
  compact?: boolean;
}

export const AttachedFiles: React.FC<AttachedFilesProps> = ({
  files,
  onRemoveFile,
  onClearAll,
  compact = false
}) => {
  const [selectedImage, setSelectedImage] = useState<{ url: string; name: string } | null>(null);
  
  const handleOpenInVSCode = (imageUrl: string, imageName: string) => {
    // Send message to extension to open image in VS Code
    const vscodeApi = window.vscode || window.__vscodeApi;
    if (vscodeApi) {
      vscodeApi.postMessage({
        type: 'openImageInVSCode',
        imageUrl: imageUrl,
        imageName: imageName
      });
    }
  };
  
  if (files.length === 0) return null;

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileName: string, fileType: string) => {
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.svg'];
    
    if (imageExtensions.includes(extension)) {
      return <Image size={16} />;
    } else if (fileType.startsWith('text/') || ['.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.h', '.css', '.scss', '.html', '.xml', '.json', '.yaml', '.yml', '.sql', '.sh', '.bat', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.dart', '.vue', '.svelte', '.purs', '.hs'].includes(extension)) {
      return <FileText size={16} />;
    } else {
      return <File size={16} />;
    }
  };

  const isImageFile = (fileName: string): boolean => {
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.svg'].includes(extension);
  };

  return (
    <div className={`attached-files ${compact ? 'attached-files-compact' : ''}`}>
      <div className="attached-files-header">
        <span className="attached-files-title">
          {files.length} file{files.length !== 1 ? 's' : ''} attached
        </span>
        {files.length > 1 && (
          <button 
            className="attached-files-clear" 
            onClick={onClearAll}
            title="Remove all files"
          >
            Clear all
          </button>
        )}
      </div>
      
      <div className="attached-files-list">
        {files.map((file, index) => (
          <div key={index} className="attached-file">
            <div className="attached-file-icon">
              {getFileIcon(file.name, file.type)}
            </div>
            
            <div className="attached-file-info">
              <div className="attached-file-name" title={file.name}>
                {file.name}
              </div>
              <div className="attached-file-details">
                {formatFileSize(file.size)}
                {isImageFile(file.name) && (
                  <span className="attached-file-type">Image</span>
                )}
              </div>
            </div>
            
            {isImageFile(file.name) && file.content.startsWith('data:image') && (
              <div className="attached-file-preview">
                <img 
                  src={file.content} 
                  alt={file.name}
                  className="attached-file-thumbnail"
                  onClick={() => setSelectedImage({ url: file.content, name: file.name })}
                  title={`Click to view ${file.name}`}
                />
              </div>
            )}
            
            <button
              className="attached-file-remove"
              onClick={() => onRemoveFile(index)}
              title={`Remove ${file.name}`}
            >
              <X size={14} />
            </button>
          </div>
        ))}
      </div>
      
      <ImageModal
        isOpen={!!selectedImage}
        imageUrl={selectedImage?.url || ''}
        imageName={selectedImage?.name || ''}
        onClose={() => setSelectedImage(null)}
        onOpenInVSCode={handleOpenInVSCode}
      />
    </div>
  );
};

export default AttachedFiles;
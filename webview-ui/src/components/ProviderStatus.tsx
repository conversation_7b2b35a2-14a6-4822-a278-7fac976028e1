import React from 'react';
import { <PERSON><PERSON><PERSON>, AlertCircle, CheckCircle, Clock, XCircle } from 'lucide-react';
import { ProviderStatusInfo } from '../types';
import { Models, AIProviders } from '../../../src/ai/types';
import { useModelData } from '../hooks/useModelData';
import { getProviderDisplayName } from '../utils/modelUtils';

interface ProviderStatusProps {
  provider: AIProviders;
  model: Models;
  status: ProviderStatusInfo;
  onOpenSettings?: () => void;
  onRetryConnection?: () => void;
}


const StatusDot: React.FC<{ status: string }> = ({ status }) => {
  const getStatusIcon = () => {
    switch (status) {
      case 'connected':
        return <CheckCircle size={12} className="status-icon connected" />;
      case 'configured':
        return <CheckCircle size={12} className="status-icon configured" />;
      case 'connecting':
        return <Clock size={12} className="status-icon connecting" />;
      case 'error':
        return <AlertCircle size={12} className="status-icon error" />;
      case 'disconnected':
      case 'not-configured':
        return <XCircle size={12} className="status-icon disconnected" />;
      default:
        return <XCircle size={12} className="status-icon disconnected" />;
    }
  };

  return (
    <div className={`status-dot ${status}`}>
      {getStatusIcon()}
    </div>
  );
};


const getStatusMessage = (statusType: string, message?: string): string => {
  if (message) return message;
  
  switch (statusType) {
    case 'connected':
      return 'Connected and ready';
    case 'configured':
      return 'Configured (not tested)';
    case 'connecting':
      return 'Connecting...';
    case 'error':
      return 'Connection error';
    case 'disconnected':
      return 'Disconnected';
    case 'not-configured':
      return 'Not configured';
    default:
      return 'Unknown status';
  }
};

const ProviderStatus: React.FC<ProviderStatusProps> = ({
  provider,
  model,
  status,
  onOpenSettings,
  onRetryConnection
}) => {
  const { getDisplayName } = useModelData();
  
  const statusMessage = getStatusMessage(status.status, status.message);
  const canRetry = status.status === 'error' || status.status === 'disconnected';

  return (
    <div className="provider-status-container">
      <div className="provider-status-main">
        <StatusDot status={status.status} />
        <div className="provider-status-info">
          <div className="provider-status-model">
            {getDisplayName(model)}
          </div>
          <div className="provider-status-provider">
            {getProviderDisplayName(provider)}
          </div>
        </div>
      </div>
      
      {(status.status === 'error' || status.status === 'not-configured') && (
        <div className="provider-status-message">
          <span className="status-message-text">{statusMessage}</span>
          <div className="status-actions">
            {canRetry && onRetryConnection && (
              <button 
                onClick={onRetryConnection}
                className="status-action-button retry"
                title="Retry connection"
              >
                Retry
              </button>
            )}
            {onOpenSettings && (
              <button 
                onClick={onOpenSettings}
                className="status-action-button settings"
                title="Open settings"
              >
                <Settings size={12} />
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProviderStatus;
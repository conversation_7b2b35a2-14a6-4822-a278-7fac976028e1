import React, { useState, useRef, useEffect } from 'react';
import { FileText, X, Save } from 'lucide-react';

interface CustomInstructionsProps {
  isOpen: boolean;
  onClose: () => void;
  currentInstructions: string;
  onSave: (instructions: string) => void;
}

export const CustomInstructions: React.FC<CustomInstructionsProps> = ({
  isOpen,
  onClose,
  currentInstructions,
  onSave
}) => {
  const [instructions, setInstructions] = useState(currentInstructions);
  const [hasChanges, setHasChanges] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    setInstructions(currentInstructions);
    setHasChanges(false);
  }, [currentInstructions, isOpen]);

  useEffect(() => {
    setHasChanges(instructions !== currentInstructions);
  }, [instructions, currentInstructions]);

  useEffect(() => {
    if (isOpen && textareaRef.current) {
      setTimeout(() => textareaRef.current?.focus(), 100);
    }
  }, [isOpen]);

  const handleSave = () => {
    onSave(instructions);
    onClose();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 's' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSave();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="search-overlay">
      <div className="search-panel" style={{ maxWidth: '600px' }}>
        {/* Header */}
        <div className="search-header">
          <div className="search-title">
            <FileText size={18} />
            <h3>Custom Instructions</h3>
          </div>
          <button className="search-close" onClick={onClose}>
            <X size={16} />
          </button>
        </div>

        {/* Content */}
        <div style={{ padding: 'var(--space-4)' }}>
          <p style={{ 
            margin: '0 0 var(--space-4) 0',
            color: 'var(--text-secondary)',
            fontSize: '0.875rem',
            lineHeight: '1.4'
          }}>
            Add custom instructions that will be included in every message for this conversation. 
            These help personalize the AI's behavior and responses to your preferences.
          </p>
          
          <textarea
            ref={textareaRef}
            value={instructions}
            onChange={(e) => setInstructions(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Enter your custom instructions here... For example:
• Always provide code examples when explaining concepts
• Be concise in your responses
• Focus on Python solutions when possible
• Explain technical concepts in simple terms"
            style={{
              width: '100%',
              minHeight: '200px',
              maxHeight: '400px',
              padding: 'var(--space-3)',
              background: 'var(--bg-tertiary)',
              border: '1px solid var(--border-secondary)',
              borderRadius: 'var(--radius-md)',
              color: 'var(--text-primary)',
              fontSize: '0.875rem',
              lineHeight: '1.4',
              resize: 'vertical',
              fontFamily: 'var(--font-sans)',
              boxSizing: 'border-box'
            }}
          />
          
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginTop: 'var(--space-3)',
            gap: 'var(--space-3)'
          }}>
            <p style={{
              margin: 0,
              fontSize: '0.75rem',
              color: 'var(--text-muted)',
              flex: 1
            }}>
              Press Ctrl/Cmd+S to save • Esc to close
            </p>
            
            <div style={{ display: 'flex', gap: 'var(--space-2)' }}>
              <button
                onClick={onClose}
                style={{
                  padding: 'var(--space-2) var(--space-3)',
                  background: 'var(--surface-elevated)',
                  border: '1px solid var(--border-secondary)',
                  borderRadius: 'var(--radius-sm)',
                  color: 'var(--text-secondary)',
                  fontSize: '0.875rem',
                  cursor: 'pointer',
                  transition: 'all 0.15s ease'
                }}
              >
                Cancel
              </button>
              
              <button
                onClick={handleSave}
                disabled={!hasChanges}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 'var(--space-1)',
                  padding: 'var(--space-2) var(--space-3)',
                  background: hasChanges ? '#0078d4' : 'var(--text-muted)',
                  border: 'none',
                  borderRadius: 'var(--radius-sm)',
                  color: 'white',
                  fontSize: '0.875rem',
                  cursor: hasChanges ? 'pointer' : 'not-allowed',
                  opacity: hasChanges ? 1 : 0.6,
                  transition: 'all 0.15s ease'
                }}
              >
                <Save size={14} />
                Save
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomInstructions;
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { ChatSession } from '../types';
import { Search, X, MessageSquare, Clock, ArrowRight, Zap } from 'lucide-react';

interface SearchResult {
  session: ChatSession;
  messageIndex: number;
  messageContent: string;
  matchText: string;
  contextBefore: string;
  contextAfter: string;
  searchTerm: string;
}

interface ConversationSearchProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectResult: (session: ChatSession, messageIndex?: number) => void;
}

export const ConversationSearch: React.FC<ConversationSearchProps> = ({
  isOpen,
  onClose,
  onSelectResult
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen) {
      // Load sessions and recent searches when opened
      const vscodeApi = window.vscode || window.__vscodeApi;
      if (vscodeApi) {
        vscodeApi.postMessage({
          type: 'getChatSessions'
        });
      }

      // Load recent searches from localStorage
      const saved = localStorage.getItem('xyne.recentSearches');
      if (saved) {
        try {
          setRecentSearches(JSON.parse(saved));
        } catch {
          setRecentSearches([]);
        }
      }

      // Focus search input
      setTimeout(() => searchInputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      
      if (message.type === 'chatSessionsLoaded') {
        setSessions(message.sessions || []);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  const addToRecentSearches = useCallback((query: string) => {
    if (!query.trim()) return;
    
    setRecentSearches(prev => {
      const filtered = prev.filter(q => q !== query.trim());
      const updated = [query.trim(), ...filtered].slice(0, 10);
      localStorage.setItem('xyne.recentSearches', JSON.stringify(updated));
      return updated;
    });
  }, []);

  const performSearch = useCallback((query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsLoading(true);
    const results: SearchResult[] = [];
    const searchTerm = query.toLowerCase();

    sessions.forEach(session => {
      // Search in session name
      if (session.name.toLowerCase().includes(searchTerm)) {
        results.push({
          session,
          messageIndex: -1, // -1 indicates session name match
          messageContent: session.name,
          matchText: session.name,
          contextBefore: '',
          contextAfter: '',
          searchTerm: query
        });
      }

      // Search in message content
      session.messages.forEach((message, index) => {
        if (message.content.toLowerCase().includes(searchTerm)) {
          const content = message.content;
          const matchIndex = content.toLowerCase().indexOf(searchTerm);
          
          // Extract context around the match
          const contextLength = 50;
          const start = Math.max(0, matchIndex - contextLength);
          const end = Math.min(content.length, matchIndex + query.length + contextLength);
          
          const contextBefore = content.substring(start, matchIndex);
          const matchText = content.substring(matchIndex, matchIndex + query.length);
          const contextAfter = content.substring(matchIndex + query.length, end);

          results.push({
            session,
            messageIndex: index,
            messageContent: content,
            matchText,
            contextBefore: start > 0 ? '...' + contextBefore : contextBefore,
            contextAfter: end < content.length ? contextAfter + '...' : contextAfter,
            searchTerm: query
          });
        }
      });
    });

    // Sort results by relevance (session name matches first, then by recency)
    results.sort((a, b) => {
      if (a.messageIndex === -1 && b.messageIndex !== -1) return -1;
      if (a.messageIndex !== -1 && b.messageIndex === -1) return 1;
      return new Date(b.session.updatedAt).getTime() - new Date(a.session.updatedAt).getTime();
    });

    setSearchResults(results);
    setSelectedIndex(0);
    setIsLoading(false);

    // Add to recent searches
    addToRecentSearches(query);
  }, [sessions, addToRecentSearches]);

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    performSearch(query);
  }, [performSearch]);

  const handleResultSelect = (result: SearchResult) => {
    onSelectResult(result.session, result.messageIndex >= 0 ? result.messageIndex : undefined);
    onClose();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => Math.min(prev + 1, searchResults.length - 1));
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => Math.max(prev - 1, 0));
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (searchResults[selectedIndex]) {
        handleResultSelect(searchResults[selectedIndex]);
      }
    }
  };

  const highlightMatch = (text: string, query: string) => {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="search-highlight">{part}</mark>
      ) : (
        part
      )
    );
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const searchDate = new Date(date);
    const diffInHours = (now.getTime() - searchDate.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return searchDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return searchDate.toLocaleDateString([], { weekday: 'short' });
    } else {
      return searchDate.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="search-overlay">
      <div className="search-panel">
        {/* Header */}
        <div className="search-header">
          <div className="search-title">
            <Search size={18} />
            <h3>Search Conversations</h3>
          </div>
          <button className="search-close" onClick={onClose}>
            <X size={16} />
          </button>
        </div>

        {/* Search Input */}
        <div className="search-input-container">
          <Search size={16} className="search-input-icon" />
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search messages and conversations..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            onKeyDown={handleKeyDown}
            className="search-input-field"
          />
          {searchQuery && (
            <button 
              className="search-clear" 
              onClick={() => handleSearch('')}
            >
              <X size={14} />
            </button>
          )}
        </div>

        {/* Recent Searches */}
        {!searchQuery && recentSearches.length > 0 && (
          <div className="recent-searches">
            <div className="recent-searches-header">
              <Zap size={14} />
              <span>Recent Searches</span>
            </div>
            <div className="recent-searches-list">
              {recentSearches.map((search, index) => (
                <button
                  key={index}
                  className="recent-search-item"
                  onClick={() => handleSearch(search)}
                >
                  <Search size={12} />
                  {search}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Search Results */}
        <div className="search-results">
          {isLoading ? (
            <div className="search-loading">
              <div className="loading-spinner" />
              <span>Searching conversations...</span>
            </div>
          ) : searchQuery && searchResults.length === 0 ? (
            <div className="search-empty">
              <Search size={32} />
              <p>No results found</p>
              <span>Try different keywords or check spelling</span>
            </div>
          ) : searchResults.length > 0 ? (
            <div className="search-results-list">
              <div className="search-results-header">
                <span>{searchResults.length} result{searchResults.length !== 1 ? 's' : ''}</span>
              </div>
              {searchResults.map((result, index) => (
                <div
                  key={`${result.session.id}-${result.messageIndex}`}
                  className={`search-result-item ${index === selectedIndex ? 'selected' : ''}`}
                  onClick={() => handleResultSelect(result)}
                >
                  <div className="search-result-content">
                    <div className="search-result-header">
                      <div className="search-result-session">
                        <MessageSquare size={14} />
                        <span className="session-name">
                          {highlightMatch(result.session.name, searchQuery)}
                        </span>
                      </div>
                      <div className="search-result-meta">
                        <Clock size={12} />
                        <span>{formatDate(result.session.updatedAt)}</span>
                      </div>
                    </div>
                    
                    {result.messageIndex >= 0 && (
                      <div className="search-result-snippet">
                        <span className="snippet-context">{result.contextBefore}</span>
                        <mark className="search-highlight">{result.matchText}</mark>
                        <span className="snippet-context">{result.contextAfter}</span>
                      </div>
                    )}
                    
                    <div className="search-result-info">
                      <span className="result-type">
                        {result.messageIndex === -1 ? 'Session name' : `Message ${result.messageIndex + 1}`}
                      </span>
                      <ArrowRight size={12} />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : null}
        </div>

        {/* Footer */}
        {searchResults.length > 0 && (
          <div className="search-footer">
            <span>Use ↑↓ to navigate • Enter to open • Esc to close</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConversationSearch;
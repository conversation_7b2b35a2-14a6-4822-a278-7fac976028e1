import React, { useState, useEffect } from 'react';
import { X, Server, ExternalLink, Plus, Trash2, Eye, EyeOff } from 'lucide-react';

interface AddMcpServerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddServer: (config: any) => void;
  activeTab?: 'remote' | 'local';
  editServer?: any; // Server to edit (if editing mode)
  onUpdateServer?: (config: any) => void; // Update handler for edit mode
}

export const AddMcpServerModal: React.FC<AddMcpServerModalProps> = ({ 
  isOpen, 
  onClose, 
  onAddServer,
  activeTab = 'local',
  editServer,
  onUpdateServer
}) => {
  const [selectedTab, setSelectedTab] = useState<'local' | 'remote'>(activeTab);
  const [formData, setFormData] = useState({
    name: '',
    command: '',
    args: '',
    url: '',
    cwd: ''
  });
  const [envVars, setEnvVars] = useState<Array<{ key: string; value: string; isSecret: boolean; showValue: boolean }>>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const isEditMode = !!editServer;

  // Populate form when editing
  useEffect(() => {
    if (editServer && isOpen) {
      const config = editServer.config;
      setFormData({
        name: config.name || '',
        command: config.command || '',
        args: config.args ? config.args.join(' ') : '',
        url: config.url || '',
        cwd: config.cwd || ''
      });
      
      // Set tab based on server type
      setSelectedTab(config.type === 'stdio' ? 'local' : 'remote');
      
      // Populate environment variables (but don't show secret values)
      if (config.env) {
        const envVarArray = Object.entries(config.env).map(([key, envConfig]: [string, any]) => ({
          key,
          value: envConfig.isSecret ? '' : (envConfig.value || ''),
          isSecret: envConfig.isSecret || false,
          showValue: false
        }));
        setEnvVars(envVarArray);
      } else {
        setEnvVars([]);
      }
    } else if (!editServer) {
      // Reset form for add mode
      setFormData({
        name: '',
        command: '',
        args: '',
        url: '',
        cwd: ''
      });
      setEnvVars([]);
      setSelectedTab(activeTab);
    }
  }, [editServer, isOpen, activeTab]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Server name is required';
    }

    if (selectedTab === 'local') {
      if (!formData.command.trim()) {
        newErrors.command = 'Command is required';
      }
    } else {
      if (!formData.url.trim()) {
        newErrors.url = 'Server URL is required';
      } else {
        try {
          new URL(formData.url);
        } catch {
          newErrors.url = 'Please enter a valid URL';
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const serverConfig = {
      name: formData.name.trim(),
      type: selectedTab === 'local' ? 'stdio' : 'http',
      disabled: false
    };

    // Add environment variables if any are defined
    const envVarObject: Record<string, { value: string; isSecret: boolean }> = {};
    envVars.forEach(envVar => {
      if (envVar.key.trim() && envVar.value.trim()) {
        envVarObject[envVar.key.trim()] = {
          value: envVar.value,
          isSecret: envVar.isSecret
        };
      }
    });

    if (Object.keys(envVarObject).length > 0) {
      Object.assign(serverConfig, { env: envVarObject });
    }

    if (selectedTab === 'local') {
      Object.assign(serverConfig, {
        command: formData.command.trim(),
        args: formData.args.trim() ? formData.args.trim().split(/\s+/) : [],
        cwd: formData.cwd.trim() || undefined
      });
    } else {
      Object.assign(serverConfig, {
        url: formData.url.trim()
      });
    }

    if (isEditMode && onUpdateServer) {
      onUpdateServer(serverConfig);
    } else {
      onAddServer(serverConfig);
    }
    handleClose();
  };

  const handleClose = () => {
    setFormData({ name: '', command: '', args: '', url: '', cwd: '' });
    setEnvVars([]);
    setErrors({});
    onClose();
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addEnvVar = () => {
    setEnvVars(prev => [...prev, { key: '', value: '', isSecret: false, showValue: false }]);
  };

  const removeEnvVar = (index: number) => {
    setEnvVars(prev => prev.filter((_, i) => i !== index));
  };

  const updateEnvVar = (index: number, field: 'key' | 'value' | 'isSecret', value: string | boolean) => {
    setEnvVars(prev => prev.map((envVar, i) => 
      i === index ? { ...envVar, [field]: value } : envVar
    ));
  };

  const toggleEnvVarVisibility = (index: number) => {
    setEnvVars(prev => prev.map((envVar, i) => 
      i === index ? { ...envVar, showValue: !envVar.showValue } : envVar
    ));
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1001
    }}>
      <div style={{
        backgroundColor: 'var(--vscode-editor-background)',
        border: '1px solid var(--vscode-panel-border)',
        borderRadius: '8px',
        maxWidth: '600px',
        width: '90%',
        maxHeight: '80vh',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '16px',
          borderBottom: '1px solid var(--vscode-panel-border)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Server size={20} style={{ color: 'var(--vscode-foreground)' }} />
            <h2 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: 'var(--vscode-foreground)',
              margin: 0
            }}>
              {isEditMode ? 'Edit MCP Server' : 'Add MCP Server'}
            </h2>
          </div>
          <button
            onClick={handleClose}
            style={{
              padding: '4px',
              backgroundColor: 'transparent',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              color: 'var(--vscode-foreground)'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-toolbar-hoverBackground)'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
          >
            <X size={16} />
          </button>
        </div>

        {/* Tabs */}
        <div style={{
          display: 'flex',
          borderBottom: '1px solid var(--vscode-panel-border)'
        }}>
          {[
            { id: 'local', label: 'Local Server' },
            { id: 'remote', label: 'Remote Server' }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setSelectedTab(tab.id as any)}
              style={{
                padding: '12px 20px',
                borderBottom: selectedTab === tab.id ? '2px solid var(--vscode-focusBorder)' : '2px solid transparent',
                backgroundColor: 'transparent',
                border: 'none',
                cursor: 'pointer',
                color: selectedTab === tab.id ? 'var(--vscode-foreground)' : 'var(--vscode-descriptionForeground)',
                transition: 'all 0.2s'
              }}
              onMouseOver={(e) => {
                if (selectedTab !== tab.id) {
                  e.currentTarget.style.color = 'var(--vscode-foreground)';
                }
              }}
              onMouseOut={(e) => {
                if (selectedTab !== tab.id) {
                  e.currentTarget.style.color = 'var(--vscode-descriptionForeground)';
                }
              }}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <div style={{ flex: 1, padding: '20px', overflow: 'auto' }}>
            {/* Server Name - Always shown */}
            <div style={{ marginBottom: '20px' }}>
              <label style={{
                display: 'block',
                marginBottom: '6px',
                fontSize: '14px',
                fontWeight: '500',
                color: 'var(--vscode-foreground)'
              }}>
                Server Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="e.g., filesystem, git, database"
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  fontSize: '14px',
                  backgroundColor: 'var(--vscode-input-background)',
                  color: 'var(--vscode-input-foreground)',
                  border: `1px solid ${errors.name ? 'var(--vscode-inputValidation-errorBorder)' : 'var(--vscode-input-border)'}`,
                  borderRadius: '4px',
                  outline: 'none',
                  boxSizing: 'border-box'
                }}
                onFocus={(e) => e.target.style.borderColor = 'var(--vscode-focusBorder)'}
                onBlur={(e) => e.target.style.borderColor = errors.name ? 'var(--vscode-inputValidation-errorBorder)' : 'var(--vscode-input-border)'}
              />
              {errors.name && (
                <div style={{
                  marginTop: '4px',
                  fontSize: '12px',
                  color: 'var(--vscode-inputValidation-errorForeground)'
                }}>
                  {errors.name}
                </div>
              )}
            </div>

            {selectedTab === 'local' ? (
              <>
                {/* Instructions */}
                <div style={{
                  marginBottom: '20px',
                  padding: '12px',
                  backgroundColor: 'var(--vscode-editor-inactiveSelectionBackground)',
                  border: '1px solid var(--vscode-panel-border)',
                  borderRadius: '4px'
                }}>
                  <div style={{
                    fontSize: '14px',
                    fontWeight: '500',
                    color: 'var(--vscode-foreground)',
                    marginBottom: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px'
                  }}>
                    💡 Quick Setup Examples
                  </div>
                  <div style={{
                    fontSize: '12px',
                    color: 'var(--vscode-descriptionForeground)',
                    lineHeight: '1.4'
                  }}>
                    <div style={{ marginBottom: '6px' }}>
                      <strong>File System:</strong> Command: <code style={{ backgroundColor: 'var(--vscode-textCodeBlock-background)', padding: '2px 4px', borderRadius: '2px' }}>npx</code> | Args: <code style={{ backgroundColor: 'var(--vscode-textCodeBlock-background)', padding: '2px 4px', borderRadius: '2px' }}>@modelcontextprotocol/server-filesystem /path/to/directory</code>
                    </div>
                    <div style={{ marginBottom: '6px' }}>
                      <strong>GitHub:</strong> Command: <code style={{ backgroundColor: 'var(--vscode-textCodeBlock-background)', padding: '2px 4px', borderRadius: '2px' }}>npx</code> | Args: <code style={{ backgroundColor: 'var(--vscode-textCodeBlock-background)', padding: '2px 4px', borderRadius: '2px' }}>@modelcontextprotocol/server-github</code> | Env: <code style={{ backgroundColor: 'var(--vscode-textCodeBlock-background)', padding: '2px 4px', borderRadius: '2px' }}>GITHUB_PERSONAL_ACCESS_TOKEN</code>
                    </div>
                    <div>
                      <strong>SQLite:</strong> Command: <code style={{ backgroundColor: 'var(--vscode-textCodeBlock-background)', padding: '2px 4px', borderRadius: '2px' }}>npx</code> | Args: <code style={{ backgroundColor: 'var(--vscode-textCodeBlock-background)', padding: '2px 4px', borderRadius: '2px' }}>@modelcontextprotocol/server-sqlite /path/to/database.db</code>
                    </div>
                  </div>
                </div>

                {/* Command */}
                <div style={{ marginBottom: '20px' }}>
                  <label style={{
                    display: 'block',
                    marginBottom: '6px',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: 'var(--vscode-foreground)'
                  }}>
                    Command *
                  </label>
                  <input
                    type="text"
                    value={formData.command}
                    onChange={(e) => handleInputChange('command', e.target.value)}
                    placeholder="e.g., npx, node, python"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      fontSize: '14px',
                      backgroundColor: 'var(--vscode-input-background)',
                      color: 'var(--vscode-input-foreground)',
                      border: `1px solid ${errors.command ? 'var(--vscode-inputValidation-errorBorder)' : 'var(--vscode-input-border)'}`,
                      borderRadius: '4px',
                      outline: 'none',
                      boxSizing: 'border-box'
                    }}
                    onFocus={(e) => e.target.style.borderColor = 'var(--vscode-focusBorder)'}
                    onBlur={(e) => e.target.style.borderColor = errors.command ? 'var(--vscode-inputValidation-errorBorder)' : 'var(--vscode-input-border)'}
                  />
                  {errors.command && (
                    <div style={{
                      marginTop: '4px',
                      fontSize: '12px',
                      color: 'var(--vscode-inputValidation-errorForeground)'
                    }}>
                      {errors.command}
                    </div>
                  )}
                </div>

                {/* Arguments */}
                <div style={{ marginBottom: '20px' }}>
                  <label style={{
                    display: 'block',
                    marginBottom: '6px',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: 'var(--vscode-foreground)'
                  }}>
                    Arguments
                  </label>
                  <input
                    type="text"
                    value={formData.args}
                    onChange={(e) => handleInputChange('args', e.target.value)}
                    placeholder="e.g., -y @modelcontextprotocol/server-filesystem /path/to/directory"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      fontSize: '14px',
                      backgroundColor: 'var(--vscode-input-background)',
                      color: 'var(--vscode-input-foreground)',
                      border: '1px solid var(--vscode-input-border)',
                      borderRadius: '4px',
                      outline: 'none',
                      boxSizing: 'border-box'
                    }}
                    onFocus={(e) => e.target.style.borderColor = 'var(--vscode-focusBorder)'}
                    onBlur={(e) => e.target.style.borderColor = 'var(--vscode-input-border)'}
                  />
                  <div style={{
                    marginTop: '4px',
                    fontSize: '12px',
                    color: 'var(--vscode-descriptionForeground)'
                  }}>
                    Space-separated command line arguments
                  </div>
                </div>

                {/* Working Directory */}
                <div style={{ marginBottom: '20px' }}>
                  <label style={{
                    display: 'block',
                    marginBottom: '6px',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: 'var(--vscode-foreground)'
                  }}>
                    Working Directory
                  </label>
                  <input
                    type="text"
                    value={formData.cwd}
                    onChange={(e) => handleInputChange('cwd', e.target.value)}
                    placeholder="Optional working directory for the server process"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      fontSize: '14px',
                      backgroundColor: 'var(--vscode-input-background)',
                      color: 'var(--vscode-input-foreground)',
                      border: '1px solid var(--vscode-input-border)',
                      borderRadius: '4px',
                      outline: 'none',
                      boxSizing: 'border-box'
                    }}
                    onFocus={(e) => e.target.style.borderColor = 'var(--vscode-focusBorder)'}
                    onBlur={(e) => e.target.style.borderColor = 'var(--vscode-input-border)'}
                  />
                </div>

                {/* Environment Variables */}
                <div style={{ marginBottom: '20px' }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: '12px'
                  }}>
                    <label style={{
                      fontSize: '14px',
                      fontWeight: '500',
                      color: 'var(--vscode-foreground)'
                    }}>
                      Environment Variables
                    </label>
                    <button
                      type="button"
                      onClick={addEnvVar}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px',
                        padding: '4px 8px',
                        backgroundColor: 'var(--vscode-button-secondaryBackground)',
                        color: 'var(--vscode-button-secondaryForeground)',
                        border: '1px solid var(--vscode-button-border)',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                      onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-button-secondaryHoverBackground)'}
                      onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-button-secondaryBackground)'}
                    >
                      <Plus size={12} />
                      Add Variable
                    </button>
                  </div>

                  {envVars.length === 0 ? (
                    <div style={{
                      padding: '16px',
                      backgroundColor: 'var(--vscode-editor-inactiveSelectionBackground)',
                      border: '1px dashed var(--vscode-panel-border)',
                      borderRadius: '4px',
                      textAlign: 'center',
                      fontSize: '12px',
                      color: 'var(--vscode-descriptionForeground)'
                    }}>
                      No environment variables configured. Click "Add Variable" to add one.
                    </div>
                  ) : (
                    <div style={{
                      border: '1px solid var(--vscode-panel-border)',
                      borderRadius: '4px',
                      backgroundColor: 'var(--vscode-editor-background)'
                    }}>
                      {envVars.map((envVar, index) => (
                        <div
                          key={index}
                          style={{
                            padding: '12px',
                            borderBottom: index < envVars.length - 1 ? '1px solid var(--vscode-panel-border)' : 'none',
                            display: 'flex',
                            gap: '8px',
                            alignItems: 'flex-start'
                          }}
                        >
                          <div style={{ flex: '0 0 120px' }}>
                            <input
                              type="text"
                              value={envVar.key}
                              onChange={(e) => updateEnvVar(index, 'key', e.target.value)}
                              placeholder="Variable name"
                              style={{
                                width: '100%',
                                padding: '6px 8px',
                                fontSize: '12px',
                                backgroundColor: 'var(--vscode-input-background)',
                                color: 'var(--vscode-input-foreground)',
                                border: '1px solid var(--vscode-input-border)',
                                borderRadius: '3px',
                                outline: 'none',
                                boxSizing: 'border-box'
                              }}
                              onFocus={(e) => e.target.style.borderColor = 'var(--vscode-focusBorder)'}
                              onBlur={(e) => e.target.style.borderColor = 'var(--vscode-input-border)'}
                            />
                          </div>
                          <div style={{ flex: 1, position: 'relative' }}>
                            <input
                              type={envVar.showValue ? 'text' : 'password'}
                              value={envVar.value}
                              onChange={(e) => updateEnvVar(index, 'value', e.target.value)}
                              placeholder="Variable value"
                              style={{
                                width: '100%',
                                padding: '6px 32px 6px 8px',
                                fontSize: '12px',
                                backgroundColor: 'var(--vscode-input-background)',
                                color: 'var(--vscode-input-foreground)',
                                border: '1px solid var(--vscode-input-border)',
                                borderRadius: '3px',
                                outline: 'none',
                                boxSizing: 'border-box'
                              }}
                              onFocus={(e) => e.target.style.borderColor = 'var(--vscode-focusBorder)'}
                              onBlur={(e) => e.target.style.borderColor = 'var(--vscode-input-border)'}
                            />
                            <button
                              type="button"
                              onClick={() => toggleEnvVarVisibility(index)}
                              style={{
                                position: 'absolute',
                                right: '6px',
                                top: '50%',
                                transform: 'translateY(-50%)',
                                padding: '2px',
                                backgroundColor: 'transparent',
                                border: 'none',
                                cursor: 'pointer',
                                color: 'var(--vscode-descriptionForeground)',
                                display: 'flex',
                                alignItems: 'center'
                              }}
                              title={envVar.showValue ? 'Hide value' : 'Show value'}
                            >
                              {envVar.showValue ? <EyeOff size={12} /> : <Eye size={12} />}
                            </button>
                          </div>
                          <div style={{ flex: '0 0 auto', display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <label style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '4px',
                              fontSize: '11px',
                              color: 'var(--vscode-descriptionForeground)',
                              cursor: 'pointer'
                            }}>
                              <input
                                type="checkbox"
                                checked={envVar.isSecret}
                                onChange={(e) => updateEnvVar(index, 'isSecret', e.target.checked)}
                                style={{ margin: 0 }}
                              />
                              Secret
                            </label>
                            <button
                              type="button"
                              onClick={() => removeEnvVar(index)}
                              style={{
                                padding: '4px',
                                backgroundColor: 'transparent',
                                border: 'none',
                                cursor: 'pointer',
                                color: 'var(--vscode-errorForeground)',
                                display: 'flex',
                                alignItems: 'center'
                              }}
                              title="Remove variable"
                              onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-toolbar-hoverBackground)'}
                              onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                            >
                              <Trash2 size={12} />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  <div style={{
                    marginTop: '6px',
                    fontSize: '11px',
                    color: 'var(--vscode-descriptionForeground)'
                  }}>
                    Add environment variables that the MCP server needs. Mark sensitive values as "Secret" to store them securely.
                  </div>
                </div>
              </>
            ) : (
              <>
                {/* Instructions for Remote */}
                <div style={{
                  marginBottom: '20px',
                  padding: '12px',
                  backgroundColor: 'var(--vscode-editor-inactiveSelectionBackground)',
                  border: '1px solid var(--vscode-panel-border)',
                  borderRadius: '4px'
                }}>
                  <div style={{
                    fontSize: '14px',
                    fontWeight: '500',
                    color: 'var(--vscode-foreground)',
                    marginBottom: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px'
                  }}>
                    🌐 Remote Server Setup
                  </div>
                  <div style={{
                    fontSize: '12px',
                    color: 'var(--vscode-descriptionForeground)',
                    lineHeight: '1.4'
                  }}>
                    <div style={{ marginBottom: '6px' }}>
                      <strong>GitHub Remote:</strong> URL: <code style={{ backgroundColor: 'var(--vscode-textCodeBlock-background)', padding: '2px 4px', borderRadius: '2px' }}>https://api.githubcopilot.com/mcp/</code> | Header: <code style={{ backgroundColor: 'var(--vscode-textCodeBlock-background)', padding: '2px 4px', borderRadius: '2px' }}>Authorization: Bearer your_token</code>
                    </div>
                    <div style={{ marginBottom: '6px' }}>
                      <strong>Custom Server:</strong> Your own HTTP MCP server endpoint
                    </div>
                    <div style={{ fontSize: '11px', fontStyle: 'italic', color: 'var(--vscode-descriptionForeground)' }}>
                      💡 Environment variables become HTTP headers for remote servers
                    </div>
                  </div>
                </div>

                {/* Remote Server URL */}
                <div style={{ marginBottom: '20px' }}>
                  <label style={{
                    display: 'block',
                    marginBottom: '6px',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: 'var(--vscode-foreground)'
                  }}>
                    Server URL *
                  </label>
                  <input
                    type="url"
                    value={formData.url}
                    onChange={(e) => handleInputChange('url', e.target.value)}
                    placeholder="https://api.example.com/mcp"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      fontSize: '14px',
                      backgroundColor: 'var(--vscode-input-background)',
                      color: 'var(--vscode-input-foreground)',
                      border: `1px solid ${errors.url ? 'var(--vscode-inputValidation-errorBorder)' : 'var(--vscode-input-border)'}`,
                      borderRadius: '4px',
                      outline: 'none',
                      boxSizing: 'border-box'
                    }}
                    onFocus={(e) => e.target.style.borderColor = 'var(--vscode-focusBorder)'}
                    onBlur={(e) => e.target.style.borderColor = errors.url ? 'var(--vscode-inputValidation-errorBorder)' : 'var(--vscode-input-border)'}
                  />
                  {errors.url && (
                    <div style={{
                      marginTop: '4px',
                      fontSize: '12px',
                      color: 'var(--vscode-inputValidation-errorForeground)'
                    }}>
                      {errors.url}
                    </div>
                  )}
                  <div style={{
                    marginTop: '8px',
                    fontSize: '12px',
                    color: 'var(--vscode-descriptionForeground)',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px'
                  }}>
                    <span>Add a remote MCP server by providing a name and its URL endpoint.</span>
                    <button
                      type="button"
                      onClick={() => window.open('https://modelcontextprotocol.io/docs/concepts/servers', '_blank')}
                      style={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        gap: '2px',
                        backgroundColor: 'transparent',
                        border: 'none',
                        color: 'var(--vscode-textLink-foreground)',
                        cursor: 'pointer',
                        fontSize: '12px',
                        textDecoration: 'underline'
                      }}
                    >
                   
                    </button>
                  </div>
                </div>

                {/* Environment Variables for Remote Servers */}
                <div style={{ marginBottom: '20px' }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: '12px'
                  }}>
                    <label style={{
                      fontSize: '14px',
                      fontWeight: '500',
                      color: 'var(--vscode-foreground)'
                    }}>
                      Environment Variables
                    </label>
                    <button
                      type="button"
                      onClick={addEnvVar}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px',
                        padding: '4px 8px',
                        backgroundColor: 'var(--vscode-button-secondaryBackground)',
                        color: 'var(--vscode-button-secondaryForeground)',
                        border: '1px solid var(--vscode-button-border)',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                      onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-button-secondaryHoverBackground)'}
                      onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-button-secondaryBackground)'}
                    >
                      <Plus size={12} />
                      Add Variable
                    </button>
                  </div>

                  {envVars.length === 0 ? (
                    <div style={{
                      padding: '16px',
                      backgroundColor: 'var(--vscode-editor-inactiveSelectionBackground)',
                      border: '1px dashed var(--vscode-panel-border)',
                      borderRadius: '4px',
                      textAlign: 'center',
                      fontSize: '12px',
                      color: 'var(--vscode-descriptionForeground)'
                    }}>
                      No environment variables configured. Click "Add Variable" to add one.
                    </div>
                  ) : (
                    <div style={{
                      border: '1px solid var(--vscode-panel-border)',
                      borderRadius: '4px',
                      backgroundColor: 'var(--vscode-editor-background)'
                    }}>
                      {envVars.map((envVar, index) => (
                        <div
                          key={index}
                          style={{
                            padding: '12px',
                            borderBottom: index < envVars.length - 1 ? '1px solid var(--vscode-panel-border)' : 'none',
                            display: 'flex',
                            gap: '8px',
                            alignItems: 'flex-start'
                          }}
                        >
                          <div style={{ flex: '0 0 120px' }}>
                            <input
                              type="text"
                              value={envVar.key}
                              onChange={(e) => updateEnvVar(index, 'key', e.target.value)}
                              placeholder="Variable name"
                              style={{
                                width: '100%',
                                padding: '6px 8px',
                                fontSize: '12px',
                                backgroundColor: 'var(--vscode-input-background)',
                                color: 'var(--vscode-input-foreground)',
                                border: '1px solid var(--vscode-input-border)',
                                borderRadius: '3px',
                                outline: 'none',
                                boxSizing: 'border-box'
                              }}
                              onFocus={(e) => e.target.style.borderColor = 'var(--vscode-focusBorder)'}
                              onBlur={(e) => e.target.style.borderColor = 'var(--vscode-input-border)'}
                            />
                          </div>
                          <div style={{ flex: 1, position: 'relative' }}>
                            <input
                              type={envVar.showValue ? 'text' : 'password'}
                              value={envVar.value}
                              onChange={(e) => updateEnvVar(index, 'value', e.target.value)}
                              placeholder="Variable value"
                              style={{
                                width: '100%',
                                padding: '6px 32px 6px 8px',
                                fontSize: '12px',
                                backgroundColor: 'var(--vscode-input-background)',
                                color: 'var(--vscode-input-foreground)',
                                border: '1px solid var(--vscode-input-border)',
                                borderRadius: '3px',
                                outline: 'none',
                                boxSizing: 'border-box'
                              }}
                              onFocus={(e) => e.target.style.borderColor = 'var(--vscode-focusBorder)'}
                              onBlur={(e) => e.target.style.borderColor = 'var(--vscode-input-border)'}
                            />
                            <button
                              type="button"
                              onClick={() => toggleEnvVarVisibility(index)}
                              style={{
                                position: 'absolute',
                                right: '6px',
                                top: '50%',
                                transform: 'translateY(-50%)',
                                padding: '2px',
                                backgroundColor: 'transparent',
                                border: 'none',
                                cursor: 'pointer',
                                color: 'var(--vscode-descriptionForeground)',
                                display: 'flex',
                                alignItems: 'center'
                              }}
                              title={envVar.showValue ? 'Hide value' : 'Show value'}
                            >
                              {envVar.showValue ? <EyeOff size={12} /> : <Eye size={12} />}
                            </button>
                          </div>
                          <div style={{ flex: '0 0 auto', display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <label style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '4px',
                              fontSize: '11px',
                              color: 'var(--vscode-descriptionForeground)',
                              cursor: 'pointer'
                            }}>
                              <input
                                type="checkbox"
                                checked={envVar.isSecret}
                                onChange={(e) => updateEnvVar(index, 'isSecret', e.target.checked)}
                                style={{ margin: 0 }}
                              />
                              Secret
                            </label>
                            <button
                              type="button"
                              onClick={() => removeEnvVar(index)}
                              style={{
                                padding: '4px',
                                backgroundColor: 'transparent',
                                border: 'none',
                                cursor: 'pointer',
                                color: 'var(--vscode-errorForeground)',
                                display: 'flex',
                                alignItems: 'center'
                              }}
                              title="Remove variable"
                              onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-toolbar-hoverBackground)'}
                              onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                            >
                              <Trash2 size={12} />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  <div style={{
                    marginTop: '6px',
                    fontSize: '11px',
                    color: 'var(--vscode-descriptionForeground)'
                  }}>
                    Add HTTP headers for the remote MCP server. These become request headers (e.g., Authorization). Mark sensitive values as "Secret" to store them securely.
                  </div>
                </div>
              </>
            )}
          </div>

          {/* Footer */}
          <div style={{
            borderTop: '1px solid var(--vscode-panel-border)',
            padding: '16px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <button
              type="button"
              onClick={() => {
                const vscodeApi = window.vscode || window.__vscodeApi;
                if (vscodeApi) {
                  vscodeApi.postMessage({
                    type: 'openMcpSettings'
                  });
                }
              }}
              style={{
                padding: '8px 16px',
                backgroundColor: 'transparent',
                color: 'var(--vscode-textLink-foreground)',
                border: '1px solid var(--vscode-button-border)',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px',
                display: 'flex',
                alignItems: 'center',
                gap: '6px'
              }}
            >
              Edit Configuration
              <ExternalLink size={14} />
            </button>
            
            <div style={{ display: 'flex', gap: '12px' }}>
              <button
                type="button"
                onClick={handleClose}
                style={{
                  padding: '8px 16px',
                  backgroundColor: 'transparent',
                  color: 'var(--vscode-foreground)',
                  border: '1px solid var(--vscode-button-border)',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-button-hoverBackground)'}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              >
                Cancel
              </button>
              <button
                type="submit"
                style={{
                  padding: '8px 16px',
                  backgroundColor: 'var(--vscode-button-background)',
                  color: 'var(--vscode-button-foreground)',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-button-hoverBackground)'}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'var(--vscode-button-background)'}
              >
                {isEditMode ? 'Update Server' : 'Add Server'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddMcpServerModal;
import { useEffect, useRef } from 'react';
import { Terminal as XTerm } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import '@xterm/xterm/css/xterm.css';
import { TerminalExecutionHistory } from '../types';

interface TerminalInstanceProps {
  sessionId: string;
  initialCommand?: string;
  onClose?: () => void;
  readonly?: boolean; // Whether this is a readonly historical terminal
  terminalHistory?: TerminalExecutionHistory; // Historical output to display
}

export default function TerminalInstance({ sessionId, initialCommand, onClose, readonly = false, terminalHistory }: TerminalInstanceProps) {
  const terminalRef = useRef<HTMLDivElement>(null);
  const xtermRef = useRef<XTerm | null>(null);
  const fitAddonRef = useRef<FitAddon | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    if (!terminalRef.current) {
      return;
    }

    let hasSentInitialCommand = false;
    let isDisconnected = false; // Track disconnect state

    const xterm = new XTerm({
      fontFamily: 'Menlo, Monaco, "Courier New", monospace',
      fontSize: 14,
      letterSpacing: 0, // Enforced correct value
      lineHeight: 1.2, // Ensured consistent line height
      theme: {
        background: '#1e1e1e',
        foreground: '#d4d4d4',
        cursor: '#d4d4d4',
        selectionBackground: '#264f78',
      },
      cursorBlink: !readonly, // Don't blink cursor in readonly mode
      scrollback: 1000,
      allowTransparency: true,
      convertEol: true,
      disableStdin: readonly, // Disable input in readonly mode
    });

    // Apply additional CSS overrides to enforce letter spacing
    if (terminalRef.current) {
      const terminalElement = terminalRef.current.querySelector('.xterm-rows') as HTMLElement;
      if (terminalElement) {
        const style = document.createElement('style');
        style.innerHTML = `.xterm-rows { letter-spacing: 0px !important; }`;
        document.head.appendChild(style);
      }
    }

    const fitAddon = new FitAddon();
    const webLinksAddon = new WebLinksAddon();
    xterm.loadAddon(fitAddon);
    xterm.loadAddon(webLinksAddon);
    xterm.open(terminalRef.current);
    
    setTimeout(() => {
      try {
        fitAddon.fit();
        if (!readonly) {
          xterm.focus();
        }
      } catch (error) {
        console.error('Error during initial terminal fit:', error);
      }
    }, 100);
    xtermRef.current = xterm;
    fitAddonRef.current = fitAddon;

    // If readonly mode and we have terminal history, display it
    if (readonly && terminalHistory) {
      // Write the historical command and output
      xterm.write(`$ ${terminalHistory.command}\r\n`);
      if (terminalHistory.output) {
        xterm.write(terminalHistory.output);
      }
      if (terminalHistory.terminated) {
        xterm.write('\r\n[Process completed]');
        if (terminalHistory.exitCode !== undefined) {
          xterm.write(` - exit code ${terminalHistory.exitCode}`);
        }
        xterm.write('\r\n');
      }
      return; // Don't set up WebSocket connection in readonly mode
    }

    // If readonly mode but no terminal history, show placeholder
    if (readonly && !terminalHistory) {
      xterm.write(`$ ${initialCommand || 'command'}\r\n`);
      xterm.write('\r\n[Historical terminal output not available - command was not re-executed for safety]\r\n');
      xterm.write('[To run this command, copy it and use a new terminal]\r\n');
      return; // Don't set up WebSocket connection in readonly mode
    }

    // WebSocket connection to pty-server (sessionId is not used by server, but could be for future multi-session support)
    const ws = new WebSocket('ws://localhost:8080');
    wsRef.current = ws;
    
    ws.onopen = () => {
      // Send cwd as the first message
      const cwd = (window as any).__vscodeWorkspacePath || process.env.HOME || '/';
      ws.send(JSON.stringify({ cwd }));
      // Do not send initialCommand yet
    };
    
    ws.onmessage = (event) => {
      if (isDisconnected) return; // Ignore messages after disconnect
      try {
        const message = JSON.parse(event.data);
        if (message.type === 'output') {
          xterm.write(message.data);
          if (initialCommand && !hasSentInitialCommand) {
            ws.send(JSON.stringify({ type: 'input', data: initialCommand + '\r' }));
            hasSentInitialCommand = true;
          }
        }
      } catch (e) {
        console.error('Error parsing WebSocket message:', e);
      }
    };
    
    ws.onclose = () => {
      isDisconnected = true;
      xterm.write('\r\n[PTY server disconnected]\r\n');
      // Disable input if setOption exists (xterm.js >=4.19), otherwise just blur
      if (typeof (xterm as any).setOption === 'function') {
        (xterm as any).setOption('disableStdin', true);
      } else if (xtermRef.current) {
        xtermRef.current.blur();
      }
      if (onClose) onClose(); // Optionally close the terminal UI
    };
    
    ws.onerror = (error) => {
      console.error('[TerminalInstance] WebSocket ERROR:', error);
    };

    // Send terminal input to server (only in interactive mode)
    let disposable: any = null;
    if (!readonly) {
      disposable = xterm.onData((data) => {
        if (!isDisconnected) {
          ws.send(JSON.stringify({ type: 'input', data }));
        }
      });
    }

    // Handle resize
    const handleResize = () => {
      if (fitAddonRef.current && wsRef.current && xtermRef.current) {
        try {
          fitAddonRef.current.fit();
          wsRef.current.send(
            JSON.stringify({
              type: 'resize',
              cols: xtermRef.current.cols,
              rows: xtermRef.current.rows,
            })
          );
        } catch (error) {
          console.error('Error fitting terminal:', error);
        }
      }
    };
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (disposable) {
        disposable.dispose();
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (xtermRef.current) {
        xtermRef.current.dispose();
      }
    };
  }, [sessionId, initialCommand, readonly, terminalHistory]);

  return (
    <div className="terminal-bubble" style={{ width: '100%', minHeight: '200px', maxHeight: '400px', height: '300px', margin: '20px 0', borderRadius: '10px', boxShadow: '0 4px 16px #0003', background: '#18181b', border: '1px solid #23232a' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        background: 'linear-gradient(90deg, #23232a 60%, #23232a 100%)',
        color: '#fff',
        fontWeight: 600,
        fontSize: '15px',
        padding: '0 16px',
        height: '40px',
        borderBottom: '1px solid #23232a',
      }}>
        <span style={{ fontFamily: 'inherit', fontWeight: 600 }}>
          {readonly ? `${initialCommand || 'Terminal'}` : (initialCommand || 'Terminal')}
        </span>
        {onClose && (
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              color: '#fff',
              fontSize: '18px',
              cursor: 'pointer',
              borderRadius: '4px',
              padding: '2px 8px',
              transition: 'background 0.15s',
            }}
            onMouseOver={e => (e.currentTarget.style.background = '#23232a')}
            onMouseOut={e => (e.currentTarget.style.background = 'none')}
            title="Close terminal"
          >
          </button>
        )}
      </div>
      <div ref={terminalRef} style={{ width: '100%', height: 'calc(100% - 40px)', background: '#1e1e1e', overflow: 'auto' }} />
    </div>
  );
}

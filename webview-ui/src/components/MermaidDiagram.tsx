import React, { useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';
import { Copy, Check, ZoomIn, ZoomOut, RotateCcw, FileText } from 'lucide-react';

interface MermaidDiagramProps {
  chart: string;
}

const MermaidDiagram: React.FC<MermaidDiagramProps> = ({ chart }) => {
  const [copied, setCopied] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [zoom, setZoom] = useState(1);
  const mermaidRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);


  // Initialize Mermaid with theme configuration
  useEffect(() => {
    if (!isInitialized) {
      mermaid.initialize({
        startOnLoad: false,
        theme: "base",  // ONLY theme that allows full customization
        themeVariables: {
          // Light theme with dark text - CONSISTENT
          darkMode: false,
          
          // Main colors
          primaryColor: "#ffffff",
          secondaryColor: "#f8f9fa", 
          tertiaryColor: "#e9ecef",
          background: "#ffffff",
          
          // All text colors - dark for visibility
          primaryTextColor: "#000000",
          secondaryTextColor: "#000000",
          tertiaryTextColor: "#000000",
          textColor: "#000000",
          
          // Node styling
          mainBkg: "#ffffff",
          nodeBorder: "#0078d4",
          clusterBkg: "#f8f9fa",
          clusterBorder: "#0078d4",
          
          // Use Mermaid's default fonts for proper metrics
          fontFamily: "arial",
          fontSize: "14px"
        },
        flowchart: {
          htmlLabels: true,
          curve: 'basis',
          padding: 10,
          nodeSpacing: 80,
          rankSpacing: 80
        },
        sequence: {
          diagramMarginX: 50,
          diagramMarginY: 10,
          actorMargin: 50,
          width: 150,
          height: 65,
          boxMargin: 10,
          boxTextMargin: 5,
          noteMargin: 10,
          messageMargin: 35
        },
        gantt: {
          numberSectionStyles: 4,
          axisFormat: '%m/%d/%Y',
          topAxis: true
        }
      });
      setIsInitialized(true);
    }
  }, [isInitialized]);

  // Render the Mermaid diagram
  useEffect(() => {
    if (!isInitialized || !mermaidRef.current) return;
    
    const renderDiagram = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Clear previous content
        if (mermaidRef.current) {
          mermaidRef.current.innerHTML = '';
        }

        // Validate and render the diagram
        const isValid = await mermaid.parse(chart);
        
        if (!isValid) {
          throw new Error('Invalid Mermaid syntax');
        }

        const elementId = `mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        const { svg } = await mermaid.render(elementId, chart);
        
        if (mermaidRef.current) {
          mermaidRef.current.innerHTML = svg;
          
          // Apply zoom transformation only
          const svgElement = mermaidRef.current.querySelector('svg');
          if (svgElement) {
            svgElement.style.transform = `scale(${zoom})`;
            svgElement.style.transformOrigin = 'top left';
            svgElement.style.transition = 'transform 0.2s ease-in-out';
          }
        }
      } catch (err) {
        console.error('Mermaid rendering error:', err);
        setError(err instanceof Error ? err.message : 'Failed to render diagram');
      } finally {
        setIsLoading(false);
      }
    };

    renderDiagram();
  }, [chart, isInitialized, zoom]);

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(chart);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy diagram code:', err);
    }
  };

  const handleOpenInEditor = () => {
    try {
      const vscodeApi = (window as any).vscode || (window as any).__vscodeApi;
      if (vscodeApi) {
        vscodeApi.postMessage({
          type: 'openMermaidInEditor',
          content: chart,
          filename: `mermaid-diagram-${Date.now()}.mmd`
        });
      } else {
        console.warn('VS Code API not available');
      }
    } catch (err) {
      console.error('Failed to open in editor:', err);
    }
  };


  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.1, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.1, 0.3));
  };

  const handleResetZoom = () => {
    setZoom(1);
  };


  return (
    <div className="mermaid-diagram-container">
      {/* Diagram Header */}
      <div className="code-block-header">
        <div className="code-block-info">
          <div className="language-badge">
            Mermaid Diagram
          </div>
          <div className="code-stats">
            {chart.split('\n').length} line{chart.split('\n').length !== 1 ? 's' : ''}
          </div>
        </div>
        
        <div className="code-block-actions">
          {/* Zoom Controls */}
          <button
            onClick={handleZoomOut}
            className="code-action-button"
            title="Zoom out"
            disabled={zoom <= 0.3}
          >
            <ZoomOut size={14} />
          </button>
          
          <span className="zoom-indicator">
            {Math.round(zoom * 100)}%
          </span>
          
          <button
            onClick={handleZoomIn}
            className="code-action-button"
            title="Zoom in"
            disabled={zoom >= 3}
          >
            <ZoomIn size={14} />
          </button>
          
          <button
            onClick={handleResetZoom}
            className="code-action-button"
            title="Reset zoom"
          >
            <RotateCcw size={14} />
          </button>
          
          {/* Copy Button */}
          <button
            onClick={handleCopyCode}
            className="code-action-button"
            title="Copy diagram code"
          >
            {copied ? <Check size={14} /> : <Copy size={14} />}
          </button>
          
          {/* Open in Editor Button */}
          <button
            onClick={handleOpenInEditor}
            className="code-action-button"
            title="Open in VS Code editor"
          >
            <FileText size={14} />
          </button>
          
        </div>
      </div>

      {/* Diagram Content */}
      <div 
        ref={containerRef}
        className="mermaid-diagram-content"
      >
        {/* Always render the mermaid ref div */}
        <div 
          ref={mermaidRef}
          className="mermaid-output"
        />
        
        {/* Loading overlay */}
        {isLoading && (
          <div className="mermaid-loading" style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, backgroundColor: 'var(--vscode-editor-background)' }}>
            <div className="loading-spinner"></div>
            <span>Rendering diagram...</span>
          </div>
        )}
        
        {/* Error overlay */}
        {error && (
          <div className="mermaid-error" style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}>
            <div className="error-icon">⚠️</div>
            <div>
              <strong>Failed to render Mermaid diagram</strong>
              <p>{error}</p>
              <details>
                <summary>Show diagram source</summary>
                <pre className="error-source">{chart}</pre>
              </details>
            </div>
          </div>
        )}
      </div>

    </div>
  );
};

export default MermaidDiagram;